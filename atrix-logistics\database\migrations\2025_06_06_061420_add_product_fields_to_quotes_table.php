<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Add quote type to distinguish between shipping and product quotes
            $table->enum('quote_type', ['shipping', 'product'])->default('shipping')->after('quote_number');
            
            // Product-related fields
            $table->json('products')->nullable()->after('metadata'); // Array of products with quantities
            $table->decimal('products_total', 10, 2)->nullable()->after('products'); // Total value of products
            $table->text('product_requirements')->nullable()->after('products_total'); // Special requirements for products
            
            // Add index for quote type
            $table->index('quote_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            $table->dropIndex(['quote_type']);
            $table->dropColumn(['quote_type', 'products', 'products_total', 'product_requirements']);
        });
    }
};
