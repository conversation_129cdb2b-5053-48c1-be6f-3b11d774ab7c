<?php

/**
 * Copyright (c) 2013-2024 <PERSON><PERSON><PERSON>
 *
 * For the full copyright and license information, please view
 * the LICENSE.md file that was distributed with this source code.
 *
 * @see https://github.com/nicmart/Tree
 */

namespace Tree\Visitor;

use Tree\Node\NodeInterface;

/**
 * Visitor interface for Nodes.
 *
 * <AUTHOR> <<EMAIL>>
 */
interface Visitor
{
    public function visit(NodeInterface $node): mixed;
}
