@extends('layouts.admin')

@section('title', 'Customer Management')
@section('page-title', 'Customer Management')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.analytics.customers') }}" class="btn btn-outline-info">
            <i class="fas fa-chart-line me-1"></i> Analytics
        </a>
        <a href="{{ route('admin.customers.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Customer
        </a>
        <button type="button" class="btn btn-outline-secondary" onclick="exportCustomers()">
            <i class="fas fa-download me-1"></i> Export
        </button>
    </div>
@endsection

@section('content')
    <!-- Customer Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['total_customers']) }}</h4>
                            <p class="mb-0">Total Customers</p>
                        </div>
                        <div>
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['active_customers']) }}</h4>
                            <p class="mb-0">Active Customers</p>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['customers_with_orders']) }}</h4>
                            <p class="mb-0">With Orders</p>
                        </div>
                        <div>
                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($stats['new_this_month']) }}</h4>
                            <p class="mb-0">New This Month</p>
                        </div>
                        <div>
                            <i class="fas fa-user-plus fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('admin.customers.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Name, email, phone...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="has_orders" class="form-label">Orders</label>
                    <select class="form-select" id="has_orders" name="has_orders">
                        <option value="">All Customers</option>
                        <option value="yes" {{ request('has_orders') === 'yes' ? 'selected' : '' }}>With Orders</option>
                        <option value="no" {{ request('has_orders') === 'no' ? 'selected' : '' }}>Without Orders</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="state" class="form-label">State</label>
                    <select class="form-select" id="state" name="state">
                        <option value="">All States</option>
                        @foreach($states as $state)
                            <option value="{{ $state }}" {{ request('state') === $state ? 'selected' : '' }}>
                                {{ $state }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">From Date</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Customers Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>
                Customers ({{ $customers->total() }})
            </h5>
            
            @if($customers->count() > 0)
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                            data-bs-toggle="dropdown">
                        Bulk Actions
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('activate')">
                            <i class="fas fa-check me-2"></i> Activate Selected
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="bulkAction('deactivate')">
                            <i class="fas fa-ban me-2"></i> Deactivate Selected
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="bulkAction('delete')">
                            <i class="fas fa-trash me-2"></i> Delete Selected
                        </a></li>
                    </ul>
                </div>
            @endif
        </div>
        
        <div class="card-body">
            @if($customers->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th>Customer</th>
                                <th>Contact</th>
                                <th>Location</th>
                                <th>Orders</th>
                                <th>Total Spent</th>
                                <th>Status</th>
                                <th>Joined</th>
                                <th width="120">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($customers as $customer)
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input customer-checkbox" 
                                               value="{{ $customer->id }}">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                {{ strtoupper(substr($customer->name, 0, 2)) }}
                                            </div>
                                            <div>
                                                <strong>{{ $customer->name }}</strong>
                                                <br><small class="text-muted">ID: {{ $customer->id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <a href="mailto:{{ $customer->email }}">{{ $customer->email }}</a>
                                            @if($customer->phone)
                                                <br><a href="tel:{{ $customer->phone }}">{{ $customer->phone }}</a>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        @if($customer->city || $customer->state)
                                            {{ $customer->city }}{{ $customer->city && $customer->state ? ', ' : '' }}{{ $customer->state }}
                                            @if($customer->country)
                                                <br><small class="text-muted">{{ $customer->country }}</small>
                                            @endif
                                        @else
                                            <span class="text-muted">Not provided</span>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $customer->orders_count }}</span>
                                        @if($customer->support_tickets_count > 0)
                                            <br><small class="text-muted">{{ $customer->support_tickets_count }} tickets</small>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>${{ number_format($customer->total_spent ?? 0, 2) }}</strong>
                                        @if($customer->wishlist_count > 0)
                                            <br><small class="text-muted">{{ $customer->wishlist_count }} wishlist</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $customer->is_active ? 'success' : 'danger' }}">
                                            {{ $customer->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ $customer->created_at->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $customer->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('admin.customers.show', $customer) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('admin.customers.edit', $customer) }}" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-{{ $customer->is_active ? 'warning' : 'success' }}" 
                                                    onclick="toggleStatus({{ $customer->id }})" 
                                                    title="{{ $customer->is_active ? 'Deactivate' : 'Activate' }}">
                                                <i class="fas fa-{{ $customer->is_active ? 'ban' : 'check' }}"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-3">
                    {{ $customers->links('pagination.admin') }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No customers found</h5>
                    <p class="text-muted">Start by adding your first customer.</p>
                    <a href="{{ route('admin.customers.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Add First Customer
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('styles')
<style>
    .avatar-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 14px;
    }
</style>
@endpush

@push('scripts')
<script>
    // Select all functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.customer-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Bulk actions
    function bulkAction(action) {
        const selectedCustomers = Array.from(document.querySelectorAll('.customer-checkbox:checked'))
                                       .map(cb => cb.value);
        
        if (selectedCustomers.length === 0) {
            alert('Please select at least one customer.');
            return;
        }

        const actionText = action === 'delete' ? 'delete' : action;
        if (confirm(`Are you sure you want to ${actionText} ${selectedCustomers.length} customer(s)?`)) {
            fetch('{{ route("admin.customers.bulk-action") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    customer_ids: selectedCustomers,
                    action: action
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred. Please try again.');
            });
        }
    }

    // Toggle customer status
    function toggleStatus(customerId) {
        fetch(`/admin/customers/${customerId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }

    // Export customers
    function exportCustomers() {
        alert('Export functionality coming soon!');
    }
</script>
@endpush
