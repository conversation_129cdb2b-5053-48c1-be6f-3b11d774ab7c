<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('referral_source')->nullable()->after('role');
            $table->string('referral_code')->nullable()->after('referral_source');
            $table->text('marketing_preferences')->nullable()->after('referral_code');
            $table->timestamp('last_login_at')->nullable()->after('marketing_preferences');
            $table->string('timezone')->nullable()->after('last_login_at');
            $table->date('date_of_birth')->nullable()->after('timezone');
            $table->enum('gender', ['male', 'female', 'other', 'prefer_not_to_say'])->nullable()->after('date_of_birth');

            // Add indexes for better performance
            $table->index(['referral_source']);
            $table->index(['last_login_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['referral_source']);
            $table->dropIndex(['last_login_at']);
            $table->dropColumn([
                'referral_source',
                'referral_code',
                'marketing_preferences',
                'last_login_at',
                'timezone',
                'date_of_birth',
                'gender'
            ]);
        });
    }
};
