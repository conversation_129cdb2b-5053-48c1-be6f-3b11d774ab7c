<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'parcel_id',
        'order_id',
        'user_id',
        'amount',
        'currency',
        'payment_method',
        'transaction_id',
        'gateway_response',
        'status',
        'processed_at',
        'admin_notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'gateway_response' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the parcel that this payment is for
     */
    public function parcel(): BelongsTo
    {
        return $this->belongsTo(Parcel::class);
    }

    /**
     * Get the order that this payment is for
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user who made this payment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted amount with currency
     */
    public function getFormattedAmountAttribute(): string
    {
        return \App\Helpers\CurrencyHelper::format($this->amount);
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'completed' => 'success',
            'pending' => 'warning',
            'pending_approval' => 'info',
            'failed' => 'danger',
            'refunded' => 'secondary',
            'cancelled' => 'dark',
            default => 'secondary'
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatusAttribute(): string
    {
        return match($this->status) {
            'completed' => 'Completed',
            'pending' => 'Pending',
            'pending_approval' => 'Pending Approval',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
            'cancelled' => 'Cancelled',
            default => ucfirst($this->status)
        };
    }

    /**
     * Get payment method display name
     */
    public function getPaymentMethodNameAttribute(): string
    {
        return match($this->payment_method) {
            'stripe' => 'Credit/Debit Card (Stripe)',
            'paypal' => 'PayPal',
            'razorpay' => 'Razorpay',
            'square' => 'Square',
            'admin_approval' => 'Bank Transfer / Manual',
            default => ucfirst(str_replace('_', ' ', $this->payment_method))
        };
    }

    /**
     * Check if payment is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return in_array($this->status, ['pending', 'pending_approval']);
    }

    /**
     * Check if payment failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if payment can be refunded
     */
    public function canBeRefunded(): bool
    {
        return $this->status === 'completed' && 
               $this->payment_method !== 'admin_approval' &&
               $this->created_at->diffInDays(now()) <= 30; // 30 day refund window
    }

    /**
     * Scope for completed payments
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for pending payments
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['pending', 'pending_approval']);
    }

    /**
     * Scope for failed payments
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for payments by method
     */
    public function scopeByMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Get gateway transaction URL (if available)
     */
    public function getGatewayUrlAttribute(): ?string
    {
        if (!$this->transaction_id) {
            return null;
        }

        return match($this->payment_method) {
            'stripe' => "https://dashboard.stripe.com/payments/{$this->transaction_id}",
            'paypal' => "https://www.paypal.com/activity/payment/{$this->transaction_id}",
            'square' => "https://squareup.com/dashboard/sales/transactions/{$this->transaction_id}",
            default => null
        };
    }

    /**
     * Get safe gateway response (removes sensitive data)
     */
    public function getSafeGatewayResponseAttribute(): array
    {
        $response = $this->gateway_response ?? [];
        
        // Remove sensitive fields that might be in the response
        $sensitiveFields = [
            'card_number', 'cvv', 'cvc', 'card_token', 
            'bank_account', 'routing_number', 'account_number'
        ];
        
        foreach ($sensitiveFields as $field) {
            unset($response[$field]);
        }
        
        return $response;
    }
}
