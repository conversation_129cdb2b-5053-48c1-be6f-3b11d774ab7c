<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SiteSetting extends Model
{
    protected $fillable = [
        'key_name',
        'value',
        'type',
        'group_name',
        'label',
        'description',
        'is_public',
    ];

    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get a setting value by key
     */
    public static function getValue(string $key, $default = null)
    {
        // In testing environment, don't cache and handle database errors gracefully
        if (app()->environment('testing')) {
            try {
                $setting = static::where('key_name', $key)->first();
                if (!$setting) {
                    return $default;
                }
                return static::castValue($setting->value, $setting->type);
            } catch (\Exception $e) {
                return $default;
            }
        }

        $cacheKey = "site_setting_{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            try {
                $setting = static::where('key_name', $key)->first();

                if (!$setting) {
                    return $default;
                }

                return static::castValue($setting->value, $setting->type);
            } catch (\Exception $e) {
                return $default;
            }
        });
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, string $type = 'string'): void
    {
        $setting = static::updateOrCreate(
            ['key_name' => $key],
            [
                'value' => $value,
                'type' => $type,
            ]
        );

        // Clear cache
        Cache::forget("site_setting_{$key}");
    }

    /**
     * Get all public settings
     */
    public static function getPublicSettings(): array
    {
        return Cache::remember('public_site_settings', 3600, function () {
            $settings = static::where('is_public', true)->get();

            $result = [];
            foreach ($settings as $setting) {
                $result[$setting->key_name] = static::castValue($setting->value, $setting->type);
            }

            return $result;
        });
    }

    /**
     * Get settings by group
     */
    public static function getByGroup(string $group): array
    {
        $settings = static::where('group_name', $group)->get();

        $result = [];
        foreach ($settings as $setting) {
            $result[$setting->key_name] = static::castValue($setting->value, $setting->type);
        }

        return $result;
    }

    /**
     * Cast value to appropriate type
     */
    protected static function castValue($value, string $type)
    {
        return match($type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'json' => json_decode($value, true),
            'image' => $value,
            default => $value,
        };
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        Cache::forget('public_site_settings');
        Cache::forget('global_site_settings'); // Clear global cache too

        // Clear individual setting caches
        $settings = static::all();
        foreach ($settings as $setting) {
            Cache::forget("site_setting_{$setting->key_name}");
        }
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function () {
            static::clearCache();
        });

        static::deleted(function () {
            static::clearCache();
        });
    }
}
