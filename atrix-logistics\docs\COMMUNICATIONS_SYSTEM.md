# Communications System Documentation

## Overview

The Communications System provides comprehensive contact form management and newsletter subscription functionality for the Atrix Logistics platform. This system allows administrators to manage contact form submissions and newsletter subscribers from a centralized admin dashboard.

## Features

### Contact Form Management
- ✅ **Contact Form Submissions**: Capture and store contact form submissions from the frontend
- ✅ **Status Management**: Track contact status (new, read, replied, closed)
- ✅ **Email Notifications**: Automatic email notifications to admin when forms are submitted
- ✅ **Admin Notes**: Add internal notes to contact submissions
- ✅ **Bulk Actions**: Perform bulk operations on multiple contacts
- ✅ **Search & Filter**: Search and filter contacts by status, name, email, etc.

### Newsletter Management
- ✅ **Subscriber Management**: Add, edit, and remove newsletter subscribers
- ✅ **Status Tracking**: Track subscriber status (active, unsubscribed, bounced)
- ✅ **Bulk Import**: Import multiple subscribers at once
- ✅ **Unsubscribe Links**: Automatic unsubscribe token generation
- ✅ **Source Tracking**: Track subscription source (website, admin, import, etc.)

## Admin Dashboard Integration

### Navigation Menu
The Communications section is added to the admin sidebar with:
- **Contact Messages** - View and manage contact form submissions
- **Newsletter Subscribers** - Manage newsletter subscriber list
- **Badge Notifications** - Shows count of new/unread messages

### Dashboard Statistics
The main dashboard now displays:
- Total contact messages
- New/unread messages count
- Total newsletter subscribers
- Active subscribers count

## Database Schema

### Contacts Table
```sql
- id (primary key)
- name (string)
- email (string)
- phone (nullable string)
- subject (nullable string)
- message (text)
- status (enum: new, read, replied, closed)
- admin_notes (nullable text)
- read_at (nullable timestamp)
- replied_at (nullable timestamp)
- replied_by (foreign key to users)
- ip_address (nullable string)
- user_agent (nullable string)
- created_at, updated_at
```

### Newsletter Subscribers Table
```sql
- id (primary key)
- email (unique string)
- name (nullable string)
- status (enum: active, unsubscribed, bounced)
- subscribed_at (timestamp)
- unsubscribed_at (nullable timestamp)
- subscription_source (string: website, admin, import, contact_form)
- ip_address (nullable string)
- user_agent (nullable string)
- unsubscribe_token (unique string)
- created_at, updated_at
```

## Frontend Integration

### Contact Form
- Enhanced contact form with newsletter subscription option
- Automatic newsletter subscription when checkbox is selected
- Form validation and error handling
- Success messages with subscription confirmation

### Newsletter Subscription
- Standalone newsletter subscription endpoint
- AJAX-compatible for dynamic subscription
- Automatic resubscription for previously unsubscribed users

### Unsubscribe Process
- Secure token-based unsubscribe links
- User-friendly unsubscribe confirmation page
- Automatic status updates

## Email Configuration

### Site Settings
New email settings added to site configuration:
- **Notification Email**: Email address to receive contact form notifications
- **Contact Email**: Main contact email displayed on website

### Email Templates
- **Contact Notification**: HTML email template for admin notifications
- Includes contact details, message content, and quick reply links
- Professional styling with company branding

## API Endpoints

### Frontend Routes
```
POST /contact - Submit contact form
POST /newsletter/subscribe - Subscribe to newsletter
GET /newsletter/unsubscribe/{token} - Unsubscribe from newsletter
```

### Admin Routes
```
GET /admin/communications/contacts - List contacts
GET /admin/communications/contacts/{contact} - View contact
PUT /admin/communications/contacts/{contact} - Update contact
DELETE /admin/communications/contacts/{contact} - Delete contact
POST /admin/communications/contacts/bulk-action - Bulk actions

GET /admin/communications/newsletter - List subscribers
POST /admin/communications/newsletter - Create subscriber
GET /admin/communications/newsletter/create - Create form
GET /admin/communications/newsletter/{subscriber} - View subscriber
PUT /admin/communications/newsletter/{subscriber} - Update subscriber
DELETE /admin/communications/newsletter/{subscriber} - Delete subscriber
POST /admin/communications/newsletter/import - Bulk import
POST /admin/communications/newsletter/bulk-action - Bulk actions
```

## Usage Examples

### Contact Form Submission
```html
<form method="POST" action="{{ route('contact.submit') }}">
    @csrf
    <input type="text" name="name" required>
    <input type="email" name="email" required>
    <input type="tel" name="phone">
    <input type="text" name="subject">
    <textarea name="message" required></textarea>
    <input type="checkbox" name="subscribe_newsletter" value="1">
    <button type="submit">Send Message</button>
</form>
```

### Newsletter Subscription (AJAX)
```javascript
fetch('/newsletter/subscribe', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
    },
    body: JSON.stringify({
        email: '<EMAIL>',
        name: 'John Doe'
    })
})
.then(response => response.json())
.then(data => console.log(data.message));
```

## Security Features

### Data Protection
- CSRF protection on all forms
- Input validation and sanitization
- XSS protection through Laravel's built-in escaping
- SQL injection prevention through Eloquent ORM

### Privacy Compliance
- IP address and user agent tracking for security
- Secure unsubscribe tokens
- Data retention policies can be implemented
- GDPR-compliant data handling

## Monitoring & Analytics

### Admin Dashboard
- Real-time statistics on dashboard
- Contact status distribution
- Subscriber growth tracking
- Response time monitoring

### Logging
- All contact submissions logged
- Email sending attempts logged
- Error handling and logging
- Performance monitoring

## Customization Options

### Email Templates
- Customizable email notification templates
- Company branding integration
- Multi-language support ready

### Form Fields
- Easily extendable contact form fields
- Custom validation rules
- Additional subscriber metadata

### Status Workflows
- Customizable contact status workflow
- Automated status transitions
- Integration with external CRM systems

## Troubleshooting

### Common Issues
1. **Email not sending**: Check mail configuration in `.env`
2. **Permissions**: Ensure proper file permissions for uploads
3. **Database**: Run migrations if tables don't exist
4. **Cache**: Clear cache after configuration changes

### Debug Commands
```bash
php artisan config:clear
php artisan cache:clear
php artisan queue:work  # For email queue processing
```

## Future Enhancements

### Planned Features
- Email campaign management
- Newsletter templates
- Subscriber segmentation
- Analytics dashboard
- Integration with email marketing services
- Automated email responses
- Contact form builder
- Multi-language support
