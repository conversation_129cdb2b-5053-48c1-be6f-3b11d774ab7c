<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tracking_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parcel_id')->constrained('parcels')->cascadeOnDelete();
            $table->enum('status', ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'returned']);
            $table->string('location')->nullable();
            $table->text('description');
            $table->timestamp('event_date');
            $table->boolean('is_public')->default(true); // Whether to show to customers
            $table->json('metadata')->nullable(); // Additional event data
            $table->timestamps();

            // Indexes
            $table->index(['parcel_id', 'event_date']);
            $table->index(['parcel_id', 'is_public']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tracking_events');
    }
};
