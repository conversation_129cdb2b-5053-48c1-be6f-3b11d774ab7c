@extends('layouts.admin')

@section('title', 'Product Details')
@section('page-title', $product->name)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
        <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Product
        </a>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li>
                    <form method="POST" action="{{ route('admin.products.toggle-status', $product) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="dropdown-item">
                            @if($product->is_active)
                                <i class="fas fa-eye-slash me-2"></i> Deactivate
                            @else
                                <i class="fas fa-eye me-2"></i> Activate
                            @endif
                        </button>
                    </form>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <button type="button" class="dropdown-item text-danger" onclick="deleteProduct()">
                        <i class="fas fa-trash me-2"></i> Delete Product
                    </button>
                </li>
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Product Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Product Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Product Name</label>
                                <p class="mb-0">{{ $product->name }}</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">SKU</label>
                                    <p class="mb-0">
                                        <code>{{ $product->sku }}</code>
                                        <a href="#" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $product->sku }}')">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">URL Slug</label>
                                    <p class="mb-0">
                                        <code>{{ $product->slug }}</code>
                                        <a href="#" class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $product->slug }}')">
                                            <i class="fas fa-copy"></i>
                                        </a>
                                    </p>
                                </div>
                            </div>

                            @if($product->short_description)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Short Description</label>
                                    <p class="mb-0">{{ $product->short_description }}</p>
                                </div>
                            @endif

                            @if($product->description)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Description</label>
                                    <div class="border rounded p-3">
                                        {!! nl2br(e($product->description)) !!}
                                    </div>
                                </div>
                            @endif

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Category</label>
                                    <p class="mb-0">
                                        @if($product->category)
                                            <a href="{{ route('admin.categories.show', $product->category) }}" class="text-decoration-none">
                                                <i class="fas fa-folder me-2"></i>{{ $product->category->name }}
                                            </a>
                                        @else
                                            <span class="text-muted">No Category</span>
                                        @endif
                                    </p>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label fw-bold">Created</label>
                                    <p class="mb-0">{{ $product->created_at->format('M d, Y h:i A') }}</p>
                                    <small class="text-muted">{{ $product->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            @if($product->featured_image)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Featured Image</label>
                                    <div>
                                        <img src="{{ Storage::url($product->featured_image) }}" 
                                             alt="{{ $product->name }}" 
                                             class="img-thumbnail w-100" 
                                             style="max-height: 300px; object-fit: cover;">
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing & Inventory -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        Pricing & Inventory
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label fw-bold">Regular Price</label>
                            <p class="mb-0 h5">@currency($product->price)</p>
                        </div>
                        @if($product->sale_price)
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">Sale Price</label>
                                <p class="mb-0 h5 text-success">@currency($product->sale_price)</p>
                                @if($product->isOnSale())
                                    <small class="badge bg-danger">{{ $product->getDiscountPercentage() }}% OFF</small>
                                @endif
                            </div>
                        @endif
                        @if($product->cost_price)
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">Cost Price</label>
                                <p class="mb-0">@currency($product->cost_price)</p>
                                @if($product->cost_price > 0)
                                    <small class="text-muted">
                                        Profit: @currency($product->getCurrentPrice() - $product->cost_price)
                                        ({{ number_format((($product->getCurrentPrice() - $product->cost_price) / $product->cost_price) * 100, 1) }}%)
                                    </small>
                                @endif
                            </div>
                        @endif
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Stock Status</label>
                            <div>
                                @if($product->manage_stock)
                                    @if($product->isInStock())
                                        <span class="badge bg-success">In Stock ({{ $product->stock_quantity }})</span>
                                    @elseif($product->isLowStock())
                                        <span class="badge bg-warning">Low Stock ({{ $product->stock_quantity }})</span>
                                    @else
                                        <span class="badge bg-danger">Out of Stock</span>
                                    @endif
                                @else
                                    <span class="badge bg-info">{{ ucwords(str_replace('_', ' ', $product->stock_status)) }}</span>
                                @endif
                            </div>
                        </div>
                        @if($product->manage_stock && $product->min_stock_level)
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Low Stock Alert</label>
                                <p class="mb-0">{{ $product->min_stock_level }} units</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Gallery Images -->
            @if($product->gallery_images && count($product->gallery_images) > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>
                            Product Gallery
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($product->gallery_images as $image)
                                <div class="col-md-3 mb-3">
                                    <img src="{{ Storage::url($image) }}" 
                                         alt="{{ $product->name }} Gallery" 
                                         class="img-thumbnail w-100" 
                                         style="height: 150px; object-fit: cover; cursor: pointer;"
                                         onclick="showImageModal('{{ Storage::url($image) }}')">
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Status & Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Status & Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Status</label>
                        <div>
                            @if($product->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-secondary">Inactive</span>
                            @endif
                            
                            @if($product->is_featured)
                                <span class="badge bg-warning ms-2">Featured</span>
                            @endif

                            @if($product->is_digital)
                                <span class="badge bg-info ms-2">Digital</span>
                            @endif

                            @if($product->is_virtual)
                                <span class="badge bg-purple ms-2">Virtual</span>
                            @endif
                        </div>
                    </div>

                    @if($product->weight || $product->dimensions)
                        <div class="mb-3">
                            <label class="form-label fw-bold">Shipping Info</label>
                            @if($product->weight)
                                <p class="mb-1"><small>Weight: {{ $product->weight }} lbs</small></p>
                            @endif
                            @if($product->dimensions)
                                <p class="mb-0">
                                    <small>
                                        Dimensions: {{ $product->dimensions['length'] ?? 0 }}" × 
                                        {{ $product->dimensions['width'] ?? 0 }}" × 
                                        {{ $product->dimensions['height'] ?? 0 }}"
                                    </small>
                                </p>
                            @endif
                        </div>
                    @endif

                    <div class="mb-3">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="mb-0 small">{{ $product->updated_at->format('M d, Y h:i A') }}</p>
                        <small class="text-muted">{{ $product->updated_at->diffForHumans() }}</small>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.products.edit', $product) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Product
                        </a>
                        <a href="{{ route('products.show', $product->slug) }}" target="_blank" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>
                            Preview on Site
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="duplicateProduct({{ $product->id }}, '{{ $product->name }}')">
                            <i class="fas fa-copy me-2"></i>
                            Duplicate Product
                        </button>
                        <a href="{{ route('admin.products.analytics', $product) }}" class="btn btn-outline-warning">
                            <i class="fas fa-chart-line me-2"></i>
                            View Analytics
                        </a>
                    </div>
                </div>
            </div>

            <!-- SEO Information -->
            @if($product->meta_title || $product->meta_description || $product->meta_keywords)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            SEO Information
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($product->meta_title)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Title</label>
                                <p class="mb-0 small">{{ $product->meta_title }}</p>
                            </div>
                        @endif

                        @if($product->meta_description)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Description</label>
                                <p class="mb-0 small">{{ $product->meta_description }}</p>
                            </div>
                        @endif

                        @if($product->meta_keywords)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Keywords</label>
                                <p class="mb-0 small">{{ $product->meta_keywords }}</p>
                            </div>
                        @endif

                        @if($product->tags)
                            <div class="mb-3">
                                <label class="form-label fw-bold">Tags</label>
                                <div>
                                    @foreach($product->tags as $tag)
                                        <span class="badge bg-light text-dark me-1">{{ $tag }}</span>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Product
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the product <strong>{{ $product->name }}</strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.products.destroy', $product) }}" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Product Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="#" alt="Product Image" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function deleteProduct() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        });
    }

    function showImageModal(imageSrc) {
        document.getElementById('modalImage').src = imageSrc;
        new bootstrap.Modal(document.getElementById('imageModal')).show();
    }

    function duplicateProduct(productId, productName) {
        if (confirm(`Are you sure you want to duplicate "${productName}"?\n\nThis will create a copy of the product that you can then edit. The copy will be inactive by default.`)) {
            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/ecommerce/products/${productId}/duplicate`;

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add to body and submit
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endpush
