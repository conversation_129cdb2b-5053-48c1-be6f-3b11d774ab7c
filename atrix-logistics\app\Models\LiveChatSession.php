<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LiveChatSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'visitor_name',
        'visitor_email',
        'visitor_ip',
        'user_agent',
        'status',
        'assigned_to',
        'last_activity',
    ];

    protected $casts = [
        'last_activity' => 'datetime',
    ];

    /**
     * Get the staff member assigned to this session
     */
    public function assignedStaff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get all messages for this session
     */
    public function messages(): HasMany
    {
        return $this->hasMany(LiveChatMessage::class, 'session_id');
    }

    /**
     * Get unread messages count for staff
     */
    public function getUnreadMessagesCountAttribute(): int
    {
        return $this->messages()
            ->where('sender_type', 'visitor')
            ->where('is_read', false)
            ->count();
    }

    /**
     * Get the latest message
     */
    public function getLatestMessageAttribute(): ?LiveChatMessage
    {
        return $this->messages()->latest()->first();
    }

    /**
     * Mark all visitor messages as read
     */
    public function markVisitorMessagesAsRead(): void
    {
        $this->messages()
            ->where('sender_type', 'visitor')
            ->where('is_read', false)
            ->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
    }

    /**
     * Update last activity timestamp
     */
    public function updateActivity(): void
    {
        $this->update(['last_activity' => now()]);
    }

    /**
     * Scope for active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for waiting sessions
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * Scope for closed sessions
     */
    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    /**
     * Scope for sessions with unread messages
     */
    public function scopeWithUnreadMessages($query)
    {
        return $query->whereHas('messages', function ($q) {
            $q->where('sender_type', 'visitor')
              ->where('is_read', false);
        });
    }

    /**
     * Generate a unique session ID
     */
    public static function generateSessionId(): string
    {
        do {
            $sessionId = 'chat_' . uniqid() . '_' . random_int(1000, 9999);
        } while (self::where('session_id', $sessionId)->exists());

        return $sessionId;
    }
}
