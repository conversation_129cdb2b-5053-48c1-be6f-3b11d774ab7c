@extends('layouts.admin')

@section('page-title', 'Edit Quote - ' . $quote->quote_number)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.quotes.show', $quote) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Quote
        </a>
        <a href="{{ route('admin.quotes.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-list me-1"></i> All Quotes
        </a>
    </div>
@endsection

@section('content')
    <form action="{{ route('admin.quotes.update', $quote) }}" method="POST" id="editQuoteForm">
        @csrf
        @method('PUT')
        
        <!-- Quote Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-1">
                                    <i class="fas fa-edit me-2"></i>
                                    Edit Quote #{{ $quote->quote_number }}
                                </h4>
                                <p class="text-muted mb-0">
                                    Quote Type: <span class="badge bg-info">{{ ucwords($quote->quote_type) }}</span>
                                    @if($quote->quote_source)
                                        • Source: <span class="badge bg-secondary">{{ ucwords(str_replace('_', ' ', $quote->quote_source)) }}</span>
                                    @endif
                                </p>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{{ $quote->status_badge_color }} fs-6 mb-2">
                                    {{ $quote->formatted_status }}
                                </span>
                                <br>
                                <span class="badge bg-{{ $quote->priority_badge_color }}">
                                    {{ $quote->formatted_priority }} Priority
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Main Form Content -->
            <div class="col-lg-8">
                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Customer Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customer_name" class="form-label">Customer Name *</label>
                                    <input type="text" class="form-control @error('customer_name') is-invalid @enderror" 
                                           id="customer_name" name="customer_name" value="{{ old('customer_name', $quote->customer_name) }}" required>
                                    @error('customer_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="customer_email" class="form-label">Email Address *</label>
                                    <input type="email" class="form-control @error('customer_email') is-invalid @enderror" 
                                           id="customer_email" name="customer_email" value="{{ old('customer_email', $quote->customer_email) }}" required>
                                    @error('customer_email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="customer_phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control @error('customer_phone') is-invalid @enderror" 
                                           id="customer_phone" name="customer_phone" value="{{ old('customer_phone', $quote->customer_phone) }}">
                                    @error('customer_phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">Company Name</label>
                                    <input type="text" class="form-control @error('company_name') is-invalid @enderror" 
                                           id="company_name" name="company_name" value="{{ old('company_name', $quote->company_name) }}">
                                    @error('company_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        @if($quote->user)
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This quote is linked to customer account: <strong>{{ $quote->user->name }}</strong>
                            <a href="{{ route('admin.customers.show', $quote->user) }}" class="btn btn-sm btn-outline-primary ms-2">
                                <i class="fas fa-external-link-alt me-1"></i> View Profile
                            </a>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Service Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-truck me-2"></i>
                            Service Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="service_type" class="form-label">Service Type *</label>
                                    <select class="form-select @error('service_type') is-invalid @enderror" 
                                            id="service_type" name="service_type" required>
                                        <option value="">Select Service Type</option>
                                        @foreach($serviceTypes as $value => $label)
                                            <option value="{{ $value }}" {{ old('service_type', $quote->service_type) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                        <option value="product_inquiry" {{ old('service_type', $quote->service_type) == 'product_inquiry' ? 'selected' : '' }}>
                                            Product Inquiry
                                        </option>
                                    </select>
                                    @error('service_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="priority" class="form-label">Priority *</label>
                                    <select class="form-select @error('priority') is-invalid @enderror" 
                                            id="priority" name="priority" required>
                                        <option value="">Select Priority</option>
                                        @foreach($priorities as $value => $label)
                                            <option value="{{ $value }}" {{ old('priority', $quote->priority) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="delivery_speed" class="form-label">Delivery Speed *</label>
                                    <select class="form-select @error('delivery_speed') is-invalid @enderror" 
                                            id="delivery_speed" name="delivery_speed" required>
                                        <option value="standard" {{ old('delivery_speed', $quote->delivery_speed) == 'standard' ? 'selected' : '' }}>Standard</option>
                                        <option value="express" {{ old('delivery_speed', $quote->delivery_speed) == 'express' ? 'selected' : '' }}>Express</option>
                                        <option value="overnight" {{ old('delivery_speed', $quote->delivery_speed) == 'overnight' ? 'selected' : '' }}>Overnight</option>
                                        <option value="same_day" {{ old('delivery_speed', $quote->delivery_speed) == 'same_day' ? 'selected' : '' }}>Same Day</option>
                                    </select>
                                    @error('delivery_speed')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="assigned_to" class="form-label">Assigned To</label>
                                    <select class="form-select @error('assigned_to') is-invalid @enderror" 
                                            id="assigned_to" name="assigned_to">
                                        <option value="">Unassigned</option>
                                        @foreach($admins as $admin)
                                            <option value="{{ $admin->id }}" {{ old('assigned_to', $quote->assigned_to) == $admin->id ? 'selected' : '' }}>
                                                {{ $admin->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="4" 
                                      placeholder="Describe the shipping requirements...">{{ old('description', $quote->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <!-- Special Services -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="insurance_required" 
                                           name="insurance_required" value="1" {{ old('insurance_required', $quote->insurance_required) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="insurance_required">
                                        Insurance Required
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="signature_required" 
                                           name="signature_required" value="1" {{ old('signature_required', $quote->signature_required) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="signature_required">
                                        Signature Required
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="fragile" 
                                           name="fragile" value="1" {{ old('fragile', $quote->fragile) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="fragile">
                                        Fragile Items
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="hazardous" 
                                           name="hazardous" value="1" {{ old('hazardous', $quote->hazardous) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="hazardous">
                                        Hazardous Materials
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Details (only for shipping quotes) -->
                @if($quote->isShippingQuote())
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Shipping Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-arrow-up me-1"></i>
                                    Origin
                                </h6>

                                <div class="mb-3">
                                    <label for="origin_address" class="form-label">Address *</label>
                                    <input type="text" class="form-control @error('origin_address') is-invalid @enderror"
                                           id="origin_address" name="origin_address" value="{{ old('origin_address', $quote->origin_address) }}" required>
                                    @error('origin_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="origin_city" class="form-label">City *</label>
                                            <input type="text" class="form-control @error('origin_city') is-invalid @enderror"
                                                   id="origin_city" name="origin_city" value="{{ old('origin_city', $quote->origin_city) }}" required>
                                            @error('origin_city')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="origin_state" class="form-label">State</label>
                                            <input type="text" class="form-control @error('origin_state') is-invalid @enderror"
                                                   id="origin_state" name="origin_state" value="{{ old('origin_state', $quote->origin_state) }}">
                                            @error('origin_state')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="origin_postal_code" class="form-label">Postal Code</label>
                                            <input type="text" class="form-control @error('origin_postal_code') is-invalid @enderror"
                                                   id="origin_postal_code" name="origin_postal_code" value="{{ old('origin_postal_code', $quote->origin_postal_code) }}">
                                            @error('origin_postal_code')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="origin_country" class="form-label">Country *</label>
                                            <input type="text" class="form-control @error('origin_country') is-invalid @enderror"
                                                   id="origin_country" name="origin_country" value="{{ old('origin_country', $quote->origin_country) }}" required>
                                            @error('origin_country')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    Destination
                                </h6>

                                <div class="mb-3">
                                    <label for="destination_address" class="form-label">Address *</label>
                                    <input type="text" class="form-control @error('destination_address') is-invalid @enderror"
                                           id="destination_address" name="destination_address" value="{{ old('destination_address', $quote->destination_address) }}" required>
                                    @error('destination_address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="destination_city" class="form-label">City *</label>
                                            <input type="text" class="form-control @error('destination_city') is-invalid @enderror"
                                                   id="destination_city" name="destination_city" value="{{ old('destination_city', $quote->destination_city) }}" required>
                                            @error('destination_city')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="destination_state" class="form-label">State</label>
                                            <input type="text" class="form-control @error('destination_state') is-invalid @enderror"
                                                   id="destination_state" name="destination_state" value="{{ old('destination_state', $quote->destination_state) }}">
                                            @error('destination_state')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="destination_postal_code" class="form-label">Postal Code</label>
                                            <input type="text" class="form-control @error('destination_postal_code') is-invalid @enderror"
                                                   id="destination_postal_code" name="destination_postal_code" value="{{ old('destination_postal_code', $quote->destination_postal_code) }}">
                                            @error('destination_postal_code')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="destination_country" class="form-label">Country *</label>
                                            <input type="text" class="form-control @error('destination_country') is-invalid @enderror"
                                                   id="destination_country" name="destination_country" value="{{ old('destination_country', $quote->destination_country) }}" required>
                                            @error('destination_country')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Information (only for shipping quotes) -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            Package Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="package_count" class="form-label">Package Count *</label>
                                    <input type="number" class="form-control @error('package_count') is-invalid @enderror"
                                           id="package_count" name="package_count" value="{{ old('package_count', $quote->package_count) }}" min="1" required>
                                    @error('package_count')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="total_weight" class="form-label">Total Weight</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('total_weight') is-invalid @enderror"
                                               id="total_weight" name="total_weight" value="{{ old('total_weight', $quote->total_weight) }}" step="0.01" min="0">
                                        <select class="form-select @error('weight_unit') is-invalid @enderror"
                                                id="weight_unit" name="weight_unit" style="max-width: 80px;">
                                            <option value="kg" {{ old('weight_unit', $quote->weight_unit) == 'kg' ? 'selected' : '' }}>kg</option>
                                            <option value="lbs" {{ old('weight_unit', $quote->weight_unit) == 'lbs' ? 'selected' : '' }}>lbs</option>
                                        </select>
                                        @error('total_weight')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        @error('weight_unit')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="package_type" class="form-label">Package Type</label>
                                    <input type="text" class="form-control @error('package_type') is-invalid @enderror"
                                           id="package_type" name="package_type" value="{{ old('package_type', $quote->package_type) }}"
                                           placeholder="e.g., Box, Envelope, Pallet">
                                    @error('package_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="declared_value" class="form-label">Declared Value</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control @error('declared_value') is-invalid @enderror"
                                               id="declared_value" name="declared_value" value="{{ old('declared_value', $quote->declared_value) }}" step="0.01" min="0">
                                        @error('declared_value')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>

                                <!-- Dimensions -->
                                <div class="mb-3">
                                    <label class="form-label">Dimensions (L × W × H)</label>
                                    <div class="row">
                                        <div class="col-4">
                                            <input type="number" class="form-control" name="length"
                                                   value="{{ old('length', $quote->dimensions['length'] ?? '') }}"
                                                   placeholder="Length" step="0.01" min="0">
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control" name="width"
                                                   value="{{ old('width', $quote->dimensions['width'] ?? '') }}"
                                                   placeholder="Width" step="0.01" min="0">
                                        </div>
                                        <div class="col-4">
                                            <input type="number" class="form-control" name="height"
                                                   value="{{ old('height', $quote->dimensions['height'] ?? '') }}"
                                                   placeholder="Height" step="0.01" min="0">
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <select class="form-select" name="dimension_unit" style="max-width: 100px;">
                                            <option value="cm" {{ old('dimension_unit', $quote->dimensions['unit'] ?? 'cm') == 'cm' ? 'selected' : '' }}>cm</option>
                                            <option value="in" {{ old('dimension_unit', $quote->dimensions['unit'] ?? 'cm') == 'in' ? 'selected' : '' }}>in</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="package_description" class="form-label">Package Description</label>
                            <textarea class="form-control @error('package_description') is-invalid @enderror"
                                      id="package_description" name="package_description" rows="3"
                                      placeholder="Describe the contents of the package...">{{ old('package_description', $quote->package_description) }}</textarea>
                            @error('package_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Dates -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="preferred_pickup_date" class="form-label">Preferred Pickup Date</label>
                                    <input type="date" class="form-control @error('preferred_pickup_date') is-invalid @enderror"
                                           id="preferred_pickup_date" name="preferred_pickup_date"
                                           value="{{ old('preferred_pickup_date', $quote->preferred_pickup_date?->format('Y-m-d')) }}">
                                    @error('preferred_pickup_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="required_delivery_date" class="form-label">Required Delivery Date</label>
                                    <input type="date" class="form-control @error('required_delivery_date') is-invalid @enderror"
                                           id="required_delivery_date" name="required_delivery_date"
                                           value="{{ old('required_delivery_date', $quote->required_delivery_date?->format('Y-m-d')) }}">
                                    @error('required_delivery_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Product Information (for product quotes) -->
                @if($quote->isProductQuote() && $quote->products)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            Products ({{ $quote->getProductsCount() }} items)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Product details are managed separately. This quote contains products worth
                            <strong>${{ number_format($quote->products_total, 2) }}</strong>.
                        </div>

                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Quantity</th>
                                        <th>Unit Price</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($quote->getProductsWithDetails() as $item)
                                    <tr>
                                        <td>
                                            <strong>{{ $item['product']->name }}</strong>
                                            @if($item['notes'])
                                                <br><small class="text-muted">{{ $item['notes'] }}</small>
                                            @endif
                                        </td>
                                        <td>{{ $item['quantity'] }}</td>
                                        <td>${{ number_format($item['price_at_time'], 2) }}</td>
                                        <td>${{ number_format($item['total'], 2) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr class="table-active">
                                        <th colspan="3">Products Total:</th>
                                        <th>${{ number_format($quote->products_total, 2) }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Admin Notes -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            Admin Notes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">Internal Notes</label>
                            <textarea class="form-control @error('admin_notes') is-invalid @enderror"
                                      id="admin_notes" name="admin_notes" rows="4"
                                      placeholder="Add internal notes about this quote...">{{ old('admin_notes', $quote->admin_notes) }}</textarea>
                            @error('admin_notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        @if($quote->customer_notes)
                        <div class="mb-3">
                            <label class="form-label">Customer Notes</label>
                            <div class="form-control-plaintext bg-light p-2 rounded">
                                {{ $quote->customer_notes }}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Quote Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Quote Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td class="fw-bold">Quote Number:</td>
                                <td>{{ $quote->quote_number }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Current Status:</td>
                                <td>
                                    <span class="badge bg-{{ $quote->status_badge_color }}">{{ $quote->formatted_status }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Created:</td>
                                <td>{{ $quote->created_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            @if($quote->quoted_at)
                            <tr>
                                <td class="fw-bold">Quoted At:</td>
                                <td>{{ $quote->quoted_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            @endif
                            @if($quote->expires_at)
                            <tr>
                                <td class="fw-bold">Expires At:</td>
                                <td>{{ $quote->expires_at->format('M j, Y g:i A') }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Update Quote
                            </button>
                            <a href="{{ route('admin.quotes.show', $quote) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.getElementById('editQuoteForm');

    form.addEventListener('submit', function(e) {
        // Basic validation
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });

    // Date validation
    const pickupDate = document.getElementById('preferred_pickup_date');
    const deliveryDate = document.getElementById('required_delivery_date');

    if (pickupDate && deliveryDate) {
        pickupDate.addEventListener('change', function() {
            if (deliveryDate.value && this.value > deliveryDate.value) {
                alert('Pickup date cannot be after delivery date.');
                this.value = '';
            }
        });

        deliveryDate.addEventListener('change', function() {
            if (pickupDate.value && this.value < pickupDate.value) {
                alert('Delivery date cannot be before pickup date.');
                this.value = '';
            }
        });
    }
});
</script>
@endpush
