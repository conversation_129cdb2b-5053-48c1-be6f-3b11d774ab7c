@extends('layouts.admin')

@section('title', 'Create Product - Step 3')
@section('page-title', 'Create New Product - Pricing & Inventory')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.create.step2') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Step 2
        </a>
    </div>
@endsection

@section('content')
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="progress-steps">
                        <div class="step completed">
                            <div class="step-number"><i class="fas fa-check"></i></div>
                            <div class="step-title">Basic Info</div>
                        </div>
                        <div class="step completed">
                            <div class="step-number"><i class="fas fa-check"></i></div>
                            <div class="step-title">Content & SEO</div>
                        </div>
                        <div class="step active">
                            <div class="step-number">3</div>
                            <div class="step-title">Pricing & Inventory</div>
                        </div>
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-title">Images & Media</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.products.create.step3.store') }}">
                @csrf

                <!-- Pricing -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>
                            Pricing
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Regular Price <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">@currencySymbol</span>
                                    <input type="number" class="form-control @error('price') is-invalid @enderror"
                                           id="price" name="price" value="{{ old('price') }}"
                                           step="0.01" min="0" required>
                                </div>
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sale_price" class="form-label">Sale Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">@currencySymbol</span>
                                    <input type="number" class="form-control @error('sale_price') is-invalid @enderror"
                                           id="sale_price" name="sale_price" value="{{ old('sale_price') }}"
                                           step="0.01" min="0">
                                </div>
                                @error('sale_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Leave empty if not on sale</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="cost_price" class="form-label">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">@currencySymbol</span>
                                    <input type="number" class="form-control @error('cost_price') is-invalid @enderror"
                                           id="cost_price" name="cost_price" value="{{ old('cost_price') }}"
                                           step="0.01" min="0">
                                </div>
                                @error('cost_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">For profit calculations</small>
                            </div>
                        </div>

                        <!-- Sale Period -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date_on_sale_from" class="form-label">Sale Start Date</label>
                                <input type="datetime-local" class="form-control @error('date_on_sale_from') is-invalid @enderror"
                                       id="date_on_sale_from" name="date_on_sale_from" value="{{ old('date_on_sale_from') }}">
                                @error('date_on_sale_from')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date_on_sale_to" class="form-label">Sale End Date</label>
                                <input type="datetime-local" class="form-control @error('date_on_sale_to') is-invalid @enderror"
                                       id="date_on_sale_to" name="date_on_sale_to" value="{{ old('date_on_sale_to') }}">
                                @error('date_on_sale_to')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="hide_price" name="hide_price"
                                           value="1" {{ old('hide_price') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="hide_price">
                                        <strong>Hide Price from Customers</strong>
                                        <br><small class="text-muted">When enabled, customers will see "Request Quote" instead of the price</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Preview -->
                        <div class="alert alert-info" id="pricing-preview" style="display: none;">
                            <h6><i class="fas fa-calculator me-2"></i>Pricing Summary</h6>
                            <div id="pricing-details"></div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Management -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-warehouse me-2"></i>
                            Inventory Management
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="manage_stock" name="manage_stock" 
                                           value="1" {{ old('manage_stock', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="manage_stock">
                                        <strong>Track Stock Quantity</strong>
                                        <br><small class="text-muted">Enable inventory tracking for this product</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="stock_status" class="form-label">Stock Status <span class="text-danger">*</span></label>
                                <select class="form-select @error('stock_status') is-invalid @enderror" 
                                        id="stock_status" name="stock_status" required>
                                    <option value="in_stock" {{ old('stock_status', 'in_stock') == 'in_stock' ? 'selected' : '' }}>In Stock</option>
                                    <option value="out_of_stock" {{ old('stock_status') == 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                                    <option value="on_backorder" {{ old('stock_status') == 'on_backorder' ? 'selected' : '' }}>On Backorder</option>
                                </select>
                                @error('stock_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div id="stock-fields" class="row">
                            <div class="col-md-6 mb-3">
                                <label for="stock_quantity" class="form-label">Stock Quantity</label>
                                <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror" 
                                       id="stock_quantity" name="stock_quantity" value="{{ old('stock_quantity', 0) }}" 
                                       min="0" step="1">
                                @error('stock_quantity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="min_stock_level" class="form-label">Low Stock Alert Level</label>
                                <input type="number" class="form-control @error('min_stock_level') is-invalid @enderror" 
                                       id="min_stock_level" name="min_stock_level" value="{{ old('min_stock_level', 5) }}" 
                                       min="0" step="1">
                                @error('min_stock_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Alert when stock falls below this level</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping & Dimensions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shipping-fast me-2"></i>
                            Shipping & Dimensions
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="weight" class="form-label">Weight (lbs)</label>
                                <input type="number" class="form-control @error('weight') is-invalid @enderror" 
                                       id="weight" name="weight" value="{{ old('weight') }}" 
                                       step="0.01" min="0">
                                @error('weight')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="length" class="form-label">Length (in)</label>
                                <input type="number" class="form-control @error('length') is-invalid @enderror" 
                                       id="length" name="length" value="{{ old('length') }}" 
                                       step="0.01" min="0">
                                @error('length')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="width" class="form-label">Width (in)</label>
                                <input type="number" class="form-control @error('width') is-invalid @enderror" 
                                       id="width" name="width" value="{{ old('width') }}" 
                                       step="0.01" min="0">
                                @error('width')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="height" class="form-label">Height (in)</label>
                                <input type="number" class="form-control @error('height') is-invalid @enderror" 
                                       id="height" name="height" value="{{ old('height') }}" 
                                       step="0.01" min="0">
                                @error('height')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="shipping_class" class="form-label">Shipping Class</label>
                                <select class="form-select @error('shipping_class') is-invalid @enderror" 
                                        id="shipping_class" name="shipping_class">
                                    <option value="">Standard Shipping</option>
                                    <option value="heavy" {{ old('shipping_class') == 'heavy' ? 'selected' : '' }}>Heavy Items</option>
                                    <option value="fragile" {{ old('shipping_class') == 'fragile' ? 'selected' : '' }}>Fragile Items</option>
                                    <option value="oversized" {{ old('shipping_class') == 'oversized' ? 'selected' : '' }}>Oversized Items</option>
                                    <option value="express" {{ old('shipping_class') == 'express' ? 'selected' : '' }}>Express Shipping</option>
                                </select>
                                @error('shipping_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="tax_class" class="form-label">Tax Class</label>
                                <select class="form-select @error('tax_class') is-invalid @enderror" 
                                        id="tax_class" name="tax_class">
                                    <option value="">Standard Tax</option>
                                    <option value="reduced" {{ old('tax_class') == 'reduced' ? 'selected' : '' }}>Reduced Rate</option>
                                    <option value="zero" {{ old('tax_class') == 'zero' ? 'selected' : '' }}>Zero Rate</option>
                                    <option value="exempt" {{ old('tax_class') == 'exempt' ? 'selected' : '' }}>Tax Exempt</option>
                                </select>
                                @error('tax_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                    <a href="{{ route('admin.products.create.step2') }}" class="btn btn-outline-secondary" id="prevBtn">
                        <i class="fas fa-arrow-left me-1"></i> Previous: Content & SEO
                    </a>
                    <button type="submit" class="btn btn-primary" id="nextBtn">
                        <i class="fas fa-arrow-right me-1"></i> Next: Images & Media
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Progress Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Step 3 of 4
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Pricing & Inventory</h6>
                    <p class="text-muted mb-3">
                        Set up pricing, inventory tracking, and shipping details for your product.
                    </p>
                    
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 75%">75%</div>
                    </div>
                    
                    <h6>Current Product:</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        <strong>{{ $step1Data['name'] }}</strong><br>
                        <small class="text-muted">{{ $step1Data['short_description'] ?? 'No short description' }}</small>
                    </div>
                    
                    <h6>What's Next?</h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-1">
                            <i class="fas fa-arrow-right text-primary me-2"></i>
                            <small>Upload product images</small>
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Review and publish</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Pricing Calculator -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Pricing Calculator
                    </h5>
                </div>
                <div class="card-body">
                    <div id="profit-calculator">
                        <div class="mb-2">
                            <small class="text-muted">Regular Price:</small>
                            <span class="float-end" id="calc-price">$0.00</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Sale Price:</small>
                            <span class="float-end" id="calc-sale-price">-</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">Cost Price:</small>
                            <span class="float-end" id="calc-cost">$0.00</span>
                        </div>
                        <hr>
                        <div class="mb-2">
                            <strong>Profit Margin:</strong>
                            <span class="float-end" id="calc-margin">0%</span>
                        </div>
                        <div class="mb-2">
                            <strong>Profit Amount:</strong>
                            <span class="float-end" id="calc-profit">$0.00</span>
                        </div>
                        <div class="mb-2" id="discount-info" style="display: none;">
                            <strong class="text-success">Discount:</strong>
                            <span class="float-end text-success" id="calc-discount">0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory Tips -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Inventory Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Track Stock:</strong> Enable for physical products</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Low Stock Alert:</strong> Set appropriate warning levels</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Dimensions:</strong> Required for accurate shipping</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Sale Pricing:</strong> Use for promotions and discounts</small>
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Cost Price:</strong> Track profitability</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin: 0 20px;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .step.active .step-number {
        background-color: #0d6efd;
        color: white;
    }

    .step.completed .step-number {
        background-color: #198754;
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6c757d;
        text-align: center;
    }

    .step.active .step-title {
        color: #0d6efd;
    }

    .step.completed .step-title {
        color: #198754;
    }

    /* Loading button styles */
    .btn.loading {
        position: relative;
        pointer-events: none;
        opacity: 0.8;
    }

    .btn.loading .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush

@push('scripts')
<script>
    // Toggle stock fields based on manage_stock checkbox
    document.getElementById('manage_stock').addEventListener('change', function() {
        const stockFields = document.getElementById('stock-fields');
        const stockQuantity = document.getElementById('stock_quantity');
        const minStockLevel = document.getElementById('min_stock_level');
        
        if (this.checked) {
            stockFields.style.display = 'flex';
            stockQuantity.required = true;
        } else {
            stockFields.style.display = 'none';
            stockQuantity.required = false;
            stockQuantity.value = '';
            minStockLevel.value = '';
        }
    });

    // Pricing calculator
    function updatePricingCalculator() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const salePrice = parseFloat(document.getElementById('sale_price').value) || 0;
        const costPrice = parseFloat(document.getElementById('cost_price').value) || 0;
        
        // Update display values
        document.getElementById('calc-price').textContent = formatCurrency(price);
        document.getElementById('calc-cost').textContent = formatCurrency(costPrice);
        
        // Determine selling price (sale price if available, otherwise regular price)
        const sellingPrice = salePrice > 0 ? salePrice : price;
        
        if (salePrice > 0) {
            document.getElementById('calc-sale-price').textContent = '$' + salePrice.toFixed(2);
            const discount = ((price - salePrice) / price * 100);
            document.getElementById('calc-discount').textContent = discount.toFixed(1) + '%';
            document.getElementById('discount-info').style.display = 'block';
        } else {
            document.getElementById('calc-sale-price').textContent = '-';
            document.getElementById('discount-info').style.display = 'none';
        }
        
        // Calculate profit
        const profit = sellingPrice - costPrice;
        const margin = costPrice > 0 ? (profit / costPrice * 100) : 0;
        
        document.getElementById('calc-profit').textContent = '$' + profit.toFixed(2);
        document.getElementById('calc-margin').textContent = margin.toFixed(1) + '%';
        
        // Update profit color
        const profitElement = document.getElementById('calc-profit');
        const marginElement = document.getElementById('calc-margin');
        
        if (profit > 0) {
            profitElement.className = 'float-end text-success';
            marginElement.className = 'float-end text-success';
        } else if (profit < 0) {
            profitElement.className = 'float-end text-danger';
            marginElement.className = 'float-end text-danger';
        } else {
            profitElement.className = 'float-end';
            marginElement.className = 'float-end';
        }
    }

    // Add event listeners for pricing calculator
    ['price', 'sale_price', 'cost_price'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePricingCalculator);
    });

    // Validate sale price
    document.getElementById('sale_price').addEventListener('input', function() {
        const price = parseFloat(document.getElementById('price').value) || 0;
        const salePrice = parseFloat(this.value) || 0;
        
        if (salePrice > 0 && salePrice >= price) {
            this.setCustomValidity('Sale price must be less than regular price');
        } else {
            this.setCustomValidity('');
        }
    });

    // Initialize calculator
    updatePricingCalculator();

    // Initialize stock fields visibility
    document.getElementById('manage_stock').dispatchEvent(new Event('change'));

    // Add spinner to form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        const nextBtn = document.getElementById('nextBtn');
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.classList.add('loading');
            nextBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
        }
    });

    // Add spinner to previous button click
    document.getElementById('prevBtn').addEventListener('click', function(e) {
        this.classList.add('loading');
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    });
</script>
@endpush
