# Atrix Logistics - Admin User Manual

## Table of Contents
1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [User Management](#user-management)
4. [Content Management](#content-management)
5. [Blog Management](#blog-management)
6. [Product Management](#product-management)
7. [Order & Quote Management](#order--quote-management)
8. [Communications](#communications)
9. [Analytics & Reports](#analytics--reports)
10. [System Settings](#system-settings)
11. [SEO Management](#seo-management)
12. [Security Features](#security-features)

## Getting Started

### Accessing the Admin Panel
1. Navigate to `/admin/login` on your website
2. Enter your admin credentials
3. Click "Sign In" to access the dashboard

### Admin Roles
- **Admin**: Full access to all features
- **Staff**: Limited access based on permissions

## Dashboard Overview

The admin dashboard provides a comprehensive overview of your logistics business:

### Key Metrics
- **Total Orders**: Current month's order count
- **Revenue**: Monthly revenue tracking
- **Active Customers**: Number of registered customers
- **Pending Quotes**: Quotes awaiting response

### Quick Actions
- Create new orders
- Add products
- Manage customer inquiries
- View recent activities

### Notifications
- New customer registrations
- Pending quote approvals
- System alerts
- Low stock warnings

## User Management

### Customer Management
Navigate to **Users → Customers** to manage customer accounts.

#### Adding New Customers
1. Click "Add New Customer"
2. Fill in required information:
   - Name and contact details
   - Company information
   - Billing/shipping addresses
3. Set account status (Active/Inactive)
4. Save the customer profile

#### Editing Customer Information
1. Find the customer in the list
2. Click "Edit" or the customer name
3. Update necessary information
4. Save changes

#### Customer Features
- View order history
- Manage addresses
- Track shipments
- Download invoices

### Staff Management
Navigate to **Users → Staff** to manage admin users.

#### Creating Staff Accounts
1. Click "Add New Staff"
2. Enter user details
3. Assign role (Admin/Staff)
4. Set permissions
5. Send invitation email

## Content Management

### Slider Management
Navigate to **CMS → Sliders** to manage homepage sliders.

#### Adding New Slides
1. Click "Add New Slide"
2. Upload slide image (recommended: 1920x800px)
3. Add title, subtitle, and description
4. Set button text and URL
5. Configure display order
6. Activate the slide

### Page Content
Navigate to **CMS → Pages** to manage static pages.

#### Editing Pages
1. Select the page to edit
2. Update content using the rich text editor
3. Modify SEO settings
4. Save changes

## Blog Management

### Creating Blog Posts
Navigate to **Content → Blog Posts** to manage your blog.

#### Adding New Posts
1. Click "Create New Post"
2. Enter post title (slug auto-generates)
3. Write content using the rich text editor
4. Add excerpt for previews
5. Upload featured image
6. Set SEO meta tags
7. Add tags for categorization
8. Choose publication status:
   - **Draft**: Not visible to public
   - **Published**: Live on website
   - **Archived**: Hidden but preserved

#### SEO Optimization for Blog Posts
- **Title**: Keep under 60 characters
- **Meta Description**: 150-160 characters
- **Featured Image**: Optimize for web (under 500KB)
- **Tags**: Use relevant keywords
- **Internal Links**: Link to related content

### Blog Settings
- Configure RSS feed
- Set default author
- Manage categories
- Control comments (if enabled)

## Product Management

### Adding Products
Navigate to **Inventory → Products** to manage your product catalog.

#### Product Information
1. **Basic Details**:
   - Product name and SKU
   - Category assignment
   - Short and long descriptions
   - Technical specifications

2. **Pricing**:
   - Regular price
   - Sale price (optional)
   - Cost price (for profit tracking)
   - Hide price option for quote-only items

3. **Inventory**:
   - Stock quantity
   - Minimum stock level
   - Stock status
   - Manage stock toggle

4. **Images**:
   - Featured image
   - Gallery images
   - Image optimization tips

5. **SEO Settings**:
   - Meta title and description
   - Keywords
   - URL slug

### Product Categories
Navigate to **Inventory → Categories** to organize products.

#### Creating Categories
1. Click "Add Category"
2. Enter category name
3. Add description
4. Upload category image
5. Set parent category (for subcategories)
6. Configure SEO settings

## Order & Quote Management

### Processing Orders
Navigate to **Orders → All Orders** to manage customer orders.

#### Order Statuses
- **Pending**: Awaiting processing
- **Processing**: Being prepared
- **Shipped**: In transit
- **Delivered**: Completed
- **Cancelled**: Cancelled orders

#### Order Actions
1. **View Order**: See complete order details
2. **Update Status**: Change order status
3. **Add Notes**: Internal notes for staff
4. **Print Invoice**: Generate PDF invoice
5. **Send Updates**: Email customer updates

### Quote Management
Navigate to **Quotes → All Quotes** to manage quote requests.

#### Quote Workflow
1. **New Quote**: Customer submits request
2. **Review**: Staff reviews requirements
3. **Calculate**: Determine pricing
4. **Send Quote**: Email quote to customer
5. **Follow Up**: Track customer response

#### Quote Features
- Automatic quote numbering
- Expiration date tracking
- Conversion to orders
- Quote templates

## Communications

### Contact Management
Navigate to **Communications → Contacts** to manage customer inquiries.

#### Processing Contacts
1. **Review**: Read customer message
2. **Respond**: Reply via email
3. **Mark Resolved**: Close the inquiry
4. **Add Notes**: Internal documentation

### Newsletter Management
Navigate to **Communications → Newsletter** to manage subscribers.

#### Subscriber Management
- View all subscribers
- Export subscriber lists
- Manage unsubscribes
- Segment subscribers

### Live Chat
Navigate to **Communications → Live Chat** to manage chat sessions.

#### Chat Features
- Real-time messaging
- Session history
- Visitor information
- Chat transcripts

## Analytics & Reports

### Dashboard Analytics
View key performance indicators:
- Sales trends
- Customer growth
- Popular products
- Revenue metrics

### Custom Reports
Navigate to **Reports** to generate detailed reports:

#### Available Reports
1. **Sales Reports**: Revenue and order analysis
2. **Customer Reports**: Customer behavior insights
3. **Product Reports**: Inventory and performance
4. **Traffic Reports**: Website analytics

#### Generating Reports
1. Select report type
2. Choose date range
3. Apply filters
4. Generate and download

## System Settings

### Site Configuration
Navigate to **Settings → General** to configure basic settings.

#### Basic Settings
- Site name and description
- Contact information
- Business hours
- Currency settings
- Timezone configuration

### Email Settings
Navigate to **Settings → Email** to configure email delivery.

#### SMTP Configuration
1. Enter SMTP server details
2. Configure authentication
3. Test email delivery
4. Set default sender information

### Payment Settings
Navigate to **Settings → Payments** to configure payment gateways.

#### Supported Gateways
- Stripe
- PayPal
- Bank transfers

## SEO Management

### Meta Tags Configuration
Navigate to **Settings → SEO** to configure global SEO settings.

#### Global SEO Settings
- Default meta title format
- Site description
- Keywords
- Social media tags

### Sitemap Management
- Automatic sitemap generation
- Include/exclude content types
- Submission to search engines

### Robots.txt Configuration
- Configure crawler access
- Block sensitive areas
- Set crawl delays

## Security Features

### User Security
- Strong password requirements
- Two-factor authentication (if enabled)
- Session management
- Login attempt monitoring

### System Security
- CSRF protection
- XSS prevention
- SQL injection protection
- File upload validation

### Monitoring
- Security event logging
- Suspicious activity detection
- IP blacklisting
- Rate limiting

## Troubleshooting

### Common Issues

#### Login Problems
1. Check username/password
2. Clear browser cache
3. Check account status
4. Contact system administrator

#### Performance Issues
1. Check system health at `/health`
2. Monitor server resources
3. Clear application cache
4. Optimize database

#### Email Delivery Issues
1. Verify SMTP settings
2. Check spam folders
3. Test email configuration
4. Review email logs

### Getting Help
- Check system logs
- Review error messages
- Contact technical support
- Consult documentation

## Best Practices

### Content Management
- Regular content updates
- SEO optimization
- Image optimization
- Mobile responsiveness

### Security
- Regular password updates
- Monitor user activities
- Keep system updated
- Backup regularly

### Performance
- Monitor system health
- Optimize images
- Cache management
- Database maintenance

---

*This manual is regularly updated. For the latest version, check the documentation section in your admin panel.*
