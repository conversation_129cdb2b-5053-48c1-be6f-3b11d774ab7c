@if(isset($siteSettings['whatsapp_enabled']) && $siteSettings['whatsapp_enabled'] && isset($siteSettings['whatsapp_number']) && !empty($siteSettings['whatsapp_number']))
<!-- WhatsApp Floating Button -->
<div id="whatsapp-float" class="fixed bottom-6 left-6 z-[9999]">
    <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $siteSettings['whatsapp_number']) }}?text=Hello! I'm interested in your logistics services."
       target="_blank"
       rel="noopener noreferrer"
       class="whatsapp-btn group flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 cursor-pointer"
       title="Chat with us on WhatsApp"
       onclick="console.log('WhatsApp button clicked')">
        <i class="fab fa-whatsapp text-2xl group-hover:animate-pulse"></i>
        
        <!-- Tooltip -->
        <div class="absolute left-full ml-3 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 whitespace-nowrap">
            Chat with us on WhatsApp
            <div class="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-800 rotate-45"></div>
        </div>
    </a>
    
    <!-- Pulse animation rings -->
    <div class="absolute inset-0 rounded-full bg-green-500 animate-ping opacity-20"></div>
    <div class="absolute inset-0 rounded-full bg-green-500 animate-ping opacity-10" style="animation-delay: 0.5s;"></div>
</div>

<style>
@keyframes whatsapp-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.whatsapp-btn {
    animation: whatsapp-bounce 2s infinite;
}

.whatsapp-btn:hover {
    animation: none;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    #whatsapp-float {
        bottom: 1rem;
        left: 1rem;
    }
    
    .whatsapp-btn {
        width: 3rem;
        height: 3rem;
    }
    
    .whatsapp-btn i {
        font-size: 1.25rem;
    }
}

/* Ensure it doesn't interfere with live chat */
@media (min-width: 769px) {
    #whatsapp-float {
        bottom: 1.5rem;
        left: 1.5rem;
    }
}

/* Hide on very small screens to avoid clutter */
@media (max-width: 480px) {
    #whatsapp-float .absolute.left-full {
        display: none;
    }
}
</style>
@endif
