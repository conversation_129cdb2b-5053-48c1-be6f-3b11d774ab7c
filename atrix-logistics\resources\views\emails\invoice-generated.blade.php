<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .order-info {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .order-info h3 {
            margin: 0 0 15px 0;
            color: #28a745;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-paid { background: #d4edda; color: #155724; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .order-completed { background: #d4edda; color: #155724; }
        .order-processing { background: #d1ecf1; color: #0c5460; }
        .order-pending { background: #fff3cd; color: #856404; }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 0 10px 10px 0;
        }
        .cta-button:hover {
            background: #218838;
        }
        .cta-button.secondary {
            background: #6c757d;
        }
        .cta-button.secondary:hover {
            background: #5a6268;
        }
        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .order-summary h4 {
            margin: 0 0 15px 0;
            color: #28a745;
        }
        .order-items {
            margin: 15px 0;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-details {
            flex: 1;
        }
        .item-price {
            font-weight: bold;
            color: #28a745;
        }
        .total-section {
            border-top: 2px solid #28a745;
            padding-top: 15px;
            margin-top: 15px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .total-row.grand-total {
            font-size: 18px;
            font-weight: bold;
            color: #28a745;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
        .footer a {
            color: #28a745;
            text-decoration: none;
        }
        .attachment-notice {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .attachment-notice .icon {
            color: #2563eb;
            margin-right: 8px;
        }
        .payment-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .payment-info.paid {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        @media (max-width: 600px) {
            body { padding: 10px; }
            .content { padding: 20px 15px; }
            .info-row { flex-direction: column; }
            .info-label { margin-bottom: 5px; }
            .cta-button { display: block; margin: 10px 0; }
            .order-item { flex-direction: column; }
            .total-row { flex-direction: column; text-align: center; }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>Invoice Ready</h1>
            <p>Your order invoice has been generated</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Dear {{ $order->customer->name ?? 'Valued Customer' }},</p>
            
            <p>Thank you for your order! Your invoice has been generated and is ready for your records.</p>

            <!-- Order Information -->
            <div class="order-info">
                <h3>Order Details</h3>
                <div class="info-row">
                    <span class="info-label">Order Number:</span>
                    <span class="info-value"><strong>{{ $order->order_number }}</strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Order Date:</span>
                    <span class="info-value">{{ $order->created_at->format('M j, Y') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-{{ $order->payment_status }}">{{ ucfirst($order->payment_status) }}</span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Order Status:</span>
                    <span class="info-value">
                        <span class="status-badge order-{{ $order->status }}">{{ ucfirst($order->status) }}</span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Method:</span>
                    <span class="info-value">{{ ucfirst($order->payment_method) }}</span>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="order-summary">
                <h4>Order Summary</h4>
                <div class="order-items">
                    @foreach($order->items as $item)
                    <div class="order-item">
                        <div class="item-details">
                            <strong>{{ $item->product->name }}</strong><br>
                            <small>Qty: {{ $item->quantity }} × @currency($item->unit_price)</small>
                        </div>
                        <div class="item-price">@currency($item->total_price)</div>
                    </div>
                    @endforeach
                </div>
                
                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal:</span>
                        <span>@currency($order->subtotal)</span>
                    </div>
                    @if($order->tax_amount > 0)
                    <div class="total-row">
                        <span>Tax:</span>
                        <span>@currency($order->tax_amount)</span>
                    </div>
                    @endif
                    @if($order->shipping_amount > 0)
                    <div class="total-row">
                        <span>Shipping:</span>
                        <span>@currency($order->shipping_amount)</span>
                    </div>
                    @endif
                    @if($order->discount_amount > 0)
                    <div class="total-row">
                        <span>Discount:</span>
                        <span>-@currency($order->discount_amount)</span>
                    </div>
                    @endif
                    <div class="total-row grand-total">
                        <span>Total:</span>
                        <span>@currency($order->total_amount)</span>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if($order->payment_status === 'paid')
            <div class="payment-info paid">
                <strong>✓ Payment Confirmed</strong><br>
                Your payment has been successfully processed. Thank you!
            </div>
            @elseif($order->payment_status === 'pending')
            <div class="payment-info">
                <strong>⏳ Payment Pending</strong><br>
                Your payment is being processed. You will receive a confirmation once completed.
            </div>
            @endif

            <!-- Attachment Notice -->
            <div class="attachment-notice">
                <span class="icon">📎</span>
                <strong>Invoice Attached:</strong> Please find your detailed invoice attached to this email for your records.
            </div>

            <!-- Call to Action -->
            <div class="cta-section">
                <a href="{{ $orderUrl }}" class="cta-button">View Order Details</a>
                @if($order->payment_status !== 'paid')
                <a href="{{ route('customer.payments.show', $order->id) }}" class="cta-button">Make Payment</a>
                @endif
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}?subject=Order {{ $order->order_number }} - Support" class="cta-button secondary">Contact Support</a>
            </div>

            <!-- Shipping Information -->
            @if($order->shippingAddress)
            <div class="order-summary">
                <h4>Shipping Address</h4>
                <p>
                    {{ $order->shippingAddress->first_name }} {{ $order->shippingAddress->last_name }}<br>
                    {{ $order->shippingAddress->address_line_1 }}<br>
                    @if($order->shippingAddress->address_line_2)
                        {{ $order->shippingAddress->address_line_2 }}<br>
                    @endif
                    {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->postal_code }}<br>
                    {{ $order->shippingAddress->country }}
                </p>
            </div>
            @endif

            <!-- Contact Information -->
            <p>If you have any questions about this invoice or your order, please contact us:</p>
            <ul>
                <li><strong>Phone:</strong> {{ $siteSettings['company_phone'] ?? 'Phone Number' }}</li>
                <li><strong>Email:</strong> {{ $siteSettings['company_email'] ?? '<EMAIL>' }}</li>
                @if(isset($siteSettings['company_website']))
                <li><strong>Website:</strong> {{ $siteSettings['company_website'] }}</li>
                @endif
            </ul>

            <p>Thank you for choosing {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}!</p>

            <p>Best regards,<br>
            <strong>Customer Service Team</strong><br>
            {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>
                <strong>{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</strong><br>
                {{ $siteSettings['company_address'] ?? 'Company Address' }}<br>
                {{ $siteSettings['company_phone'] ?? 'Phone' }} | 
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}">{{ $siteSettings['company_email'] ?? '<EMAIL>' }}</a>
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                This email was sent regarding order #{{ $order->order_number }}. 
                <a href="{{ $orderUrl }}">View online</a> | 
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}">Contact Support</a>
            </p>
        </div>
    </div>
</body>
</html>
