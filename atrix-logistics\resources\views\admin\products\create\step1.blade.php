@extends('layouts.admin')

@section('title', 'Create Product - Step 1')
@section('page-title', 'Create New Product')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
    </div>
@endsection

@section('content')
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="progress-steps">
                        <div class="step active">
                            <div class="step-number">1</div>
                            <div class="step-title">Basic Info</div>
                        </div>
                        <div class="step">
                            <div class="step-number">2</div>
                            <div class="step-title">Content & SEO</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-title">Pricing & Inventory</div>
                        </div>
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-title">Images & Media</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.products.create.step1.store') }}">
                @csrf

                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="name" class="form-label">Product Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name') }}" required autofocus>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sku" class="form-label">SKU</label>
                                <input type="text" class="form-control @error('sku') is-invalid @enderror" 
                                       id="sku" name="sku" value="{{ old('sku') }}"
                                       placeholder="Auto-generated">
                                @error('sku')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Leave empty to auto-generate</small>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="slug" class="form-label">URL Slug</label>
                                <input type="text" class="form-control @error('slug') is-invalid @enderror" 
                                       id="slug" name="slug" value="{{ old('slug') }}"
                                       placeholder="Auto-generated from name">
                                @error('slug')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">Leave empty to auto-generate from name</small>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category_id" class="form-label">Category <span class="text-danger">*</span></label>
                                <select class="form-select @error('category_id') is-invalid @enderror" 
                                        id="category_id" name="category_id" required>
                                    <option value="">-- Select Category --</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                        @foreach($category->children as $child)
                                            <option value="{{ $child->id }}" {{ old('category_id') == $child->id ? 'selected' : '' }}>
                                                └─ {{ $child->name }}
                                            </option>
                                        @endforeach
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description</label>
                            <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                      id="short_description" name="short_description" rows="3" 
                                      maxlength="500"
                                      placeholder="Brief product description for listings...">{{ old('short_description') }}</textarea>
                            @error('short_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Maximum 500 characters</small>
                        </div>
                    </div>
                </div>

                <!-- Product Type & Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Product Type & Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="mb-3">Product Type</h6>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="is_digital" name="is_digital" 
                                           value="1" {{ old('is_digital') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_digital">
                                        <strong>Digital Product</strong>
                                        <br><small class="text-muted">Downloadable product (software, ebooks, etc.)</small>
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="checkbox" id="is_virtual" name="is_virtual" 
                                           value="1" {{ old('is_virtual') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_virtual">
                                        <strong>Virtual Product</strong>
                                        <br><small class="text-muted">No shipping required (services, consultations)</small>
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-3">Status Settings</h6>
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <strong>Active Status</strong>
                                        <br><small class="text-muted">Show this product on the website</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                           value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        <strong>Featured Product</strong>
                                        <br><small class="text-muted">Highlight this product</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary me-md-2">
                        <i class="fas fa-times me-1"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary" id="nextBtn">
                        <i class="fas fa-arrow-right me-1"></i> Next: Content & SEO
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Progress Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Step 1 of 4
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Basic Information</h6>
                    <p class="text-muted mb-3">
                        Set up the fundamental details of your product including name, category, and basic settings.
                    </p>
                    
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 25%">25%</div>
                    </div>
                    
                    <h6>What's Next?</h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-1">
                            <i class="fas fa-arrow-right text-primary me-2"></i>
                            <small>Content & SEO details</small>
                        </li>
                        <li class="mb-1">
                            <i class="fas fa-arrow-right text-muted me-2"></i>
                            <small>Pricing & inventory</small>
                        </li>
                        <li>
                            <i class="fas fa-arrow-right text-muted me-2"></i>
                            <small>Images & media</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Use clear, descriptive product names</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Choose the most specific category</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Write compelling short descriptions</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small>Set appropriate product type flags</small>
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            <small>You can always edit these later</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Categories Quick View -->
            @if($categories->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-folder me-2"></i>
                            Available Categories
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="category-list" style="max-height: 200px; overflow-y: auto;">
                            @foreach($categories->take(10) as $category)
                                <div class="mb-2">
                                    <i class="fas fa-folder text-primary me-2"></i>
                                    <small>{{ $category->name }}</small>
                                    @if($category->children->count() > 0)
                                        <div class="ms-3">
                                            @foreach($category->children->take(3) as $child)
                                                <div class="mb-1">
                                                    <i class="fas fa-folder-open text-secondary me-2"></i>
                                                    <small>{{ $child->name }}</small>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                        
                        @if($categories->count() === 0)
                            <p class="text-muted mb-0">
                                <a href="{{ route('admin.categories.create') }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus me-1"></i> Create Category
                                </a>
                            </p>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection

@push('styles')
<style>
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin: 0 20px;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .step.active .step-number {
        background-color: #0d6efd;
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6c757d;
        text-align: center;
    }

    .step.active .step-title {
        color: #0d6efd;
    }

    @media (max-width: 768px) {
        .progress-steps {
            margin: 0 10px;
        }

        .step-title {
            font-size: 0.75rem;
        }

        .step-number {
            width: 32px;
            height: 32px;
            font-size: 0.875rem;
        }
    }

    /* Loading button styles */
    .btn.loading {
        position: relative;
        pointer-events: none;
        opacity: 0.8;
    }

    .btn.loading .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush

@push('scripts')
<script>
    // Auto-generate slug from name
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value;
        const slug = name.toLowerCase()
                        .replace(/[^a-z0-9\s-]/g, '')
                        .replace(/\s+/g, '-')
                        .replace(/-+/g, '-')
                        .trim('-');
        document.getElementById('slug').value = slug;
        
        // Auto-generate SKU
        const sku = name.toUpperCase()
                       .replace(/[^A-Z0-9\s]/g, '')
                       .replace(/\s+/g, '')
                       .substring(0, 8);
        document.getElementById('sku').value = sku;
    });

    // Character counter for short description
    document.getElementById('short_description').addEventListener('input', function() {
        const maxLength = 500;
        const currentLength = this.value.length;
        const remaining = maxLength - currentLength;
        
        // Find or create counter element
        let counter = document.getElementById('short-description-counter');
        if (!counter) {
            counter = document.createElement('small');
            counter.id = 'short-description-counter';
            counter.className = 'text-muted float-end';
            this.parentNode.appendChild(counter);
        }
        
        counter.textContent = `${currentLength}/${maxLength}`;
        counter.className = remaining < 50 ? 'text-warning float-end' : (remaining < 20 ? 'text-danger float-end' : 'text-muted float-end');
    });

    // Product type logic
    document.getElementById('is_digital').addEventListener('change', function() {
        if (this.checked) {
            document.getElementById('is_virtual').checked = true;
        }
    });

    document.getElementById('is_virtual').addEventListener('change', function() {
        if (!this.checked) {
            document.getElementById('is_digital').checked = false;
        }
    });

    // Add spinner to form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        const nextBtn = document.getElementById('nextBtn');
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.classList.add('loading');
            nextBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
        }
    });
</script>
@endpush
