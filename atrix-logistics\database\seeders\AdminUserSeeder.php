<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '+****************',
            'address' => '123 Logistics Ave',
            'city' => 'Transport City',
            'state' => 'TC',
            'postal_code' => '12345',
            'country' => 'USA',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create staff user
        User::create([
            'name' => 'Staff User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'staff',
            'phone' => '+****************',
            'address' => '123 Logistics Ave',
            'city' => 'Transport City',
            'state' => 'TC',
            'postal_code' => '12345',
            'country' => 'USA',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create customer user
        User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'phone' => '+****************',
            'address' => '456 Customer St',
            'city' => 'Customer City',
            'state' => 'CC',
            'postal_code' => '54321',
            'country' => 'USA',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);
    }
}
