<?php

namespace App\Console\Commands;

use App\Models\Parcel;
use App\Models\User;
use App\Models\Carrier;
use Illuminate\Console\Command;

class CreateTestParcel extends Command
{
    protected $signature = 'test:create-parcel {--unpaid : Create an unpaid parcel}';
    protected $description = 'Create a test parcel for payment testing';

    public function handle()
    {
        // Get a customer user
        $customer = User::where('role', 'customer')->first();
        if (!$customer) {
            $this->error('No customer users found. Please create a customer user first.');
            return 1;
        }

        // Get a carrier
        $carrier = Carrier::first();
        if (!$carrier) {
            $this->error('No carriers found. Please create a carrier first.');
            return 1;
        }

        // Create test parcel
        $parcel = Parcel::create([
            'tracking_number' => 'ATX-TEST-' . now()->format('YmdHis'),
            'carrier_id' => $carrier->id,
            'user_id' => $customer->id,
            'sender_name' => 'Test Sender',
            'sender_email' => '<EMAIL>',
            'sender_phone' => '+****************',
            'sender_address' => '123 Test Street',
            'sender_city' => 'Test City',
            'sender_state' => 'TS',
            'sender_postal_code' => '12345',
            'sender_country' => 'USA',
            'recipient_name' => $customer->name,
            'recipient_email' => $customer->email,
            'recipient_phone' => '+****************',
            'recipient_address' => '456 Customer Ave',
            'recipient_city' => 'Customer City',
            'recipient_state' => 'CS',
            'recipient_postal_code' => '67890',
            'recipient_country' => 'USA',
            'description' => 'Test Package for Payment Testing',
            'weight' => '2.50',
            'dimensions' => '30x20x15 cm',
            'declared_value' => '75.00',
            'service_type' => 'express',
            'status' => 'pending',
            'shipping_cost' => '25.99',
            'insurance_cost' => '5.00',
            'total_cost' => '30.99',
            'is_paid' => $this->option('unpaid') ? false : true,
        ]);

        $this->info("Test parcel created successfully!");
        $this->info("Tracking Number: {$parcel->tracking_number}");
        $this->info("Parcel ID: {$parcel->id}");
        $this->info("Customer: {$customer->name} ({$customer->email})");
        $this->info("Total Cost: $" . number_format($parcel->total_cost, 2));
        $this->info("Payment Status: " . ($parcel->is_paid ? 'Paid' : 'Unpaid'));
        
        if (!$parcel->is_paid) {
            $this->info("Payment URL: " . route('customer.payments.show', $parcel));
        }

        return 0;
    }
}
