<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NewsletterSubscriber>
 */
class NewsletterSubscriberFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'email' => $this->faker->unique()->safeEmail(),
            'name' => $this->faker->name(),
            'status' => $this->faker->randomElement(['active', 'unsubscribed', 'bounced']),
            'subscribed_at' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'subscription_source' => $this->faker->randomElement(['website', 'admin', 'import', 'contact_form']),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'unsubscribe_token' => $this->faker->unique()->sha256(),
        ];
    }
}
