<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class TeamMember extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'position',
        'department',
        'bio',
        'email',
        'phone',
        'photo',
        'social_links',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'social_links' => 'array',
    ];

    /**
     * Get the photo URL
     */
    public function getPhotoUrlAttribute(): ?string
    {
        return $this->photo ? Storage::url($this->photo) : null;
    }

    /**
     * Get active team members
     */
    public static function active()
    {
        return static::where('is_active', true)
                    ->orderBy('sort_order')
                    ->orderBy('name');
    }

    /**
     * Get team members for frontend display
     */
    public static function forDisplay()
    {
        return static::active()->get();
    }

    /**
     * Get formatted social media links
     */
    public function getFormattedSocialLinksAttribute(): array
    {
        $links = [];
        $socialLinks = $this->social_links ?? [];

        foreach ($socialLinks as $platform => $url) {
            if ($url) {
                $links[$platform] = [
                    'url' => $url,
                    'icon' => $this->getSocialIcon($platform),
                    'name' => ucfirst($platform)
                ];
            }
        }

        return $links;
    }

    /**
     * Get social media icon class
     */
    private function getSocialIcon(string $platform): string
    {
        return match($platform) {
            'linkedin' => 'fab fa-linkedin',
            'twitter' => 'fab fa-twitter',
            'facebook' => 'fab fa-facebook',
            'instagram' => 'fab fa-instagram',
            'youtube' => 'fab fa-youtube',
            default => 'fas fa-link',
        };
    }

    /**
     * Get short bio
     */
    public function getShortBioAttribute(): string
    {
        return $this->bio ? \Str::limit($this->bio, 150) : '';
    }

    /**
     * Scope for ordering by sort order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope for active members
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
