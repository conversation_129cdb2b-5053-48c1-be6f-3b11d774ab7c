@extends('layouts.admin')

@section('title', 'Team Members')
@section('page-title', 'Team Members Management')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.team.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Team Member
        </a>
        <button type="button" class="btn btn-outline-info" onclick="toggleSortMode()">
            <i class="fas fa-sort me-1"></i> Reorder
        </button>
    </div>
@endsection

@section('content')
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                Team Members ({{ $teamMembers->total() }} total)
            </h6>
            <div class="text-muted small">
                Showing {{ $teamMembers->firstItem() ?? 0 }} to {{ $teamMembers->lastItem() ?? 0 }} of {{ $teamMembers->total() }} results
            </div>
        </div>
        <div class="card-body">
            @if($teamMembers->count() > 0)
                <div id="sortable-container">
                    <div class="row" id="team-grid">
                        @foreach($teamMembers as $member)
                            <div class="col-lg-4 col-md-6 mb-4" data-id="{{ $member->id }}">
                                <div class="card team-member-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-{{ $member->is_active ? 'success' : 'secondary' }} me-2">
                                                {{ $member->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                            <small class="text-muted">Order: {{ $member->sort_order }}</small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                                    data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ route('admin.cms.team.show', $member) }}">
                                                    <i class="fas fa-eye me-2"></i> View
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('admin.cms.team.edit', $member) }}">
                                                    <i class="fas fa-edit me-2"></i> Edit
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="#" 
                                                       onclick="toggleStatus('{{ $member->id }}', '{{ $member->name }}', {{ $member->is_active ? 'false' : 'true' }})">
                                                    <i class="fas fa-{{ $member->is_active ? 'eye-slash' : 'eye' }} me-2"></i> 
                                                    {{ $member->is_active ? 'Deactivate' : 'Activate' }}
                                                </a></li>
                                                <li><a class="dropdown-item text-danger" href="#" 
                                                       onclick="confirmDelete('{{ $member->name }}', '{{ route('admin.cms.team.destroy', $member) }}')">
                                                    <i class="fas fa-trash me-2"></i> Delete
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            @if($member->photo)
                                                <img src="{{ Storage::url($member->photo) }}" 
                                                     alt="{{ $member->name }}" 
                                                     class="rounded-circle img-thumbnail" 
                                                     style="width: 100px; height: 100px; object-fit: cover;">
                                            @else
                                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto" 
                                                     style="width: 100px; height: 100px;">
                                                    <i class="fas fa-user fa-2x text-muted"></i>
                                                </div>
                                            @endif
                                        </div>
                                        <h6 class="card-title mb-1">{{ $member->name }}</h6>
                                        <p class="text-muted small mb-2">{{ $member->position }}</p>
                                        
                                        @if($member->bio)
                                            <p class="card-text small">{{ Str::limit($member->bio, 100) }}</p>
                                        @endif
                                        
                                        <div class="d-flex justify-content-center gap-2 mt-3">
                                            @if($member->email)
                                                <a href="mailto:{{ $member->email }}" class="btn btn-sm btn-outline-primary" title="Email">
                                                    <i class="fas fa-envelope"></i>
                                                </a>
                                            @endif
                                            @if($member->phone)
                                                <a href="tel:{{ $member->phone }}" class="btn btn-sm btn-outline-success" title="Phone">
                                                    <i class="fas fa-phone"></i>
                                                </a>
                                            @endif
                                            @if($member->linkedin_url)
                                                <a href="{{ $member->linkedin_url }}" target="_blank" class="btn btn-sm btn-outline-info" title="LinkedIn">
                                                    <i class="fab fa-linkedin"></i>
                                                </a>
                                            @endif
                                            @if($member->twitter_url)
                                                <a href="{{ $member->twitter_url }}" target="_blank" class="btn btn-sm btn-outline-primary" title="Twitter">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                            @endif
                                            @if($member->facebook_url)
                                                <a href="{{ $member->facebook_url }}" target="_blank" class="btn btn-sm btn-outline-primary" title="Facebook">
                                                    <i class="fab fa-facebook"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="card-footer text-center">
                                        <small class="text-muted">
                                            Added {{ $member->created_at->format('M d, Y') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Pagination -->
                <div class="mt-4">
                    {{ $teamMembers->links('pagination.admin') }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-users fa-4x text-muted mb-4"></i>
                    <h5 class="text-muted">No Team Members Found</h5>
                    <p class="text-muted">Get started by adding your first team member.</p>
                    <a href="{{ route('admin.cms.team.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add First Team Member
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete team member <strong id="deleteMemberName"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Team Member</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Sort Mode Instructions -->
    <div id="sortInstructions" class="alert alert-info" style="display: none;">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Sort Mode Active:</strong> Drag and drop team member cards to reorder them. Click "Save Order" when finished.
        <button type="button" class="btn btn-sm btn-success ms-3" onclick="saveOrder()">
            <i class="fas fa-save me-1"></i> Save Order
        </button>
        <button type="button" class="btn btn-sm btn-secondary ms-2" onclick="cancelSort()">
            <i class="fas fa-times me-1"></i> Cancel
        </button>
    </div>
@endsection

@push('styles')
<style>
    .team-member-card {
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    .team-member-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    .sortable-mode .team-member-card {
        cursor: move;
        border-color: #007bff;
    }
    .sortable-mode .team-member-card:hover {
        border-color: #0056b3;
    }
    .ui-sortable-helper {
        transform: rotate(5deg);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.3);
    }
</style>
@endpush

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
<script>
    let sortMode = false;
    let originalOrder = [];

    function confirmDelete(memberName, deleteUrl) {
        document.getElementById('deleteMemberName').textContent = memberName;
        document.getElementById('deleteForm').action = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus(memberId, memberName, newStatus) {
        if (confirm(`Are you sure you want to ${newStatus === 'true' ? 'activate' : 'deactivate'} ${memberName}?`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/cms/team/${memberId}/toggle-status`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            document.body.appendChild(form);
            form.submit();
        }
    }

    function toggleSortMode() {
        sortMode = !sortMode;
        const container = document.getElementById('sortable-container');
        const instructions = document.getElementById('sortInstructions');
        
        if (sortMode) {
            // Store original order
            originalOrder = Array.from(document.querySelectorAll('[data-id]')).map(el => el.dataset.id);
            
            // Enable sorting
            container.classList.add('sortable-mode');
            instructions.style.display = 'block';
            
            // Initialize jQuery UI sortable
            $('#team-grid').sortable({
                items: '.col-lg-4',
                placeholder: 'ui-state-highlight',
                helper: 'clone',
                opacity: 0.8
            });
        } else {
            cancelSort();
        }
    }

    function saveOrder() {
        const newOrder = Array.from(document.querySelectorAll('[data-id]')).map(el => el.dataset.id);
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/admin/cms/team/update-order';

        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);
        
        newOrder.forEach((id, index) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `orders[${index}]`;
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }

    function cancelSort() {
        sortMode = false;
        const container = document.getElementById('sortable-container');
        const instructions = document.getElementById('sortInstructions');
        
        container.classList.remove('sortable-mode');
        instructions.style.display = 'none';
        
        // Destroy sortable
        if ($('#team-grid').hasClass('ui-sortable')) {
            $('#team-grid').sortable('destroy');
        }
        
        // Restore original order if needed
        if (originalOrder.length > 0) {
            const grid = document.getElementById('team-grid');
            const items = Array.from(grid.children);
            
            originalOrder.forEach(id => {
                const item = items.find(el => el.dataset.id === id);
                if (item) {
                    grid.appendChild(item);
                }
            });
        }
    }
</script>
@endpush
