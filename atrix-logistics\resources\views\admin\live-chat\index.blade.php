@extends('layouts.admin')

@section('page-title', 'Live Chat Management')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.communications.live-chat.history') }}" class="btn btn-outline-info">
            <i class="fas fa-history me-1"></i> Chat History
        </a>
        <button type="button" class="btn btn-outline-primary" onclick="refreshChatData()">
            <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
        <button type="button" class="btn btn-outline-secondary" onclick="toggleAutoRefresh()">
            <i class="fas fa-play me-1" id="autoRefreshIcon"></i>
            <span id="autoRefreshText">Start Auto Refresh</span>
        </button>
    </div>
@endsection

@section('content')
    <!-- Live Chat Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0" id="totalActive">{{ $stats['total_active'] }}</h4>
                            <p class="mb-0">Active Chats</p>
                        </div>
                        <div>
                            <i class="fas fa-comments fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0" id="totalWaiting">{{ $stats['total_waiting'] }}</h4>
                            <p class="mb-0">Waiting for Response</p>
                        </div>
                        <div>
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0" id="totalUnread">{{ $stats['total_unread'] }}</h4>
                            <p class="mb-0">Unread Messages</p>
                        </div>
                        <div>
                            <i class="fas fa-envelope fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0" id="myAssigned">{{ $stats['my_assigned'] }}</h4>
                            <p class="mb-0">My Assigned Chats</p>
                        </div>
                        <div>
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Statistics Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-secondary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0" id="totalClosed">{{ $stats['total_closed'] }}</h4>
                            <p class="mb-0">Closed Sessions</p>
                        </div>
                        <div>
                            <i class="fas fa-history fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <div class="mt-2">
                        <a href="{{ route('admin.communications.live-chat.history') }}" class="btn btn-sm btn-light">
                            <i class="fas fa-eye me-1"></i> View History
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-9 col-md-6 mb-3">
            <div class="card h-100">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        Quick Stats
                    </h6>
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="border-end">
                                <h5 class="text-primary mb-0">{{ $stats['total_active'] + $stats['total_waiting'] + $stats['total_closed'] }}</h5>
                                <small class="text-muted">Total Sessions</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border-end">
                                <h5 class="text-info mb-0">{{ round(($stats['total_closed'] / max(1, $stats['total_active'] + $stats['total_waiting'] + $stats['total_closed'])) * 100) }}%</h5>
                                <small class="text-muted">Completion Rate</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border-end">
                                <h5 class="text-warning mb-0">{{ $stats['total_unread'] }}</h5>
                                <small class="text-muted">Unread Messages</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <h5 class="text-success mb-0">{{ $stats['my_assigned'] }}</h5>
                            <small class="text-muted">My Active Chats</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Waiting Sessions -->
    @if($waitingSessions->count() > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        Waiting for Response ({{ $waitingSessions->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="waitingSessions">
                        @foreach($waitingSessions as $session)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            {{ $session->visitor_name ?? 'Anonymous Visitor' }}
                                        </h6>
                                        @if($session->unread_count > 0)
                                        <span class="badge bg-danger">{{ $session->unread_count }}</span>
                                        @endif
                                    </div>
                                    
                                    @if($session->visitor_email)
                                    <p class="text-muted small mb-1">{{ $session->visitor_email }}</p>
                                    @endif
                                    
                                    @if($session->messages->first())
                                    <p class="card-text small">{{ Str::limit($session->messages->first()->message, 60) }}</p>
                                    @endif
                                    
                                    <small class="text-muted">{{ $session->created_at->diffForHumans() }}</small>
                                    
                                    <div class="mt-2">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ route('admin.communications.live-chat.show', $session) }}"
                                               class="btn btn-primary">
                                                <i class="fas fa-eye me-1"></i> View Chat
                                            </a>
                                            <button type="button" class="btn btn-outline-info"
                                                    onclick="showSessionDetails({{ $session->id }})" title="Session Details">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                            <button type="button" class="btn btn-success"
                                                    onclick="assignToMe({{ $session->id }})">
                                                <i class="fas fa-user-plus me-1"></i> Take Chat
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Active Sessions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-comments me-2 text-primary"></i>
                        Active Chat Sessions ({{ $activeSessions->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    @if($activeSessions->count() > 0)
                    <div class="row" id="activeSessions">
                        @foreach($activeSessions as $session)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            {{ $session->visitor_name ?? 'Anonymous Visitor' }}
                                        </h6>
                                        @if($session->unread_count > 0)
                                        <span class="badge bg-danger">{{ $session->unread_count }}</span>
                                        @endif
                                    </div>
                                    
                                    @if($session->visitor_email)
                                    <p class="text-muted small mb-1">{{ $session->visitor_email }}</p>
                                    @endif
                                    
                                    @if($session->assignedStaff)
                                    <p class="text-success small mb-1">
                                        <i class="fas fa-user me-1"></i>
                                        Assigned to: {{ $session->assignedStaff->name }}
                                    </p>
                                    @endif
                                    
                                    @if($session->messages->first())
                                    <p class="card-text small">{{ Str::limit($session->messages->first()->message, 60) }}</p>
                                    @endif
                                    
                                    <small class="text-muted">Last activity: {{ $session->last_activity?->diffForHumans() }}</small>
                                    
                                    <div class="mt-2">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ route('admin.communications.live-chat.show', $session) }}"
                                               class="btn btn-primary">
                                                <i class="fas fa-eye me-1"></i> View Chat
                                            </a>
                                            <button type="button" class="btn btn-outline-info"
                                                    onclick="showSessionDetails({{ $session->id }})" title="Session Details">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                            @if(!$session->assignedStaff || $session->assigned_to === auth()->id())
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="closeSession({{ $session->id }})">
                                                <i class="fas fa-times me-1"></i> Close
                                            </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-4">
                        <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No active chat sessions</h5>
                        <p class="text-muted">Active chats will appear here when visitors start conversations.</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Session Details Modal -->
    <div class="modal fade" id="sessionDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="sessionDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="viewConversationBtn" style="display: none;">
                        <i class="fas fa-eye me-1"></i> View Full Conversation
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
let autoRefreshInterval = null;
let isAutoRefreshActive = false;

// Refresh chat data
function refreshChatData() {
    fetch('{{ route("admin.communications.live-chat.stats") }}')
        .then(response => response.json())
        .then(data => {
            // Update statistics
            document.getElementById('totalActive').textContent = data.total_active;
            document.getElementById('totalWaiting').textContent = data.total_waiting;
            document.getElementById('totalUnread').textContent = data.total_unread;
            document.getElementById('myAssigned').textContent = data.my_assigned;
            document.getElementById('totalClosed').textContent = data.total_closed;
            
            console.log('Chat data refreshed');
        })
        .catch(error => {
            console.error('Error refreshing chat data:', error);
        });
}

// Toggle auto refresh
function toggleAutoRefresh() {
    if (isAutoRefreshActive) {
        clearInterval(autoRefreshInterval);
        isAutoRefreshActive = false;
        document.getElementById('autoRefreshIcon').className = 'fas fa-play me-1';
        document.getElementById('autoRefreshText').textContent = 'Start Auto Refresh';
    } else {
        autoRefreshInterval = setInterval(refreshChatData, 10000); // Refresh every 10 seconds
        isAutoRefreshActive = true;
        document.getElementById('autoRefreshIcon').className = 'fas fa-pause me-1';
        document.getElementById('autoRefreshText').textContent = 'Stop Auto Refresh';
    }
}

// Assign session to current user
function assignToMe(sessionId) {
    fetch(`/admin/communications/live-chat/sessions/${sessionId}/assign`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error assigning chat session');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error assigning chat session');
    });
}

// Close session
function closeSession(sessionId) {
    if (confirm('Are you sure you want to close this chat session?')) {
        fetch(`/admin/communications/live-chat/sessions/${sessionId}/close`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error closing chat session');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error closing chat session');
        });
    }
}

// Show session details
function showSessionDetails(sessionId) {
    // Show loading state
    document.getElementById('sessionDetailsContent').innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-spinner fa-spin fa-2x mb-3 text-primary"></i>
            <p class="text-muted">Loading session details...</p>
        </div>
    `;

    // Hide view conversation button initially
    document.getElementById('viewConversationBtn').style.display = 'none';

    const modal = new bootstrap.Modal(document.getElementById('sessionDetailsModal'));
    modal.show();

    // Fetch session details
    fetch(`/admin/communications/live-chat/sessions/${sessionId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySessionDetails(data.data);
            } else {
                showError('Failed to load session details');
            }
        })
        .catch(error => {
            console.error('Error fetching session details:', error);
            showError('Error loading session details');
        });
}

function displaySessionDetails(data) {
    const session = data.session;
    const stats = data.statistics;
    const browserInfo = data.browser_info;

    // Show view conversation button
    const viewBtn = document.getElementById('viewConversationBtn');
    viewBtn.style.display = 'inline-block';
    viewBtn.onclick = () => {
        window.open(`/admin/communications/live-chat/sessions/${session.id}`, '_blank');
    };

    const statusBadgeClass = session.status === 'active' ? 'bg-success' :
                            session.status === 'waiting' ? 'bg-warning' : 'bg-secondary';

    const content = `
        <div class="row">
            <!-- Session Information -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Session Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">Session ID:</td>
                                <td><code>${session.session_id}</code></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Visitor:</td>
                                <td>${session.visitor_name}</td>
                            </tr>
                            ${session.visitor_email ? `
                            <tr>
                                <td class="fw-bold">Email:</td>
                                <td><a href="mailto:${session.visitor_email}">${session.visitor_email}</a></td>
                            </tr>
                            ` : ''}
                            <tr>
                                <td class="fw-bold">Status:</td>
                                <td><span class="badge ${statusBadgeClass}">${session.status.charAt(0).toUpperCase() + session.status.slice(1)}</span></td>
                            </tr>
                            ${session.assigned_staff ? `
                            <tr>
                                <td class="fw-bold">Assigned Staff:</td>
                                <td><span class="badge bg-info">${session.assigned_staff}</span></td>
                            </tr>
                            ` : ''}
                            <tr>
                                <td class="fw-bold">Started:</td>
                                <td>${session.created_at}</td>
                            </tr>
                            ${session.status === 'closed' ? `
                            <tr>
                                <td class="fw-bold">Ended:</td>
                                <td>${session.updated_at}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Duration:</td>
                                <td><strong>${session.duration}</strong></td>
                            </tr>
                            ` : `
                            <tr>
                                <td class="fw-bold">Last Activity:</td>
                                <td>${session.last_activity || 'Just now'}</td>
                            </tr>
                            `}
                            <tr>
                                <td class="fw-bold">IP Address:</td>
                                <td><code>${session.visitor_ip || 'Not recorded'}</code></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            Chat Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center mb-3">
                            <div class="col-4">
                                <div class="border-end">
                                    <h4 class="text-primary mb-0">${stats.total_messages}</h4>
                                    <small class="text-muted">Total Messages</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="border-end">
                                    <h4 class="text-success mb-0">${stats.visitor_messages}</h4>
                                    <small class="text-muted">Visitor</small>
                                </div>
                            </div>
                            <div class="col-4">
                                <h4 class="text-info mb-0">${stats.staff_messages}</h4>
                                <small class="text-muted">Staff</small>
                            </div>
                        </div>

                        ${stats.avg_response_time > 0 ? `
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">Avg Response Time:</td>
                                <td><span class="badge bg-primary">${stats.avg_response_time} min</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">First Response:</td>
                                <td><span class="badge bg-success">${stats.first_response_time} min</span></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Longest Response:</td>
                                <td><span class="badge bg-warning">${stats.longest_response_time} min</span></td>
                            </tr>
                        </table>
                        ` : `
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-2x mb-2"></i>
                            <p>Response time statistics will appear after staff replies</p>
                        </div>
                        `}
                    </div>
                </div>
            </div>
        </div>

        <!-- Browser Information -->
        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-globe me-2"></i>
                            Browser & Device Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Browser:</strong><br>
                                <span class="badge bg-light text-dark">${browserInfo.browser}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Platform:</strong><br>
                                <span class="badge bg-light text-dark">${browserInfo.platform}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Device:</strong><br>
                                <span class="badge bg-light text-dark">${browserInfo.device}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>User Agent:</strong><br>
                                <small class="text-muted" title="${browserInfo.full_user_agent}">
                                    ${browserInfo.full_user_agent ? browserInfo.full_user_agent.substring(0, 50) + '...' : 'Not available'}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('sessionDetailsContent').innerHTML = content;
}

function showError(message) {
    document.getElementById('sessionDetailsContent').innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${message}
        </div>
    `;
}

// Start auto refresh on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleAutoRefresh(); // Start auto refresh by default
});
</script>
@endpush

@push('styles')
<style>
/* Modal Enhancements */
.modal-lg {
    max-width: 900px;
}

.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.table-borderless td {
    padding: 0.5rem 0;
    border: none;
}

.badge {
    font-size: 0.75em;
}

/* Statistics Cards */
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-lg {
        max-width: 95%;
        margin: 10px auto;
    }
}
</style>
@endpush
