<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', $documentType ?? 'Document')</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .document {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Header Styles */
        .document-header {
            display: table;
            width: 100%;
            margin-bottom: 30px;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
        }

        .header-left {
            display: table-cell;
            width: 60%;
            vertical-align: top;
        }

        .header-right {
            display: table-cell;
            width: 40%;
            vertical-align: top;
            text-align: right;
        }

        .company-logo {
            max-width: 150px;
            max-height: 80px;
            margin-bottom: 10px;
        }

        .company-info {
            color: #666;
            font-size: 11px;
            line-height: 1.4;
        }

        .document-title {
            font-size: 28px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }

        .document-number {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .document-date {
            font-size: 11px;
            color: #666;
        }

        /* Barcode Styles */
        .barcode-section {
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }

        .barcode {
            font-family: 'Courier New', monospace;
            font-size: 10px;
            letter-spacing: 2px;
            margin-top: 5px;
        }

        /* Content Styles */
        .document-content {
            margin: 30px 0;
        }

        .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }

        /* Table Styles */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .table th,
        .table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
            vertical-align: top;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            font-size: 11px;
        }

        .table td {
            font-size: 11px;
        }

        .table tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        /* Info Box Styles */
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box-title {
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }

        /* Address Styles */
        .address-section {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }

        .address-box {
            display: table-cell;
            width: 48%;
            vertical-align: top;
            padding: 15px;
            border: 1px solid #dee2e6;
            background: #f8f9fa;
        }

        .address-box + .address-box {
            margin-left: 4%;
        }

        .address-title {
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .address-content {
            font-size: 11px;
            line-height: 1.4;
        }

        /* Total Styles */
        .totals-section {
            margin-top: 30px;
            text-align: right;
        }

        .totals-table {
            width: 300px;
            margin-left: auto;
            border-collapse: collapse;
        }

        .totals-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #dee2e6;
        }

        .totals-table .total-label {
            font-weight: bold;
            text-align: right;
            width: 60%;
        }

        .totals-table .total-value {
            font-weight: bold;
            text-align: right;
            width: 40%;
        }

        .totals-table .grand-total {
            border-top: 2px solid #2563eb;
            background: #f8f9fa;
            font-size: 14px;
            color: #2563eb;
        }

        /* Footer Styles */
        .document-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            font-size: 10px;
            color: #666;
            text-align: center;
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .text-left { text-align: left; }
        .font-bold { font-weight: bold; }
        .text-primary { color: #2563eb; }
        .text-muted { color: #666; }
        .mb-10 { margin-bottom: 10px; }
        .mb-20 { margin-bottom: 20px; }
        .mt-20 { margin-top: 20px; }

        /* Status Badges */
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .badge-success { background: #28a745; color: white; }
        .badge-warning { background: #ffc107; color: #212529; }
        .badge-danger { background: #dc3545; color: white; }
        .badge-info { background: #17a2b8; color: white; }
        .badge-secondary { background: #6c757d; color: white; }

        /* Print Styles */
        @media print {
            body { margin: 0; }
            .document { 
                box-shadow: none; 
                margin: 0; 
                padding: 15mm;
            }
            .page-break { page-break-before: always; }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .document { padding: 15px; }
            .header-left, .header-right { 
                display: block; 
                width: 100%; 
                text-align: left; 
            }
            .address-box { 
                display: block; 
                width: 100%; 
                margin-bottom: 15px; 
            }
        }
    </style>
    @stack('document-styles')
</head>
<body>
    <div class="document">
        <!-- Document Header -->
        <div class="document-header">
            <div class="header-left">
                @if(isset($siteSettings['site_logo']) && $siteSettings['site_logo'])
                    <img src="{{ public_path('storage/' . $siteSettings['site_logo']) }}" alt="Company Logo" class="company-logo">
                @endif
                <div class="company-info">
                    <strong>{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</strong><br>
                    {{ $siteSettings['company_address'] ?? 'Company Address' }}<br>
                    {{ $siteSettings['company_city'] ?? 'City' }}, {{ $siteSettings['company_state'] ?? 'State' }} {{ $siteSettings['company_zip'] ?? 'ZIP' }}<br>
                    Phone: {{ $siteSettings['company_phone'] ?? 'Phone Number' }}<br>
                    Email: {{ $siteSettings['company_email'] ?? '<EMAIL>' }}<br>
                    @if(isset($siteSettings['company_website']))
                        Website: {{ $siteSettings['company_website'] }}
                    @endif
                </div>
            </div>
            <div class="header-right">
                <div class="document-title">@yield('document-title', $documentType ?? 'Document')</div>
                <div class="document-number">{{ $documentNumber ?? 'DOC-' . date('Ymd-His') }}</div>
                <div class="document-date">
                    Generated: {{ $generatedAt->format('M j, Y \a\t g:i A') ?? now()->format('M j, Y \a\t g:i A') }}
                </div>
                @yield('header-right-extra')
            </div>
        </div>

        <!-- Barcode Section -->
        @if(isset($barcode))
        <div class="barcode-section">
            <div style="font-size: 11px; font-weight: bold;">Document ID</div>
            <div class="barcode">{{ $documentNumber ?? 'DOCUMENT' }}</div>
        </div>
        @endif

        <!-- Document Content -->
        <div class="document-content">
            @yield('content')
        </div>

        <!-- Document Footer -->
        <div class="document-footer">
            @yield('footer')
            <div>
                This document was generated automatically by {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }} on {{ now()->format('M j, Y \a\t g:i A') }}
            </div>
            @if(isset($siteSettings['company_footer_text']))
                <div style="margin-top: 10px;">{{ $siteSettings['company_footer_text'] }}</div>
            @endif
        </div>
    </div>

    @stack('document-scripts')
</body>
</html>
