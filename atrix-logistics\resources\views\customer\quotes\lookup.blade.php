@extends('layouts.customer')

@section('title', 'Quote Lookup')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Quote Lookup</h1>
                    <p class="text-muted">Search for quotes by quote number and email</p>
                </div>
                <div>
                    <a href="{{ route('customer.quotes.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>My Quotes
                    </a>
                </div>
            </div>

            <!-- Lookup Form -->
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-search me-2"></i>
                                Find Quote
                            </h5>
                        </div>
                        <div class="card-body">
                            @if(session('success'))
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ session('success') }}
                            </div>
                            @endif

                            @if($errors->any())
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @foreach($errors->all() as $error)
                                    <div>{{ $error }}</div>
                                @endforeach
                            </div>
                            @endif

                            <form method="POST" action="{{ route('customer.quotes.lookup.search') }}">
                                @csrf
                                <div class="mb-3">
                                    <label for="quote_number" class="form-label">
                                        <i class="fas fa-hashtag me-2"></i>Quote Number
                                    </label>
                                    <input type="text" class="form-control" id="quote_number" 
                                           name="quote_number" placeholder="e.g., QTE20240115001" 
                                           value="{{ old('quote_number') }}" required>
                                    <div class="form-text">
                                        Your quote number was provided in the confirmation email
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address
                                    </label>
                                    <input type="email" class="form-control" id="email" 
                                           name="email" placeholder="<EMAIL>" 
                                           value="{{ old('email', auth()->user()->email ?? '') }}" required>
                                    <div class="form-text">
                                        Enter the email address used when requesting the quote
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>
                                        Find Quote
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-question-circle me-2"></i>
                                Need Help?
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                        <h6>Email Support</h6>
                                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                                            <EMAIL>
                                        </a>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-phone fa-2x text-success mb-2"></i>
                                        <h6>Phone Support</h6>
                                        <a href="tel:+1234567890" class="btn btn-outline-success btn-sm">
                                            +****************
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Information Cards -->
            <div class="row mt-5">
                <div class="col-md-4 mb-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-3x text-primary mb-3"></i>
                            <h5>Quick Response</h5>
                            <p class="text-muted">We respond to all quote requests within 24 hours during business days</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-calculator fa-3x text-success mb-3"></i>
                            <h5>Competitive Pricing</h5>
                            <p class="text-muted">Get the best rates for your shipping and logistics needs</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100 text-center">
                        <div class="card-body">
                            <i class="fas fa-shield-alt fa-3x text-info mb-3"></i>
                            <h5>Secure & Reliable</h5>
                            <p class="text-muted">Your quotes and personal data are protected with enterprise-grade security</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.card {
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0 !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}
</style>
@endpush
