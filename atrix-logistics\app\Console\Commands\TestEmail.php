<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Mail\UserWelcome;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:email {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email functionality by sending a welcome email';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email') ?? '<EMAIL>';
        
        // Create a test user
        $testUser = new User([
            'name' => 'Test User',
            'email' => $email,
            'role' => 'customer',
        ]);
        
        $this->info("Sending test welcome email to: {$email}");
        
        try {
            Mail::to($email)->send(new UserWelcome($testUser, 'TestPassword123'));
            $this->info('✅ Email sent successfully!');
            $this->info('Check your Mailpit inbox at: http://localhost:8025');
        } catch (\Exception $e) {
            $this->error('❌ Failed to send email: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
