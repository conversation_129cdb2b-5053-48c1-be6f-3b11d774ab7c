<?php

namespace App\Exports;

use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\User;
use App\Helpers\CurrencyHelper;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;
use Illuminate\Http\Request;

class EcommerceAnalyticsExport implements WithMultipleSheets
{
    protected $request;
    protected $period;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
        $this->period = $request ? $request->get('period', 30) : 30;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        return [
            'Overview' => new OverviewSheet($this->request),
            'Products' => new ProductAnalyticsSheet($this->request),
            'Sales' => new SalesAnalyticsSheet($this->request),
            'Categories' => new CategoryPerformanceSheet($this->request),
            'Customers' => new TopCustomersSheet($this->request),
            'Inventory' => new InventorySheet($this->request),
        ];
    }
}

class OverviewSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Overview';
    }

    public function collection()
    {
        $period = $this->request ? $this->request->get('period', 30) : 30;
        $startDate = Carbon::now()->subDays($period);

        return collect([
            [
                'metric' => 'Total Products',
                'value' => Product::count(),
                'period_value' => Product::where('created_at', '>=', $startDate)->count(),
            ],
            [
                'metric' => 'Active Products',
                'value' => Product::where('is_active', true)->count(),
                'period_value' => Product::where('is_active', true)->where('created_at', '>=', $startDate)->count(),
            ],
            [
                'metric' => 'Total Categories',
                'value' => Category::count(),
                'period_value' => Category::where('created_at', '>=', $startDate)->count(),
            ],
            [
                'metric' => 'Total Orders',
                'value' => Order::count(),
                'period_value' => Order::where('created_at', '>=', $startDate)->count(),
            ],
            [
                'metric' => 'Paid Orders',
                'value' => Order::where('payment_status', 'paid')->count(),
                'period_value' => Order::where('payment_status', 'paid')->where('created_at', '>=', $startDate)->count(),
            ],
            [
                'metric' => 'Total Revenue (' . CurrencyHelper::getCode() . ')',
                'value' => Order::where('payment_status', 'paid')->sum('total_amount'),
                'period_value' => Order::where('payment_status', 'paid')->where('created_at', '>=', $startDate)->sum('total_amount'),
            ],
            [
                'metric' => 'Low Stock Products',
                'value' => Product::where('manage_stock', true)->whereColumn('stock_quantity', '<=', 'min_stock_level')->where('stock_quantity', '>', 0)->count(),
                'period_value' => 'N/A',
            ],
            [
                'metric' => 'Out of Stock Products',
                'value' => Product::where('manage_stock', true)->where('stock_quantity', 0)->count(),
                'period_value' => 'N/A',
            ],
        ]);
    }

    public function headings(): array
    {
        $period = $this->request ? $this->request->get('period', 30) : 30;
        return [
            'Metric',
            'Total Value',
            "Last {$period} Days",
        ];
    }

    public function map($row): array
    {
        return [
            $row['metric'],
            $row['value'],
            $row['period_value'],
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class ProductAnalyticsSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Product Analytics';
    }

    public function collection()
    {
        return Product::with(['category'])
                     ->selectRaw('products.*, (stock_quantity * price) as stock_value')
                     ->orderBy('stock_value', 'desc')
                     ->limit(100)
                     ->get();
    }

    public function headings(): array
    {
        return [
            'Product Name',
            'SKU',
            'Category',
            'Price (' . CurrencyHelper::getCode() . ')',
            'Stock Quantity',
            'Stock Value (' . CurrencyHelper::getCode() . ')',
            'Status',
            'Featured',
            'Created Date',
        ];
    }

    public function map($product): array
    {
        return [
            $product->name,
            $product->sku,
            $product->category->name ?? 'Uncategorized',
            $product->price,
            $product->manage_stock ? $product->stock_quantity : 'N/A',
            $product->manage_stock ? ($product->stock_quantity * $product->price) : 'N/A',
            $product->is_active ? 'Active' : 'Inactive',
            $product->is_featured ? 'Yes' : 'No',
            $product->created_at->format('Y-m-d'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class SalesAnalyticsSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Sales Analytics';
    }

    public function collection()
    {
        $period = $this->request ? $this->request->get('period', 30) : 30;
        $startDate = Carbon::now()->subDays($period);

        return Order::with(['customer'])
                   ->where('created_at', '>=', $startDate)
                   ->orderBy('total_amount', 'desc')
                   ->get();
    }

    public function headings(): array
    {
        return [
            'Order Number',
            'Customer',
            'Status',
            'Payment Status',
            'Total Amount (' . CurrencyHelper::getCode() . ')',
            'Items Count',
            'Order Date',
        ];
    }

    public function map($order): array
    {
        return [
            $order->order_number,
            $order->customer->name ?? 'Guest',
            ucfirst(str_replace('_', ' ', $order->status)),
            ucfirst(str_replace('_', ' ', $order->payment_status)),
            $order->total_amount,
            $order->items->count(),
            $order->created_at->format('Y-m-d H:i:s'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class CategoryPerformanceSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Category Performance';
    }

    public function collection()
    {
        return Category::withCount('products')
                      ->with(['products' => function($query) {
                          $query->where('is_active', true);
                      }])
                      ->where('is_active', true)
                      ->orderBy('products_count', 'desc')
                      ->get()
                      ->map(function($category) {
                          $category->avg_price = $category->products->avg('price') ?? 0;
                          $category->total_stock_value = $category->products->sum(function($product) {
                              return $product->manage_stock ? ($product->stock_quantity * $product->price) : 0;
                          });
                          return $category;
                      });
    }

    public function headings(): array
    {
        return [
            'Category Name',
            'Products Count',
            'Average Price (' . CurrencyHelper::getCode() . ')',
            'Total Stock Value (' . CurrencyHelper::getCode() . ')',
            'Status',
            'Created Date',
        ];
    }

    public function map($category): array
    {
        return [
            $category->name,
            $category->products_count,
            round($category->avg_price, 2),
            round($category->total_stock_value, 2),
            $category->is_active ? 'Active' : 'Inactive',
            $category->created_at->format('Y-m-d'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class TopCustomersSheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Top Customers';
    }

    public function collection()
    {
        return User::where('role', 'customer')
                  ->withCount(['orders' => function($query) {
                      $query->where('payment_status', 'paid');
                  }])
                  ->withSum(['orders as total_spent' => function($query) {
                      $query->where('payment_status', 'paid');
                  }], 'total_amount')
                  ->orderBy('total_spent', 'desc')
                  ->limit(50)
                  ->get();
    }

    public function headings(): array
    {
        return [
            'Customer Name',
            'Email',
            'Orders Count',
            'Total Spent (' . CurrencyHelper::getCode() . ')',
            'Average Order Value (' . CurrencyHelper::getCode() . ')',
            'Registration Date',
        ];
    }

    public function map($customer): array
    {
        $avgOrderValue = $customer->orders_count > 0 ? ($customer->total_spent / $customer->orders_count) : 0;

        return [
            $customer->name,
            $customer->email,
            $customer->orders_count,
            $customer->total_spent ?? 0,
            round($avgOrderValue, 2),
            $customer->created_at->format('Y-m-d'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}

class InventorySheet implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    public function title(): string
    {
        return 'Inventory Status';
    }

    public function collection()
    {
        return Product::where('manage_stock', true)
                     ->with(['category'])
                     ->orderBy('stock_quantity', 'asc')
                     ->get();
    }

    public function headings(): array
    {
        return [
            'Product Name',
            'SKU',
            'Category',
            'Current Stock',
            'Min Stock Level',
            'Stock Status',
            'Price (' . CurrencyHelper::getCode() . ')',
            'Stock Value (' . CurrencyHelper::getCode() . ')',
            'Last Updated',
        ];
    }

    public function map($product): array
    {
        $stockStatus = 'In Stock';
        if ($product->stock_quantity == 0) {
            $stockStatus = 'Out of Stock';
        } elseif ($product->stock_quantity <= $product->min_stock_level) {
            $stockStatus = 'Low Stock';
        }

        return [
            $product->name,
            $product->sku,
            $product->category->name ?? 'Uncategorized',
            $product->stock_quantity,
            $product->min_stock_level,
            $stockStatus,
            $product->price,
            $product->stock_quantity * $product->price,
            $product->updated_at->format('Y-m-d H:i:s'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
