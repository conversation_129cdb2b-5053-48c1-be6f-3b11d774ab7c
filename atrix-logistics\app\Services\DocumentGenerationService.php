<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Quote;
use App\Models\Parcel;
use App\Models\SiteSetting;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class DocumentGenerationService
{
    protected array $siteSettings;

    public function __construct()
    {
        $this->siteSettings = cache()->remember('site_settings', 3600, function () {
            return SiteSetting::pluck('value', 'key_name')->toArray();
        });
    }

    /**
     * Generate invoice PDF
     */
    public function generateInvoice(Order $order): string
    {
        $data = [
            'order' => $order->load(['customer', 'items.product', 'shippingAddress', 'billingAddress']),
            'siteSettings' => $this->siteSettings,
            'documentType' => 'Invoice',
            'documentNumber' => $order->order_number,
            'barcode' => $this->generateBarcode($order->order_number),
            'generatedAt' => now(),
        ];

        $pdf = Pdf::loadView('documents.invoice', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        $filename = "invoice_{$order->order_number}_" . now()->format('Y-m-d') . ".pdf";
        $path = "documents/invoices/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate quote PDF
     */
    public function generateQuote(Quote $quote): string
    {
        $data = [
            'quote' => $quote->load(['customer', 'assignedTo']),
            'siteSettings' => $this->siteSettings,
            'documentType' => 'Quote',
            'documentNumber' => $quote->quote_number,
            'barcode' => $this->generateBarcode($quote->quote_number),
            'generatedAt' => now(),
        ];

        $pdf = Pdf::loadView('documents.quote', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        $filename = "quote_{$quote->quote_number}_" . now()->format('Y-m-d') . ".pdf";
        $path = "documents/quotes/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate waybill PDF
     */
    public function generateWaybill(Parcel $parcel): string
    {
        $data = [
            'parcel' => $parcel->load(['customer', 'trackingEvents']),
            'siteSettings' => $this->siteSettings,
            'documentType' => 'Waybill',
            'documentNumber' => $parcel->tracking_number,
            'barcode' => $this->generateBarcode($parcel->tracking_number),
            'generatedAt' => now(),
        ];

        $pdf = Pdf::loadView('documents.waybill', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        $filename = "waybill_{$parcel->tracking_number}_" . now()->format('Y-m-d') . ".pdf";
        $path = "documents/waybills/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate shipping label PDF
     */
    public function generateShippingLabel(Parcel $parcel): string
    {
        $data = [
            'parcel' => $parcel->load(['customer']),
            'siteSettings' => $this->siteSettings,
            'documentType' => 'Shipping Label',
            'documentNumber' => $parcel->tracking_number,
            'barcode' => $this->generateBarcode($parcel->tracking_number),
            'generatedAt' => now(),
        ];

        $pdf = Pdf::loadView('documents.shipping-label', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        $filename = "shipping_label_{$parcel->tracking_number}_" . now()->format('Y-m-d') . ".pdf";
        $path = "documents/shipping-labels/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate delivery receipt PDF
     */
    public function generateDeliveryReceipt(Parcel $parcel): string
    {
        $data = [
            'parcel' => $parcel->load(['customer', 'trackingEvents']),
            'siteSettings' => $this->siteSettings,
            'documentType' => 'Delivery Receipt',
            'documentNumber' => $parcel->tracking_number,
            'barcode' => $this->generateBarcode($parcel->tracking_number),
            'generatedAt' => now(),
        ];

        $pdf = Pdf::loadView('documents.delivery-receipt', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        $filename = "delivery_receipt_{$parcel->tracking_number}_" . now()->format('Y-m-d') . ".pdf";
        $path = "documents/delivery-receipts/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate analytics report PDF
     */
    public function generateAnalyticsReport(array $data, string $reportType = 'analytics'): string
    {
        $reportData = array_merge($data, [
            'siteSettings' => $this->siteSettings,
            'documentType' => 'Analytics Report',
            'documentNumber' => 'RPT-' . now()->format('Ymd-His'),
            'barcode' => $this->generateBarcode('RPT-' . now()->format('Ymd-His')),
            'generatedAt' => now(),
        ]);

        $pdf = Pdf::loadView('documents.analytics-report', $reportData)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'dpi' => 150,
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
            ]);

        $filename = "{$reportType}_report_" . now()->format('Y-m-d_H-i-s') . ".pdf";
        $path = "documents/reports/{$filename}";
        
        Storage::disk('public')->put($path, $pdf->output());
        
        return $path;
    }

    /**
     * Generate barcode for document
     */
    protected function generateBarcode(string $text): string
    {
        // Simple barcode representation using Code 128 pattern
        // In a real implementation, you might use a barcode library
        return base64_encode($text);
    }

    /**
     * Get document download URL
     */
    public function getDocumentUrl(string $path): string
    {
        return Storage::disk('public')->url($path);
    }

    /**
     * Delete document
     */
    public function deleteDocument(string $path): bool
    {
        return Storage::disk('public')->delete($path);
    }

    /**
     * Get document storage path
     */
    public function getDocumentPath(string $filename, string $type = 'general'): string
    {
        return "documents/{$type}/{$filename}";
    }

    /**
     * Generate unique document filename
     */
    public function generateDocumentFilename(string $prefix, string $identifier, string $extension = 'pdf'): string
    {
        return "{$prefix}_{$identifier}_" . now()->format('Y-m-d_H-i-s') . ".{$extension}";
    }

    /**
     * Ensure document directories exist
     */
    public function ensureDirectoriesExist(): void
    {
        $directories = [
            'documents/invoices',
            'documents/quotes',
            'documents/waybills',
            'documents/shipping-labels',
            'documents/delivery-receipts',
            'documents/reports',
        ];

        foreach ($directories as $directory) {
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }
        }
    }

    /**
     * Get document metadata
     */
    public function getDocumentMetadata(string $path): array
    {
        if (!Storage::disk('public')->exists($path)) {
            return [];
        }

        return [
            'size' => Storage::disk('public')->size($path),
            'lastModified' => Storage::disk('public')->lastModified($path),
            'url' => Storage::disk('public')->url($path),
            'exists' => true,
        ];
    }
}
