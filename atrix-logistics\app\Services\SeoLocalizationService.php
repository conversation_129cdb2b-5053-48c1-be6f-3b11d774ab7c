<?php

namespace App\Services;

use App\Models\SiteSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;

class SeoLocalizationService
{
    /**
     * Get the current locale based on URL, user preference, or browser detection
     */
    public function getCurrentLocale(): string
    {
        // Try to get locale from URL first
        $urlLocale = $this->getLocaleFromUrl();
        if ($urlLocale) {
            return $urlLocale;
        }

        // Try to get from session
        $sessionLocale = session('locale');
        if ($sessionLocale && $this->isLocaleSupported($sessionLocale)) {
            return $sessionLocale;
        }

        // Auto-detect from browser if enabled
        if (SiteSetting::getValue('auto_detect_language', true)) {
            $browserLocale = $this->detectBrowserLocale();
            if ($browserLocale) {
                return $browserLocale;
            }
        }

        // Fallback to default locale
        return SiteSetting::getValue('default_locale', 'en-US');
    }

    /**
     * Get supported locales configuration
     */
    public function getSupportedLocales(): array
    {
        return Cache::remember('supported_locales', 3600, function () {
            $locales = SiteSetting::getValue('supported_locales', []);
            return is_string($locales) ? json_decode($locales, true) : $locales;
        });
    }

    /**
     * Check if a locale is supported
     */
    public function isLocaleSupported(string $locale): bool
    {
        $supportedLocales = $this->getSupportedLocales();
        return isset($supportedLocales[$locale]) && $supportedLocales[$locale]['enabled'];
    }

    /**
     * Generate hreflang tags for the current page
     */
    public function generateHreflangTags(string $currentUrl = null): array
    {
        if (!SiteSetting::getValue('enable_hreflang', true)) {
            return [];
        }

        $currentUrl = $currentUrl ?: Request::url();
        $supportedLocales = $this->getSupportedLocales();
        $hreflangTags = [];

        foreach ($supportedLocales as $locale => $config) {
            if (!$config['enabled']) {
                continue;
            }

            $localizedUrl = $this->getLocalizedUrl($currentUrl, $locale);
            $hreflangTags[] = [
                'hreflang' => $locale,
                'href' => $localizedUrl
            ];
        }

        // Add x-default if configured
        $xDefaultUrl = SiteSetting::getValue('hreflang_x_default');
        if ($xDefaultUrl) {
            $hreflangTags[] = [
                'hreflang' => 'x-default',
                'href' => $xDefaultUrl
            ];
        }

        return $hreflangTags;
    }

    /**
     * Get localized meta title
     */
    public function getLocalizedMetaTitle(string $locale = null, string $fallback = null): string
    {
        $locale = $locale ?: $this->getCurrentLocale();
        $language = $this->getLanguageFromLocale($locale);
        
        $localizedTitles = SiteSetting::getValue('localized_meta_titles', []);
        if (is_string($localizedTitles)) {
            $localizedTitles = json_decode($localizedTitles, true);
        }

        // Try exact locale match first
        if (isset($localizedTitles[$locale])) {
            return $localizedTitles[$locale];
        }

        // Try language match
        if (isset($localizedTitles[$language])) {
            return $localizedTitles[$language];
        }

        // Fallback to provided fallback or default
        return $fallback ?: SiteSetting::getValue('site_title', 'Atrix Logistics');
    }

    /**
     * Get localized meta description
     */
    public function getLocalizedMetaDescription(string $locale = null, string $fallback = null): string
    {
        $locale = $locale ?: $this->getCurrentLocale();
        $language = $this->getLanguageFromLocale($locale);
        
        $localizedDescriptions = SiteSetting::getValue('localized_meta_descriptions', []);
        if (is_string($localizedDescriptions)) {
            $localizedDescriptions = json_decode($localizedDescriptions, true);
        }

        // Try exact locale match first
        if (isset($localizedDescriptions[$locale])) {
            return $localizedDescriptions[$locale];
        }

        // Try language match
        if (isset($localizedDescriptions[$language])) {
            return $localizedDescriptions[$language];
        }

        // Fallback to provided fallback or default
        return $fallback ?: SiteSetting::getValue('site_description', 'Professional logistics solutions worldwide');
    }

    /**
     * Get currency for current locale
     */
    public function getCurrencyForLocale(string $locale = null): string
    {
        if (!SiteSetting::getValue('locale_based_currency', false)) {
            return SiteSetting::getValue('default_currency', 'USD');
        }

        $locale = $locale ?: $this->getCurrentLocale();
        $currencyMap = SiteSetting::getValue('currency_by_locale', []);
        
        if (is_string($currencyMap)) {
            $currencyMap = json_decode($currencyMap, true);
        }

        return $currencyMap[$locale] ?? SiteSetting::getValue('default_currency', 'USD');
    }

    /**
     * Get localized organization name
     */
    public function getLocalizedOrganizationName(string $locale = null): string
    {
        $locale = $locale ?: $this->getCurrentLocale();
        $language = $this->getLanguageFromLocale($locale);
        
        $localizedNames = SiteSetting::getValue('organization_name_localized', []);
        if (is_string($localizedNames)) {
            $localizedNames = json_decode($localizedNames, true);
        }

        // Try exact locale match first
        if (isset($localizedNames[$locale])) {
            return $localizedNames[$locale];
        }

        // Try language match
        if (isset($localizedNames[$language])) {
            return $localizedNames[$language];
        }

        // Fallback to default site name
        return SiteSetting::getValue('site_name', 'Atrix Logistics');
    }

    /**
     * Extract language code from locale (e.g., 'en' from 'en-US')
     */
    private function getLanguageFromLocale(string $locale): string
    {
        return explode('-', $locale)[0];
    }

    /**
     * Get locale from current URL
     */
    private function getLocaleFromUrl(): ?string
    {
        $urlStructure = SiteSetting::getValue('url_structure_type', 'subdirectory');
        
        switch ($urlStructure) {
            case 'subdirectory':
                $segments = Request::segments();
                if (!empty($segments)) {
                    $potentialLocale = $segments[0];
                    // Check if it's a valid locale format (e.g., 'en', 'en-US')
                    if (preg_match('/^[a-z]{2}(-[A-Z]{2})?$/', $potentialLocale)) {
                        return $this->isLocaleSupported($potentialLocale) ? $potentialLocale : null;
                    }
                }
                break;
                
            case 'parameter':
                return Request::get('lang');
                
            case 'subdomain':
                $host = Request::getHost();
                $parts = explode('.', $host);
                if (count($parts) >= 3) {
                    $subdomain = $parts[0];
                    return $this->isLocaleSupported($subdomain) ? $subdomain : null;
                }
                break;
        }

        return null;
    }

    /**
     * Detect browser locale
     */
    private function detectBrowserLocale(): ?string
    {
        $acceptLanguage = Request::header('Accept-Language');
        if (!$acceptLanguage) {
            return null;
        }

        $supportedLocales = array_keys($this->getSupportedLocales());
        
        // Parse Accept-Language header
        $languages = [];
        foreach (explode(',', $acceptLanguage) as $lang) {
            $parts = explode(';', trim($lang));
            $locale = trim($parts[0]);
            $quality = 1.0;
            
            if (isset($parts[1]) && strpos($parts[1], 'q=') === 0) {
                $quality = (float) substr($parts[1], 2);
            }
            
            $languages[$locale] = $quality;
        }

        // Sort by quality
        arsort($languages);

        // Find best match
        foreach ($languages as $locale => $quality) {
            // Try exact match first
            if (in_array($locale, $supportedLocales)) {
                return $locale;
            }
            
            // Try language match (e.g., 'en' for 'en-US')
            $language = explode('-', $locale)[0];
            foreach ($supportedLocales as $supportedLocale) {
                if (strpos($supportedLocale, $language . '-') === 0) {
                    return $supportedLocale;
                }
            }
        }

        return null;
    }

    /**
     * Get localized URL for a given locale
     */
    private function getLocalizedUrl(string $url, string $locale): string
    {
        $supportedLocales = $this->getSupportedLocales();
        
        if (isset($supportedLocales[$locale]['url']) && !empty($supportedLocales[$locale]['url'])) {
            return $supportedLocales[$locale]['url'];
        }

        $urlStructure = SiteSetting::getValue('url_structure_type', 'subdirectory');
        $hideDefault = SiteSetting::getValue('hide_default_locale_in_url', true);
        $defaultLocale = SiteSetting::getValue('default_locale', 'en-US');

        switch ($urlStructure) {
            case 'subdirectory':
                if ($hideDefault && $locale === $defaultLocale) {
                    return $url;
                }
                return preg_replace('/^(https?:\/\/[^\/]+)/', '$1/' . $locale, $url);
                
            case 'subdomain':
                if ($hideDefault && $locale === $defaultLocale) {
                    return $url;
                }
                return preg_replace('/^(https?:\/\/)([^.]+\.)/', '$1' . $locale . '.', $url);
                
            case 'parameter':
                $separator = strpos($url, '?') !== false ? '&' : '?';
                return $url . $separator . 'lang=' . $locale;
                
            default:
                return $url;
        }
    }
}
