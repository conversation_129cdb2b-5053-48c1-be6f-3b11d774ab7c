<?php

namespace Tests\Feature;

use App\Models\Cart;
use App\Models\Category;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EcommerceFlowTest extends TestCase
{
    use WithFaker;

    protected User $user;
    protected Product $product1;
    protected Product $product2;
    protected Category $category;
    protected UserAddress $address;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed essential data
        $this->seed([
            \Database\Seeders\SiteSettingSeeder::class,
        ]);

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'customer',
            'email_verified_at' => now(),
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'is_active' => true,
        ]);

        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Electronics',
            'is_active' => true,
        ]);

        // Create test products
        $this->product1 = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Laptop',
            'price' => 999.99,
            'sale_price' => null, // Ensure no sale price
            'stock_quantity' => 5,
            'manage_stock' => true,
            'is_active' => true,
        ]);

        $this->product2 = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Mouse',
            'price' => 29.99,
            'sale_price' => null, // Ensure no sale price
            'stock_quantity' => 20,
            'manage_stock' => true,
            'is_active' => true,
        ]);

        // Create test address (both shipping and billing)
        $this->address = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'both',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address_line_1' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10001',
            'country' => 'USA',
            'is_default' => true,
        ]);
    }

    /** @test */
    public function complete_ecommerce_flow_with_manual_payment()
    {
        // Step 1: User logs in
        $this->actingAs($this->user);

        // Step 2: Add products to cart
        $response = $this->postJson('/cart/add', [
            'product_id' => $this->product1->id,
            'quantity' => 1,
        ]);
        $response->assertStatus(200);

        $response = $this->postJson('/cart/add', [
            'product_id' => $this->product2->id,
            'quantity' => 2,
        ]);
        $response->assertStatus(200);

        // Step 3: Verify cart contents
        $cart = Cart::getCurrent();
        $this->assertEquals(3, $cart->item_count); // 1 laptop + 2 mice
        $this->assertEquals(1059.97, $cart->total_amount); // 999.99 + (29.99 * 2)

        // Step 4: Update cart (change mouse quantity)
        $response = $this->postJson('/cart/update', [
            'product_id' => $this->product2->id,
            'quantity' => 3,
        ]);
        $response->assertStatus(200);

        $cart->refresh();
        $this->assertEquals(4, $cart->item_count); // 1 laptop + 3 mice
        $this->assertEquals(1089.96, $cart->total_amount); // 999.99 + (29.99 * 3)

        // Step 5: View cart page
        $response = $this->get('/cart');
        $response->assertStatus(200)
            ->assertSee('Laptop')
            ->assertSee('Mouse')
            ->assertSee('$1,089.96');

        // Step 6: Proceed to checkout
        $response = $this->get('/checkout');
        $response->assertStatus(200)
            ->assertSee('Laptop')
            ->assertSee('Mouse')
            ->assertSee('$1,089.96');

        // Step 7: Place order with manual payment
        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->address->id,
            'billing_address_id' => $this->address->id,
            'payment_method' => 'manual',
            'notes' => 'Please deliver during business hours',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully.',
            ]);

        // Step 8: Verify order was created correctly
        $order = Order::where('customer_id', $this->user->id)->first();
        $this->assertNotNull($order);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals('manual', $order->payment_method);
        $this->assertEquals(1089.96, $order->total_amount);
        $this->assertEquals('Please deliver during business hours', $order->notes);

        // Verify order items
        $this->assertEquals(2, $order->items()->count());
        
        $laptopItem = $order->items()->where('product_id', $this->product1->id)->first();
        $this->assertEquals(1, $laptopItem->quantity);
        $this->assertEquals(999.99, $laptopItem->unit_price);
        
        $mouseItem = $order->items()->where('product_id', $this->product2->id)->first();
        $this->assertEquals(3, $mouseItem->quantity);
        $this->assertEquals(29.99, $mouseItem->unit_price);

        // Step 9: Verify stock was decremented
        $this->product1->refresh();
        $this->product2->refresh();
        $this->assertEquals(4, $this->product1->stock_quantity); // 5 - 1
        $this->assertEquals(17, $this->product2->stock_quantity); // 20 - 3

        // Step 10: Verify cart was cleared
        $cart->refresh();
        $this->assertTrue($cart->isEmpty());

        // Step 11: View order confirmation
        $response = $this->get("/checkout/confirmation/{$order->id}");
        $response->assertStatus(200)
            ->assertSee($order->order_number)
            ->assertSee('Order Confirmed!')
            ->assertSee('$1,089.96')
            ->assertSee('Manual Payment')
            ->assertSee('Payment instructions will be sent via email');
    }

    /** @test */
    public function complete_ecommerce_flow_with_paypal_payment()
    {
        $this->actingAs($this->user);

        // Add product to cart
        $this->postJson('/cart/add', [
            'product_id' => $this->product1->id,
            'quantity' => 1,
        ]);

        // Place order with PayPal
        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->address->id,
            'billing_address_id' => $this->address->id,
            'payment_method' => 'paypal',
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        // Verify PayPal redirect is provided
        $this->assertEquals('paypal', $responseData['payment_response']['type']);
        $this->assertStringContainsString('pay', $responseData['payment_response']['redirect']);

        // Verify order was created
        $order = Order::where('customer_id', $this->user->id)->first();
        $this->assertEquals('paypal', $order->payment_method);
        $this->assertEquals('pending', $order->payment_status);
    }

    /** @test */
    public function complete_ecommerce_flow_with_stripe_payment()
    {
        $this->actingAs($this->user);

        // Add product to cart
        $this->postJson('/cart/add', [
            'product_id' => $this->product1->id,
            'quantity' => 1,
        ]);

        // Place order with Stripe
        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->address->id,
            'billing_address_id' => $this->address->id,
            'payment_method' => 'stripe',
        ]);

        $response->assertStatus(200);
        $responseData = $response->json();

        // Verify Stripe redirect is provided
        $this->assertEquals('stripe', $responseData['payment_response']['type']);
        $this->assertStringContainsString('pay', $responseData['payment_response']['redirect']);

        // Verify order was created
        $order = Order::where('customer_id', $this->user->id)->first();
        $this->assertEquals('stripe', $order->payment_method);
        $this->assertEquals('pending', $order->payment_status);
    }

    /** @test */
    public function user_can_edit_cart_multiple_times_before_checkout()
    {
        $this->actingAs($this->user);

        // Add initial products
        $this->postJson('/cart/add', ['product_id' => $this->product1->id, 'quantity' => 2]);
        $this->postJson('/cart/add', ['product_id' => $this->product2->id, 'quantity' => 1]);

        $cart = Cart::getCurrent();
        $this->assertEquals(3, $cart->item_count);

        // Update quantities
        $this->postJson('/cart/update', ['product_id' => $this->product1->id, 'quantity' => 1]);
        $this->postJson('/cart/update', ['product_id' => $this->product2->id, 'quantity' => 5]);

        $cart->refresh();
        $this->assertEquals(6, $cart->item_count); // 1 + 5

        // Remove one product
        $this->postJson('/cart/remove', ['product_id' => $this->product1->id]);

        $cart->refresh();
        $this->assertEquals(5, $cart->item_count); // Only mice left
        $this->assertEquals(1, $cart->items()->count()); // Only one product type

        // Add product back
        $this->postJson('/cart/add', ['product_id' => $this->product1->id, 'quantity' => 1]);

        $cart->refresh();
        $this->assertEquals(6, $cart->item_count); // 1 laptop + 5 mice
        $this->assertEquals(2, $cart->items()->count()); // Two product types
    }

    /** @test */
    public function stock_management_works_correctly_during_checkout()
    {
        $this->actingAs($this->user);

        // Set low stock for product1
        $this->product1->update(['stock_quantity' => 2]);

        // Try to add more than available stock
        $response = $this->postJson('/cart/add', [
            'product_id' => $this->product1->id,
            'quantity' => 3,
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Insufficient stock. Only 2 items available.',
            ]);

        // Add available stock
        $this->postJson('/cart/add', [
            'product_id' => $this->product1->id,
            'quantity' => 2,
        ]);

        // Complete checkout
        $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->address->id,
            'billing_address_id' => $this->address->id,
            'payment_method' => 'manual',
        ]);

        // Verify stock was decremented to 0
        $this->product1->refresh();
        $this->assertEquals(0, $this->product1->stock_quantity);
    }
}
