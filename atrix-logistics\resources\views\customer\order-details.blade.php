@extends('layouts.customer')

@section('title', 'Order Details')
@section('page-title', 'Order #' . $order->order_number)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.orders') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Orders
        </a>
        @if($order->canBeTracked())
            <a href="{{ route('customer.track') }}?tracking_number={{ $order->tracking_number }}" 
               class="btn btn-primary">
                <i class="fas fa-map-marker-alt me-1"></i> Track Order
            </a>
        @endif
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cog me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item" href="#" onclick="printOrder()">
                        <i class="fas fa-print me-2"></i> Print Order
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="{{ route('customer.support.create') }}?order_number={{ $order->order_number }}">
                        <i class="fas fa-headset me-2"></i> Get Support
                    </a>
                </li>
                @if($order->canBeReordered())
                    <li>
                        <a class="dropdown-item" href="#" onclick="reorderItems()">
                            <i class="fas fa-redo me-2"></i> Reorder Items
                        </a>
                    </li>
                @endif
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-8">
            <!-- Order Status -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Order Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Status</label>
                                <div>
                                    <span class="badge bg-{{ $order->status_badge_color }} fs-6">
                                        {{ $order->formatted_status }}
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label fw-bold">Payment Status</label>
                                <div>
                                    <span class="badge bg-{{ $order->payment_status_badge_color }} fs-6">
                                        {{ $order->formatted_payment_status }}
                                    </span>
                                </div>
                            </div>

                            @if($order->payment_method)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Payment Method</label>
                                    <p class="mb-0">
                                        @switch($order->payment_method)
                                            @case('manual')
                                                <i class="fas fa-money-bill-wave text-success me-2"></i>Manual Payment
                                                @break
                                            @case('paypal')
                                                <i class="fab fa-paypal text-primary me-2"></i>PayPal
                                                @break
                                            @case('stripe')
                                                <i class="fas fa-credit-card text-info me-2"></i>Credit/Debit Card
                                                @break
                                            @default
                                                <i class="fas fa-question-circle text-muted me-2"></i>{{ ucfirst($order->payment_method) }}
                                        @endswitch
                                        @if($order->payment_reference)
                                            <br><small class="text-muted">Reference: {{ $order->payment_reference }}</small>
                                        @endif
                                    </p>
                                </div>
                            @endif
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Order Date</label>
                                <p class="mb-0">
                                    {{ $order->created_at->format('M d, Y h:i A') }}<br>
                                    <small class="text-muted">{{ $order->created_at->diffForHumans() }}</small>
                                </p>
                            </div>

                            @if($order->tracking_number)
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Tracking Number</label>
                                    <p class="mb-0">
                                        <code>{{ $order->tracking_number }}</code>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $order->tracking_number }}')">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Order Items ({{ $order->items->count() }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->items as $item)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($item->product && $item->product->featured_image)
                                                    <img src="{{ Storage::url($item->product->featured_image) }}" 
                                                         alt="{{ $item->product_name }}" 
                                                         class="me-3" 
                                                         style="width: 60px; height: 60px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 60px; height: 60px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <strong>{{ $item->product_name }}</strong>
                                                    @if($item->product_description)
                                                        <br><small class="text-muted">{{ $item->product_description }}</small>
                                                    @endif
                                                    @if($item->product_sku)
                                                        <br><code class="small">{{ $item->product_sku }}</code>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @currency($item->unit_price)
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $item->quantity }}</span>
                                        </td>
                                        <td>
                                            <strong>@currency($item->total_price)</strong>
                                        </td>
                                        <td>
                                            @if($item->product)
                                                <a href="#" class="btn btn-sm btn-outline-primary" onclick="addToWishlist({{ $item->product_id }})">
                                                    <i class="fas fa-heart"></i>
                                                </a>
                                                <a href="#" class="btn btn-sm btn-outline-success" onclick="addToCart({{ $item->product_id }})">
                                                    <i class="fas fa-cart-plus"></i>
                                                </a>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Shipping Address -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Shipping Address
                    </h6>
                </div>
                <div class="card-body">
                    <address class="mb-0">
                        <strong>{{ $order->shipping_first_name }} {{ $order->shipping_last_name }}</strong><br>
                        @if($order->shipping_company)
                            {{ $order->shipping_company }}<br>
                        @endif
                        {{ $order->shipping_address_1 }}<br>
                        @if($order->shipping_address_2)
                            {{ $order->shipping_address_2 }}<br>
                        @endif
                        {{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}<br>
                        {{ $order->shipping_country }}
                    </address>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        Order Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>@currency($order->subtotal)</span>
                    </div>
                    
                    @if($order->shipping_amount > 0)
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span>@currency($order->shipping_amount)</span>
                        </div>
                    @endif
                    
                    @if($order->tax_amount > 0)
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>@currency($order->tax_amount)</span>
                        </div>
                    @endif
                    
                    @if($order->discount_amount > 0)
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Discount:</span>
                            <span>-@currency($order->discount_amount)</span>
                        </div>
                    @endif
                    
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>Total:</strong>
                        <strong class="h5">@currency($order->total_amount)</strong>
                    </div>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Order Timeline
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Order Placed</h6>
                                <p class="mb-0 text-muted">{{ $order->created_at->format('M d, Y h:i A') }}</p>
                            </div>
                        </div>
                        
                        @if($order->paid_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Payment Confirmed</h6>
                                    <p class="mb-0 text-muted">{{ $order->paid_at->format('M d, Y h:i A') }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($order->shipped_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-info"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Shipped</h6>
                                    <p class="mb-0 text-muted">{{ $order->shipped_at->format('M d, Y h:i A') }}</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($order->delivered_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Delivered</h6>
                                    <p class="mb-0 text-muted">{{ $order->delivered_at->format('M d, Y h:i A') }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($order->canBePaid())
                            <a href="{{ route('customer.orders.pay', $order) }}"
                               class="btn btn-success btn-lg">
                                <i class="fas fa-credit-card me-2"></i>
                                Pay Now - @currency($order->total_amount)
                            </a>
                        @elseif($order->needsPayment())
                            <div class="alert alert-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Payment Required</strong><br>
                                <small>This order requires payment to proceed.</small>
                            </div>
                            <a href="{{ route('customer.orders.pay', $order) }}"
                               class="btn btn-warning">
                                <i class="fas fa-credit-card me-2"></i>
                                Complete Payment
                            </a>
                        @elseif($order->isPaid())
                            <div class="alert alert-success mb-3">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>Payment Completed</strong><br>
                                <small>Paid on {{ $order->paid_at->format('M d, Y') }}</small>
                            </div>
                        @endif

                        @if($order->canBeTracked())
                            <a href="{{ route('customer.track') }}?tracking_number={{ $order->tracking_number }}"
                               class="btn btn-primary">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Track Order
                            </a>
                        @endif

                        <a href="{{ route('customer.support.create') }}?order_number={{ $order->order_number }}"
                           class="btn btn-outline-warning">
                            <i class="fas fa-headset me-2"></i>
                            Get Support
                        </a>

                        @if($order->canBeReordered())
                            <button type="button" class="btn btn-outline-success" onclick="reorderItems()">
                                <i class="fas fa-redo me-2"></i>
                                Reorder Items
                            </button>
                        @endif

                        <button type="button" class="btn btn-outline-secondary" onclick="printOrder()">
                            <i class="fas fa-print me-2"></i>
                            Print Order
                        </button>

                        @if($order->canBeCancelled())
                            <button type="button" class="btn btn-outline-danger" onclick="cancelOrder()">
                                <i class="fas fa-times me-2"></i>
                                Cancel Order
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -22px;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #fff;
        box-shadow: 0 0 0 2px #dee2e6;
    }
    
    .timeline-content h6 {
        margin-bottom: 5px;
        font-size: 0.9rem;
    }
    
    .timeline-content p {
        font-size: 0.8rem;
    }
</style>
@endpush

@push('scripts')
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.style.zIndex = '9999';
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        });
    }

    function printOrder() {
        window.print();
    }

    function addToWishlist(productId) {
        // Implementation for adding to wishlist
        alert('Add to wishlist functionality coming soon!');
    }

    function addToCart(productId) {
        // Implementation for adding to cart
        alert('Add to cart functionality coming soon!');
    }

    function reorderItems() {
        if (confirm('Are you sure you want to reorder all items from this order?')) {
            // Create a form to submit the reorder request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route('customer.orders.reorder', $order) }}';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add to body and submit
            document.body.appendChild(form);
            form.submit();
        }
    }

    function cancelOrder() {
        if (confirm('Are you sure you want to cancel this order?\n\nThis action cannot be undone. If you have already paid, a refund will be processed according to our refund policy.')) {
            // Create a form to submit the cancellation request
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route('customer.orders.cancel', $order) }}';

            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);

            // Add to body and submit
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endpush
