@extends('layouts.admin')

@section('page-title', 'Customer Details - ' . $customer->name)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Customers
        </a>
        <a href="{{ route('admin.customers.edit', $customer) }}" class="btn btn-outline-warning">
            <i class="fas fa-edit me-1"></i> Edit Customer
        </a>
        <button type="button" class="btn btn-outline-{{ $customer->is_active ? 'danger' : 'success' }}" 
                onclick="toggleStatus({{ $customer->id }})">
            <i class="fas fa-{{ $customer->is_active ? 'ban' : 'check' }} me-1"></i> 
            {{ $customer->is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="fas fa-cogs me-1"></i> Actions
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="mailto:{{ $customer->email }}">
                    <i class="fas fa-envelope me-2"></i> Send Email
                </a></li>
                @if($customer->phone)
                <li><a class="dropdown-item" href="tel:{{ $customer->phone }}">
                    <i class="fas fa-phone me-2"></i> Call Customer
                </a></li>
                @endif
                <li><hr class="dropdown-divider"></li>
                <li><button class="dropdown-item" onclick="exportCustomerData()">
                    <i class="fas fa-download me-2"></i> Export Data
                </button></li>
                @if($customerStats['total_orders'] == 0)
                <li><hr class="dropdown-divider"></li>
                <li><button class="dropdown-item text-danger" onclick="deleteCustomer()">
                    <i class="fas fa-trash me-2"></i> Delete Customer
                </button></li>
                @endif
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <!-- Customer Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1">
                                <i class="fas fa-user me-2"></i>
                                {{ $customer->name }}
                                @if($customer->company_name)
                                    <small class="text-muted">- {{ $customer->company_name }}</small>
                                @endif
                            </h4>
                            <p class="text-muted mb-0">
                                Customer since {{ $customer->created_at->format('M j, Y') }}
                                @if($customer->last_login_at)
                                    • Last login: {{ $customer->last_login_at->diffForHumans() }}
                                @endif
                            </p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ $customer->is_active ? 'success' : 'danger' }} fs-6 mb-2">
                                {{ $customer->is_active ? 'Active' : 'Inactive' }}
                            </span>
                            <br>
                            @if($customer->email_verified_at)
                                <span class="badge bg-success">Email Verified</span>
                            @else
                                <span class="badge bg-warning">Email Unverified</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ $customerStats['total_orders'] }}</h4>
                            <p class="mb-0">Total Orders</p>
                        </div>
                        <div>
                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">${{ number_format($customerStats['total_spent'], 2) }}</h4>
                            <p class="mb-0">Total Spent</p>
                        </div>
                        <div>
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">${{ number_format($customerStats['average_order_value'], 2) }}</h4>
                            <p class="mb-0">Avg Order Value</p>
                        </div>
                        <div>
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ $customerStats['support_tickets'] }}</h4>
                            <p class="mb-0">Support Tickets</p>
                        </div>
                        <div>
                            <i class="fas fa-headset fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Customer Details -->
        <div class="col-lg-8">
            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-address-card me-2"></i>
                        Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Name:</td>
                                    <td>{{ $customer->name }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Email:</td>
                                    <td>
                                        <a href="mailto:{{ $customer->email }}">{{ $customer->email }}</a>
                                        @if($customer->email_verified_at)
                                            <i class="fas fa-check-circle text-success ms-1" title="Verified"></i>
                                        @else
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="Unverified"></i>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Phone:</td>
                                    <td>
                                        @if($customer->phone)
                                            <a href="tel:{{ $customer->phone }}">{{ $customer->phone }}</a>
                                        @else
                                            <span class="text-muted">Not provided</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                @if($customer->company_name)
                                <tr>
                                    <td class="fw-bold">Company:</td>
                                    <td>{{ $customer->company_name }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="fw-bold">Status:</td>
                                    <td>
                                        <span class="badge bg-{{ $customer->is_active ? 'success' : 'danger' }}">
                                            {{ $customer->is_active ? 'Active' : 'Inactive' }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Member Since:</td>
                                    <td>{{ $customer->created_at->format('M j, Y') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            @if($customer->address || $customer->city || $customer->state || $customer->country)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Address Information
                    </h5>
                </div>
                <div class="card-body">
                    <address class="mb-0">
                        @if($customer->address)
                            {{ $customer->address }}<br>
                        @endif
                        @if($customer->city || $customer->state || $customer->postal_code)
                            {{ $customer->city }}@if($customer->city && $customer->state), @endif{{ $customer->state }} {{ $customer->postal_code }}<br>
                        @endif
                        @if($customer->country)
                            {{ $customer->country }}
                        @endif
                    </address>
                </div>
            </div>
            @endif

            <!-- Recent Orders -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Recent Orders ({{ $customerStats['total_orders'] }} total)
                    </h5>
                    @if($customerStats['total_orders'] > 0)
                    <a href="{{ route('admin.orders.index', ['customer_id' => $customer->id]) }}" class="btn btn-sm btn-outline-primary">
                        View All Orders
                    </a>
                    @endif
                </div>
                <div class="card-body">
                    @if($customer->orders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Total</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($customer->orders as $order)
                                    <tr>
                                        <td>
                                            <a href="{{ route('admin.orders.show', $order) }}" class="text-decoration-none">
                                                #{{ $order->order_number }}
                                            </a>
                                        </td>
                                        <td>{{ $order->created_at->format('M j, Y') }}</td>
                                        <td>
                                            <span class="badge bg-{{ $order->status_badge_color }}">
                                                {{ $order->formatted_status }}
                                            </span>
                                        </td>
                                        <td>${{ number_format($order->total_amount, 2) }}</td>
                                        <td>
                                            <a href="{{ route('admin.orders.show', $order) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if($customerStats['last_order_date'])
                        <div class="mt-3">
                            <small class="text-muted">
                                Last order: {{ $customerStats['last_order_date']->diffForHumans() }}
                            </small>
                        </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No Orders Yet</h6>
                            <p class="text-muted">This customer hasn't placed any orders.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Support Tickets -->
            @if($customerStats['support_tickets'] > 0)
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-headset me-2"></i>
                        Recent Support Tickets ({{ $customerStats['support_tickets'] }} total)
                    </h5>
                    <a href="{{ route('admin.support.tickets.index', ['customer_id' => $customer->id]) }}" class="btn btn-sm btn-outline-primary">
                        View All Tickets
                    </a>
                </div>
                <div class="card-body">
                    @if($customer->supportTickets->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Ticket #</th>
                                        <th>Subject</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($customer->supportTickets as $ticket)
                                    <tr>
                                        <td>
                                            <a href="{{ route('admin.support.tickets.show', $ticket) }}" class="text-decoration-none">
                                                #{{ $ticket->ticket_number }}
                                            </a>
                                        </td>
                                        <td>{{ Str::limit($ticket->subject, 40) }}</td>
                                        <td>
                                            <span class="badge bg-{{ $ticket->status_badge_color }}">
                                                {{ $ticket->formatted_status }}
                                            </span>
                                        </td>
                                        <td>{{ $ticket->created_at->format('M j, Y') }}</td>
                                        <td>
                                            <a href="{{ route('admin.support.tickets.show', $ticket) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ $customer->email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-1"></i> Send Email
                        </a>
                        
                        @if($customer->phone)
                        <a href="tel:{{ $customer->phone }}" class="btn btn-outline-success">
                            <i class="fas fa-phone me-1"></i> Call Customer
                        </a>
                        @endif
                        
                        <button type="button" class="btn btn-outline-info" onclick="createOrder()">
                            <i class="fas fa-plus me-1"></i> Create Order
                        </button>
                        
                        <button type="button" class="btn btn-outline-warning" onclick="createTicket()">
                            <i class="fas fa-ticket-alt me-1"></i> Create Support Ticket
                        </button>
                    </div>
                </div>
            </div>

            <!-- Customer Insights -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Customer Insights
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Customer Value:</span>
                            <span class="fw-bold">
                                @if($customerStats['total_spent'] > 1000)
                                    <span class="text-success">High Value</span>
                                @elseif($customerStats['total_spent'] > 500)
                                    <span class="text-warning">Medium Value</span>
                                @else
                                    <span class="text-muted">New Customer</span>
                                @endif
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Order Frequency:</span>
                            <span class="fw-bold">
                                @if($customerStats['total_orders'] > 10)
                                    <span class="text-success">Frequent</span>
                                @elseif($customerStats['total_orders'] > 3)
                                    <span class="text-info">Regular</span>
                                @else
                                    <span class="text-muted">Occasional</span>
                                @endif
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Support Level:</span>
                            <span class="fw-bold">
                                @if($customerStats['support_tickets'] > 5)
                                    <span class="text-danger">High Maintenance</span>
                                @elseif($customerStats['support_tickets'] > 2)
                                    <span class="text-warning">Moderate</span>
                                @else
                                    <span class="text-success">Low Maintenance</span>
                                @endif
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Wishlist Items:</span>
                            <span class="fw-bold">{{ $customerStats['wishlist_items'] }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user-cog me-2"></i>
                        Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold">Customer ID:</td>
                            <td>{{ $customer->id }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Registration:</td>
                            <td>{{ $customer->created_at->format('M j, Y g:i A') }}</td>
                        </tr>
                        @if($customer->last_login_at)
                        <tr>
                            <td class="fw-bold">Last Login:</td>
                            <td>{{ $customer->last_login_at->format('M j, Y g:i A') }}</td>
                        </tr>
                        @endif
                        @if($customer->timezone)
                        <tr>
                            <td class="fw-bold">Timezone:</td>
                            <td>{{ $customer->timezone }}</td>
                        </tr>
                        @endif
                        @if($customer->referral_source)
                        <tr>
                            <td class="fw-bold">Referral Source:</td>
                            <td>{{ $customer->referral_source }}</td>
                        </tr>
                        @endif
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
// Toggle customer status
function toggleStatus(customerId) {
    const isActive = {{ $customer->is_active ? 'true' : 'false' }};
    const action = isActive ? 'deactivate' : 'activate';
    const confirmMessage = isActive ?
        'Are you sure you want to deactivate this customer?' :
        'Are you sure you want to activate this customer?';

    if (confirm(confirmMessage)) {
        fetch(`/admin/customers/${customerId}/toggle-status`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to update customer status'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the customer status');
        });
    }
}

// Delete customer
function deleteCustomer() {
    if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/customers/{{ $customer->id }}`;

        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';

        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = '_token';
        tokenInput.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        form.appendChild(methodInput);
        form.appendChild(tokenInput);
        document.body.appendChild(form);
        form.submit();
    }
}

// Export customer data (placeholder)
function exportCustomerData() {
    alert('Export functionality coming soon!');
}

// Create order for customer (placeholder)
function createOrder() {
    // Redirect to order creation with customer pre-selected
    window.location.href = `/admin/orders/create?customer_id={{ $customer->id }}`;
}

// Create support ticket for customer (placeholder)
function createTicket() {
    // Redirect to ticket creation with customer pre-selected
    window.location.href = `/admin/support/tickets/create?customer_id={{ $customer->id }}`;
}
</script>
@endpush
