<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\SupportTicket;
use App\Models\Wishlist;
use App\Mail\UserWelcome;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class CustomerController extends Controller
{
    /**
     * Display a listing of customers
     */
    public function index(Request $request): View
    {
        $query = User::where('role', 'customer')
                    ->withCount(['orders', 'supportTickets', 'wishlist'])
                    ->withSum(['orders as total_spent' => function($q) {
                        $q->where('payment_status', 'paid');
                    }], 'total_amount');

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->filled('has_orders')) {
            if ($request->has_orders === 'yes') {
                $query->whereHas('orders');
            } else {
                $query->whereDoesntHave('orders');
            }
        }

        if ($request->filled('state')) {
            $query->where('state', $request->state);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $customers = $query->latest()->paginate(20);

        // Get filter options
        $states = User::where('role', 'customer')
                     ->whereNotNull('state')
                     ->distinct()
                     ->pluck('state')
                     ->sort();

        // Customer statistics
        $stats = [
            'total_customers' => User::where('role', 'customer')->count(),
            'active_customers' => User::where('role', 'customer')->where('is_active', true)->count(),
            'customers_with_orders' => User::where('role', 'customer')->whereHas('orders')->count(),
            'new_this_month' => User::where('role', 'customer')
                                   ->whereMonth('created_at', now()->month)
                                   ->count(),
        ];

        return view('admin.customers.index', compact('customers', 'states', 'stats'));
    }

    /**
     * Show the form for creating a new customer
     */
    public function create(): View
    {
        return view('admin.customers.create');
    }

    /**
     * Store a newly created customer
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'phone' => 'nullable|string|max:20',
            'password' => 'required|string|min:8|confirmed',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'company_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        // Store the plain password for email before hashing
        $plainPassword = $validated['password'];
        $validated['password'] = Hash::make($validated['password']);
        $validated['role'] = 'customer';
        $validated['is_active'] = (bool) $request->input('is_active', false);

        $customer = User::create($validated);

        // Send welcome email with login credentials
        try {
            Mail::to($customer->email)->send(new UserWelcome($customer, $plainPassword));
        } catch (\Exception $e) {
            \Log::error('Failed to send welcome email to customer: ' . $e->getMessage());
        }

        return redirect()->route('admin.customers.show', $customer)
                        ->with('success', 'Customer created successfully and welcome email sent.');
    }

    /**
     * Display the specified customer
     */
    public function show(User $customer): View
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        $customer->load([
            'orders' => function($query) {
                $query->latest()->limit(10);
            },
            'supportTickets' => function($query) {
                $query->latest()->limit(10);
            },
            'wishlist' => function($query) {
                $query->with('product')->latest()->limit(10);
            }
        ]);

        // Customer statistics
        $customerStats = [
            'total_orders' => $customer->orders()->count(),
            'total_spent' => $customer->orders()->where('payment_status', 'paid')->sum('total_amount'),
            'average_order_value' => $customer->orders()->where('payment_status', 'paid')->avg('total_amount') ?? 0,
            'last_order_date' => $customer->orders()->latest()->first()?->created_at,
            'support_tickets' => $customer->supportTickets()->count(),
            'wishlist_items' => $customer->wishlist()->count(),
        ];

        return view('admin.customers.show', compact('customer', 'customerStats'));
    }

    /**
     * Show the form for editing the specified customer
     */
    public function edit(User $customer): View
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        return view('admin.customers.edit', compact('customer'));
    }

    /**
     * Update the specified customer
     */
    public function update(Request $request, User $customer): RedirectResponse
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $customer->id,
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|string|min:8|confirmed',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'company_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string|max:1000',
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $validated['is_active'] = (bool) $request->input('is_active', false);

        $customer->update($validated);

        return redirect()->route('admin.customers.show', $customer)
                        ->with('success', 'Customer updated successfully.');
    }

    /**
     * Remove the specified customer
     */
    public function destroy(User $customer): RedirectResponse
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        // Check if customer has orders
        if ($customer->orders()->count() > 0) {
            return redirect()->route('admin.customers.index')
                           ->with('error', 'Cannot delete customer with existing orders.');
        }

        $customer->delete();

        return redirect()->route('admin.customers.index')
                        ->with('success', 'Customer deleted successfully.');
    }

    /**
     * Toggle customer status
     */
    public function toggleStatus(User $customer): JsonResponse
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        $customer->update(['is_active' => !$customer->is_active]);

        return response()->json([
            'success' => true,
            'status' => $customer->is_active,
            'message' => 'Customer status updated successfully.',
        ]);
    }

    /**
     * Get customer orders
     */
    public function orders(User $customer): JsonResponse
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        $orders = $customer->orders()
                          ->with(['items'])
                          ->latest()
                          ->paginate(10);

        return response()->json($orders);
    }

    /**
     * Get customer support tickets
     */
    public function supportTickets(User $customer): JsonResponse
    {
        // Ensure this is a customer
        if ($customer->role !== 'customer') {
            abort(404);
        }

        $tickets = $customer->supportTickets()
                           ->latest()
                           ->paginate(10);

        return response()->json($tickets);
    }

    /**
     * Bulk actions for customers
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'customer_ids' => 'required|array',
            'customer_ids.*' => 'exists:users,id',
            'action' => 'required|in:activate,deactivate,delete',
        ]);

        $customers = User::where('role', 'customer')
                        ->whereIn('id', $request->customer_ids)
                        ->get();

        $count = 0;

        foreach ($customers as $customer) {
            switch ($request->action) {
                case 'activate':
                    $customer->update(['is_active' => true]);
                    $count++;
                    break;
                case 'deactivate':
                    $customer->update(['is_active' => false]);
                    $count++;
                    break;
                case 'delete':
                    if ($customer->orders()->count() === 0) {
                        $customer->delete();
                        $count++;
                    }
                    break;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully processed {$count} customers.",
        ]);
    }
}
