<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\Payment;
use App\Models\Order;
use App\Models\SiteSetting;
use App\Services\StripePaymentService;
use App\Services\PayPalPaymentService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $stripeService;
    protected $paypalService;

    public function __construct(StripePaymentService $stripeService, PayPalPaymentService $paypalService)
    {
        $this->stripeService = $stripeService;
        $this->paypalService = $paypalService;
    }

    /**
     * Show payment form for a parcel
     */
    public function show(Parcel $parcel): View|RedirectResponse
    {
        // Ensure the parcel belongs to the authenticated customer
        if ($parcel->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this parcel.');
        }

        // Check if parcel is already paid
        if ($parcel->is_paid) {
            return redirect()->route('customer.parcels.show', $parcel)
                           ->with('info', 'This parcel has already been paid for.');
        }

        // Get available payment methods
        $paymentMethods = $this->getAvailablePaymentMethods();
        
        // Get currency settings
        $currency = [
            'code' => SiteSetting::getValue('base_currency', 'USD'),
            'symbol' => SiteSetting::getValue('currency_symbol', '$'),
        ];

        return view('customer.payments.show', compact('parcel', 'paymentMethods', 'currency'));
    }

    /**
     * Process payment
     */
    public function process(Request $request, Parcel $parcel): RedirectResponse|\Illuminate\Http\JsonResponse
    {
        // Ensure the parcel belongs to the authenticated customer
        if ($parcel->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this parcel.');
        }

        // Check if parcel is already paid
        if ($parcel->is_paid) {
            return redirect()->route('customer.parcels.show', $parcel)
                           ->with('info', 'This parcel has already been paid for.');
        }

        $validated = $request->validate([
            'payment_method' => 'required|string|in:stripe,paypal,razorpay,square,admin_approval',
            'payment_intent_id' => 'nullable|string', // For Stripe
            'payment_id' => 'nullable|string', // For PayPal
            'payer_id' => 'nullable|string', // For PayPal
            'save_card' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $paymentResult = match($validated['payment_method']) {
                'stripe' => $this->processStripePayment($request, $parcel),
                'paypal' => $this->processPayPalPayment($request, $parcel),
                'razorpay' => $this->processRazorpayPayment($request, $parcel),
                'square' => $this->processSquarePayment($request, $parcel),
                'admin_approval' => $this->requestAdminApproval($request, $parcel),
                default => throw new \Exception('Invalid payment method')
            };

            // Handle special cases that require redirects or additional actions
            if (isset($paymentResult['requires_action']) && $paymentResult['requires_action']) {
                return response()->json($paymentResult);
            }

            if (isset($paymentResult['requires_redirect']) && $paymentResult['requires_redirect']) {
                return redirect()->to($paymentResult['approval_url']);
            }

            if ($paymentResult['success']) {
                // Create payment record
                $payment = Payment::create([
                    'parcel_id' => $parcel->id,
                    'user_id' => Auth::id(),
                    'amount' => $parcel->total_cost,
                    'currency' => SiteSetting::getValue('base_currency', 'USD'),
                    'payment_method' => $validated['payment_method'],
                    'transaction_id' => $paymentResult['transaction_id'] ?? null,
                    'gateway_response' => $paymentResult['gateway_response'] ?? null,
                    'status' => $paymentResult['status'] ?? 'completed',
                ]);

                // Update parcel if payment is completed
                if ($paymentResult['status'] === 'completed') {
                    $parcel->update(['is_paid' => true]);
                }

                DB::commit();

                return redirect()->route('customer.payments.success', $payment)
                               ->with('success', $paymentResult['message']);
            } else {
                DB::rollBack();
                return redirect()->back()
                               ->with('error', $paymentResult['message'])
                               ->withInput();
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Payment processing failed', [
                'parcel_id' => $parcel->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                           ->with('error', 'Payment processing failed. Please try again.')
                           ->withInput();
        }
    }

    /**
     * Show payment success page
     */
    public function success(Payment $payment): View
    {
        // Ensure the payment belongs to the authenticated customer
        if ($payment->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this payment.');
        }

        return view('customer.payments.success', compact('payment'));
    }

    /**
     * Handle PayPal return
     */
    public function paypalReturn(Request $request, Parcel $parcel): RedirectResponse
    {
        $paymentId = $request->get('paymentId');
        $payerId = $request->get('PayerID');

        if (!$paymentId || !$payerId) {
            return redirect()->route('customer.payments.show', $parcel)
                           ->with('error', 'Invalid PayPal payment parameters.');
        }

        try {
            DB::beginTransaction();

            // Execute PayPal payment
            $result = $this->paypalService->executePayment($paymentId, $payerId);

            if ($result['success']) {
                // Create payment record
                $payment = Payment::create([
                    'parcel_id' => $parcel->id,
                    'user_id' => Auth::id(),
                    'amount' => $parcel->total_cost,
                    'currency' => SiteSetting::getValue('base_currency', 'USD'),
                    'payment_method' => 'paypal',
                    'transaction_id' => $result['transaction_id'],
                    'gateway_response' => $result['gateway_response'],
                    'status' => $result['status'],
                ]);

                // Update parcel
                $parcel->update(['is_paid' => true]);

                DB::commit();

                return redirect()->route('customer.payments.success', $payment)
                               ->with('success', $result['message']);
            } else {
                DB::rollBack();
                return redirect()->route('customer.payments.show', $parcel)
                               ->with('error', 'PayPal payment failed: ' . $result['error']);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('PayPal return processing failed', [
                'parcel_id' => $parcel->id,
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('customer.payments.show', $parcel)
                           ->with('error', 'Payment processing failed. Please try again.');
        }
    }

    /**
     * Handle PayPal cancel
     */
    public function paypalCancel(Parcel $parcel): RedirectResponse
    {
        return redirect()->route('customer.payments.show', $parcel)
                       ->with('warning', 'PayPal payment was cancelled.');
    }

    /**
     * Process Stripe payment
     */
    private function processStripePayment(Request $request, Parcel $parcel): array
    {
        try {
            $paymentIntentId = $request->input('payment_intent_id');

            if (!$paymentIntentId) {
                // Create new payment intent
                $result = $this->stripeService->createPaymentIntent($parcel);

                if (!$result['success']) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create payment intent: ' . $result['error'],
                    ];
                }

                // Return for frontend to handle payment
                return [
                    'success' => true,
                    'requires_action' => true,
                    'client_secret' => $result['client_secret'],
                    'payment_intent_id' => $result['payment_intent_id'],
                ];
            } else {
                // Verify existing payment intent
                $result = $this->stripeService->handleSuccessfulPayment($paymentIntentId);

                if ($result['success']) {
                    return [
                        'success' => true,
                        'transaction_id' => $result['transaction_id'],
                        'status' => $result['status'],
                        'message' => $result['message'],
                        'gateway_response' => $result['gateway_response'],
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Payment verification failed: ' . $result['error'],
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error('Stripe payment processing error', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing failed. Please try again.',
            ];
        }
    }

    /**
     * Process PayPal payment
     */
    private function processPayPalPayment(Request $request, Parcel $parcel): array
    {
        try {
            $paymentId = $request->input('payment_id');
            $payerId = $request->input('payer_id');

            if (!$paymentId || !$payerId) {
                // Create new PayPal payment
                $returnUrl = route('customer.payments.paypal.return', $parcel);
                $cancelUrl = route('customer.payments.paypal.cancel', $parcel);

                $result = $this->paypalService->createPayment($parcel, $returnUrl, $cancelUrl);

                if (!$result['success']) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create PayPal payment: ' . $result['error'],
                    ];
                }

                // Redirect to PayPal for approval
                return [
                    'success' => true,
                    'requires_redirect' => true,
                    'approval_url' => $result['approval_url'],
                    'payment_id' => $result['payment_id'],
                ];
            } else {
                // Execute PayPal payment
                $result = $this->paypalService->executePayment($paymentId, $payerId);

                if ($result['success']) {
                    return [
                        'success' => true,
                        'transaction_id' => $result['transaction_id'],
                        'status' => $result['status'],
                        'message' => $result['message'],
                        'gateway_response' => $result['gateway_response'],
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'PayPal payment execution failed: ' . $result['error'],
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error('PayPal payment processing error', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'message' => 'Payment processing failed. Please try again.',
            ];
        }
    }

    /**
     * Process Razorpay payment (for Indian market)
     */
    private function processRazorpayPayment(Request $request, Parcel $parcel): array
    {
        // Razorpay integration would go here
        // This is a placeholder for actual Razorpay implementation
        
        return [
            'success' => true,
            'transaction_id' => 'razorpay_' . uniqid(),
            'status' => 'completed',
            'message' => 'Payment processed successfully via Razorpay.',
            'gateway_response' => json_encode(['status' => 'captured'])
        ];
    }

    /**
     * Process Square payment
     */
    private function processSquarePayment(Request $request, Parcel $parcel): array
    {
        // Square integration would go here
        // This is a placeholder for actual Square implementation
        
        return [
            'success' => true,
            'transaction_id' => 'square_' . uniqid(),
            'status' => 'completed',
            'message' => 'Payment processed successfully via Square.',
            'gateway_response' => json_encode(['status' => 'COMPLETED'])
        ];
    }

    /**
     * Request admin approval for manual payment
     */
    private function requestAdminApproval(Request $request, Parcel $parcel): array
    {
        // This creates a pending payment that requires admin approval
        // No card details are stored - customer provides payment offline
        
        return [
            'success' => true,
            'transaction_id' => 'admin_approval_' . uniqid(),
            'status' => 'pending_approval',
            'message' => 'Payment request submitted for admin approval. You will be notified once processed.',
            'gateway_response' => json_encode(['status' => 'pending_admin_approval'])
        ];
    }

    /**
     * Get available payment methods based on configuration
     */
    private function getAvailablePaymentMethods(): array
    {
        $methods = [];

        // Check which payment gateways are configured
        if (config('services.stripe.key')) {
            $methods['stripe'] = [
                'enabled' => true,
                'name' => 'Credit/Debit Card (Stripe)',
                'description' => 'Secure payment via Stripe',
                'icon' => 'fab fa-cc-stripe',
                'color' => 'primary',
                'supports_cards' => true,
                'supports_3ds' => true,
                'processing_fee' => 0, // Can be configured
            ];
        }

        if (config('services.paypal.client_id')) {
            $methods['paypal'] = [
                'enabled' => true,
                'name' => 'PayPal',
                'description' => 'Pay with your PayPal account',
                'icon' => 'fab fa-paypal',
                'color' => 'info',
                'supports_cards' => false,
                'supports_3ds' => true,
                'processing_fee' => 0, // Can be configured
            ];
        }

        if (config('services.razorpay.key')) {
            $methods['razorpay'] = [
                'enabled' => true,
                'name' => 'Razorpay',
                'description' => 'UPI, Cards, Net Banking, Wallets',
                'icon' => 'fas fa-credit-card',
                'color' => 'success',
                'supports_cards' => true,
                'supports_3ds' => true,
                'processing_fee' => 0, // Can be configured
            ];
        }

        if (config('services.square.application_id')) {
            $methods['square'] = [
                'enabled' => true,
                'name' => 'Square',
                'description' => 'Secure card payment via Square',
                'icon' => 'fas fa-credit-card',
                'color' => 'warning',
                'supports_cards' => true,
                'supports_3ds' => true,
                'processing_fee' => 0, // Can be configured
            ];
        }

        // Always include admin approval option
        $methods['admin_approval'] = [
            'enabled' => true,
            'name' => 'Bank Transfer / Manual Payment',
            'description' => 'Pay via bank transfer or other offline methods',
            'icon' => 'fas fa-university',
            'color' => 'secondary',
            'supports_cards' => false,
            'supports_3ds' => false,
            'processing_fee' => 0,
        ];

        return $methods;
    }

    /**
     * Show payment form for an order
     */
    public function showOrderPayment(Order $order): View|RedirectResponse
    {
        // Ensure the order belongs to the authenticated customer
        if ($order->customer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        // Check if order is already paid
        if ($order->isPaid()) {
            return redirect()->route('customer.orders.show', $order)
                           ->with('info', 'This order has already been paid for.');
        }

        // Check if order can be paid (not cancelled)
        if ($order->isCancelled()) {
            return redirect()->route('customer.orders.show', $order)
                           ->with('error', 'Cannot pay for a cancelled order.');
        }

        // Get available payment methods
        $paymentMethods = $this->getAvailablePaymentMethods();

        // Get currency settings
        $currency = [
            'code' => SiteSetting::getValue('base_currency', 'USD'),
            'symbol' => SiteSetting::getValue('currency_symbol', '$'),
        ];

        return view('customer.payments.order-payment', compact('order', 'paymentMethods', 'currency'));
    }

    /**
     * Process order payment
     */
    public function processOrderPayment(Request $request, Order $order): RedirectResponse|\Illuminate\Http\JsonResponse
    {
        // Ensure the order belongs to the authenticated customer
        if ($order->customer_id !== Auth::id()) {
            abort(403, 'Unauthorized access to this order.');
        }

        // Check if order is already paid
        if ($order->isPaid()) {
            return redirect()->route('customer.orders.show', $order)
                           ->with('info', 'This order has already been paid for.');
        }

        // Check if order can be paid
        if ($order->isCancelled()) {
            return redirect()->route('customer.orders.show', $order)
                           ->with('error', 'Cannot pay for a cancelled order.');
        }

        $validated = $request->validate([
            'payment_method' => 'required|string|in:stripe,paypal,razorpay,square,admin_approval',
            'payment_intent_id' => 'nullable|string', // For Stripe
            'payment_id' => 'nullable|string', // For PayPal
            'payer_id' => 'nullable|string', // For PayPal
            'save_card' => 'boolean',
        ]);

        try {
            DB::beginTransaction();

            $paymentResult = match($validated['payment_method']) {
                'stripe' => $this->processOrderStripePayment($request, $order),
                'paypal' => $this->processOrderPayPalPayment($request, $order),
                'razorpay' => $this->processOrderRazorpayPayment($request, $order),
                'square' => $this->processOrderSquarePayment($request, $order),
                'admin_approval' => $this->requestOrderAdminApproval($request, $order),
                default => throw new \Exception('Invalid payment method')
            };

            // Handle special cases that require redirects or additional actions
            if (isset($paymentResult['requires_action']) && $paymentResult['requires_action']) {
                return response()->json($paymentResult);
            }

            if (isset($paymentResult['requires_redirect']) && $paymentResult['requires_redirect']) {
                return redirect()->to($paymentResult['approval_url']);
            }

            if ($paymentResult['success']) {
                // Create payment record
                $payment = Payment::create([
                    'order_id' => $order->id,
                    'user_id' => Auth::id(),
                    'amount' => $order->total_amount,
                    'currency' => SiteSetting::getValue('base_currency', 'USD'),
                    'payment_method' => $validated['payment_method'],
                    'transaction_id' => $paymentResult['transaction_id'] ?? null,
                    'gateway_response' => $paymentResult['gateway_response'] ?? null,
                    'status' => $paymentResult['status'] ?? 'completed',
                ]);

                // Update order if payment is completed
                if ($paymentResult['status'] === 'completed') {
                    $order->update([
                        'payment_status' => 'paid',
                        'payment_method' => $validated['payment_method'],
                        'payment_reference' => $paymentResult['transaction_id'] ?? null,
                        'paid_at' => now(),
                    ]);
                }

                DB::commit();

                return redirect()->route('customer.payments.success', $payment)
                               ->with('success', $paymentResult['message'] ?? 'Payment processed successfully.');
            } else {
                DB::rollBack();
                return redirect()->route('customer.orders.pay', $order)
                               ->with('error', $paymentResult['message'] ?? 'Payment failed. Please try again.');
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order payment processing failed', [
                'order_id' => $order->id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->route('customer.orders.pay', $order)
                           ->with('error', 'Payment processing failed. Please try again or contact support.');
        }
    }

    /**
     * Handle PayPal return for order payment
     */
    public function orderPaypalReturn(Request $request, Order $order): RedirectResponse
    {
        $paymentId = $request->get('paymentId');
        $payerId = $request->get('PayerID');

        if (!$paymentId || !$payerId) {
            return redirect()->route('customer.orders.pay', $order)
                           ->with('error', 'Invalid PayPal payment parameters.');
        }

        try {
            DB::beginTransaction();

            $result = $this->paypalService->executePayment($paymentId, $payerId);

            if ($result['success']) {
                // Create payment record
                $payment = Payment::create([
                    'order_id' => $order->id,
                    'user_id' => Auth::id(),
                    'amount' => $order->total_amount,
                    'currency' => SiteSetting::getValue('base_currency', 'USD'),
                    'payment_method' => 'paypal',
                    'transaction_id' => $result['transaction_id'],
                    'gateway_response' => $result['gateway_response'],
                    'status' => $result['status'],
                ]);

                // Update order
                $order->update([
                    'payment_status' => 'paid',
                    'payment_method' => 'paypal',
                    'payment_reference' => $result['transaction_id'],
                    'paid_at' => now(),
                ]);

                DB::commit();

                return redirect()->route('customer.payments.success', $payment)
                               ->with('success', $result['message']);
            } else {
                DB::rollBack();
                return redirect()->route('customer.orders.pay', $order)
                               ->with('error', 'PayPal payment failed: ' . $result['error']);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('PayPal order payment return failed', [
                'order_id' => $order->id,
                'payment_id' => $paymentId,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('customer.orders.pay', $order)
                           ->with('error', 'PayPal payment processing failed. Please try again.');
        }
    }

    /**
     * Handle PayPal cancel for order payment
     */
    public function orderPaypalCancel(Order $order): RedirectResponse
    {
        return redirect()->route('customer.orders.pay', $order)
                       ->with('warning', 'PayPal payment was cancelled. You can try again or choose a different payment method.');
    }

    /**
     * Process Stripe payment for order
     */
    private function processOrderStripePayment(Request $request, Order $order): array
    {
        try {
            $paymentIntentId = $request->input('payment_intent_id');

            if (!$paymentIntentId) {
                // Create new payment intent
                $result = $this->stripeService->createOrderPaymentIntent($order);

                if (!$result['success']) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create payment intent: ' . $result['error'],
                    ];
                }

                // Return for frontend to handle payment
                return [
                    'success' => true,
                    'requires_action' => true,
                    'client_secret' => $result['client_secret'],
                    'payment_intent_id' => $result['payment_intent_id'],
                ];
            } else {
                // Confirm existing payment intent
                $result = $this->stripeService->confirmPaymentIntent($paymentIntentId);

                if ($result['success']) {
                    return [
                        'success' => true,
                        'transaction_id' => $result['payment_intent_id'],
                        'status' => 'completed',
                        'message' => 'Payment completed successfully.',
                        'gateway_response' => $result['payment_intent']
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Payment confirmation failed: ' . $result['error'],
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error('Stripe order payment failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Stripe payment processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process PayPal payment for order
     */
    private function processOrderPayPalPayment(Request $request, Order $order): array
    {
        try {
            $paymentId = $request->input('payment_id');
            $payerId = $request->input('payer_id');

            if (!$paymentId || !$payerId) {
                // Create new PayPal payment
                $returnUrl = route('customer.orders.payment.paypal.return', $order);
                $cancelUrl = route('customer.orders.payment.paypal.cancel', $order);

                $result = $this->paypalService->createOrderPayment($order, $returnUrl, $cancelUrl);

                if (!$result['success']) {
                    return [
                        'success' => false,
                        'message' => 'Failed to create PayPal payment: ' . $result['error'],
                    ];
                }

                // Redirect to PayPal for approval
                return [
                    'success' => true,
                    'requires_redirect' => true,
                    'approval_url' => $result['approval_url'],
                    'payment_id' => $result['payment_id'],
                ];
            } else {
                // Execute PayPal payment
                $result = $this->paypalService->executePayment($paymentId, $payerId);

                if ($result['success']) {
                    return [
                        'success' => true,
                        'transaction_id' => $result['transaction_id'],
                        'status' => 'completed',
                        'message' => 'PayPal payment completed successfully.',
                        'gateway_response' => $result['gateway_response']
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'PayPal payment execution failed: ' . $result['error'],
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error('PayPal order payment failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'PayPal payment processing failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Process Razorpay payment for order
     */
    private function processOrderRazorpayPayment(Request $request, Order $order): array
    {
        // Razorpay integration would go here
        // This is a placeholder for actual Razorpay implementation

        return [
            'success' => true,
            'transaction_id' => 'razorpay_order_' . uniqid(),
            'status' => 'completed',
            'message' => 'Payment processed successfully via Razorpay.',
            'gateway_response' => json_encode(['status' => 'captured'])
        ];
    }

    /**
     * Process Square payment for order
     */
    private function processOrderSquarePayment(Request $request, Order $order): array
    {
        // Square integration would go here
        // This is a placeholder for actual Square implementation

        return [
            'success' => true,
            'transaction_id' => 'square_order_' . uniqid(),
            'status' => 'completed',
            'message' => 'Payment processed successfully via Square.',
            'gateway_response' => json_encode(['status' => 'COMPLETED'])
        ];
    }

    /**
     * Request admin approval for manual order payment
     */
    private function requestOrderAdminApproval(Request $request, Order $order): array
    {
        // This creates a pending payment that requires admin approval
        // Useful for bank transfers, checks, or other manual payment methods

        return [
            'success' => true,
            'transaction_id' => 'manual_order_' . uniqid(),
            'status' => 'pending_approval',
            'message' => 'Payment request submitted for admin approval. You will be notified once the payment is verified.',
            'gateway_response' => json_encode([
                'method' => 'manual',
                'submitted_at' => now()->toISOString(),
                'requires_admin_approval' => true
            ])
        ];
    }
}
