# 🧪 Comprehensive Testing Strategy - Atrix Logistics

## 🎯 Testing Overview

This document outlines the complete testing strategy for the Atrix Logistics application, ensuring code quality, functionality, and reliability for seamless developer handover.

## 📋 Testing Pyramid

```
                    E2E Tests (10%)
                 ┌─────────────────┐
                 │   Browser Tests │
                 │   User Journeys │
                 └─────────────────┘
              
              Integration Tests (20%)
           ┌─────────────────────────┐
           │    Feature Tests        │
           │    API Tests           │
           │    Database Tests      │
           └─────────────────────────┘
           
         Unit Tests (70%)
    ┌─────────────────────────────────┐
    │        Model Tests              │
    │        Service Tests            │
    │        Helper Tests             │
    └─────────────────────────────────┘
```

## 🔧 Testing Environment Setup

### Required Packages
```json
{
    "require-dev": {
        "phpunit/phpunit": "^10.1",
        "laravel/dusk": "^7.0",
        "mockery/mockery": "^1.4.4",
        "fakerphp/faker": "^1.21",
        "pestphp/pest": "^2.0",
        "pestphp/pest-plugin-laravel": "^2.0"
    }
}
```

### Test Database Configuration
```php
// config/database.php - testing connection
'testing' => [
    'driver' => 'sqlite',
    'database' => ':memory:',
    'prefix' => '',
],

// phpunit.xml
<env name="DB_CONNECTION" value="testing"/>
<env name="MAIL_MAILER" value="array"/>
<env name="QUEUE_CONNECTION" value="sync"/>
<env name="CACHE_DRIVER" value="array"/>
```

## 🏗️ Unit Tests (70% of test suite)

### Model Tests
```php
// tests/Unit/Models/ParcelTest.php
class ParcelTest extends TestCase
{
    use RefreshDatabase;

    public function test_parcel_can_generate_tracking_number()
    {
        $parcel = Parcel::factory()->create();
        
        $this->assertNotNull($parcel->tracking_number);
        $this->assertStringStartsWith('ATX-', $parcel->tracking_number);
        $this->assertMatchesRegularExpression('/^ATX-\d{4}-\d{8}$/', $parcel->tracking_number);
    }

    public function test_parcel_belongs_to_carrier()
    {
        $carrier = Carrier::factory()->create();
        $parcel = Parcel::factory()->create(['carrier_id' => $carrier->id]);

        $this->assertInstanceOf(Carrier::class, $parcel->carrier);
        $this->assertEquals($carrier->id, $parcel->carrier->id);
    }

    public function test_parcel_can_have_tracking_events()
    {
        $parcel = Parcel::factory()->create();
        TrackingEvent::factory()->count(3)->create(['parcel_id' => $parcel->id]);

        $this->assertCount(3, $parcel->trackingEvents);
    }

    public function test_parcel_status_validation()
    {
        $this->expectException(ValidationException::class);
        
        Parcel::factory()->create(['status' => 'invalid_status']);
    }
}
```

### Service Tests
```php
// tests/Unit/Services/ParcelServiceTest.php
class ParcelServiceTest extends TestCase
{
    use RefreshDatabase;

    private ParcelService $parcelService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->parcelService = app(ParcelService::class);
    }

    public function test_can_create_parcel_with_tracking_number()
    {
        $data = [
            'carrier_id' => Carrier::factory()->create()->id,
            'sender_name' => 'John Doe',
            'recipient_name' => 'Jane Smith',
            // ... other required fields
        ];

        $parcel = $this->parcelService->createParcel($data);

        $this->assertInstanceOf(Parcel::class, $parcel);
        $this->assertNotNull($parcel->tracking_number);
        $this->assertDatabaseHas('parcels', ['id' => $parcel->id]);
    }

    public function test_can_update_parcel_status()
    {
        $parcel = Parcel::factory()->create(['status' => 'pending']);

        $this->parcelService->updateParcelStatus($parcel, 'in_transit', [
            'location' => 'Chicago, IL',
            'description' => 'Package in transit'
        ]);

        $this->assertEquals('in_transit', $parcel->fresh()->status);
        $this->assertDatabaseHas('tracking_events', [
            'parcel_id' => $parcel->id,
            'status' => 'in_transit'
        ]);
    }
}
```

### Repository Tests
```php
// tests/Unit/Repositories/ParcelRepositoryTest.php
class ParcelRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private ParcelRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = app(ParcelRepository::class);
    }

    public function test_can_find_parcel_by_tracking_number()
    {
        $parcel = Parcel::factory()->create();

        $found = $this->repository->findByTrackingNumber($parcel->tracking_number);

        $this->assertNotNull($found);
        $this->assertEquals($parcel->id, $found->id);
    }

    public function test_returns_null_for_invalid_tracking_number()
    {
        $found = $this->repository->findByTrackingNumber('INVALID-123');

        $this->assertNull($found);
    }
}
```

## 🔗 Integration Tests (20% of test suite)

### Feature Tests
```php
// tests/Feature/TrackingTest.php
class TrackingTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_track_parcel_with_valid_tracking_number()
    {
        $parcel = Parcel::factory()->create();

        $response = $this->get("/track/{$parcel->tracking_number}");

        $response->assertStatus(200)
                ->assertSee($parcel->tracking_number)
                ->assertSee($parcel->status);
    }

    public function test_tracking_returns_404_for_invalid_number()
    {
        $response = $this->get('/track/INVALID-123');

        $response->assertStatus(404);
    }

    public function test_tracking_api_returns_json_response()
    {
        $parcel = Parcel::factory()->create();

        $response = $this->getJson("/api/track/{$parcel->tracking_number}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'tracking_number',
                        'status',
                        'carrier',
                        'events'
                    ]
                ]);
    }
}
```

### API Tests
```php
// tests/Feature/Api/QuoteApiTest.php
class QuoteApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_submit_quote_request()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'message' => 'Need shipping quote'
        ];

        $response = $this->postJson('/api/quotes', $data);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => ['quote_number', 'status'],
                    'message'
                ]);

        $this->assertDatabaseHas('quote_requests', [
            'email' => '<EMAIL>'
        ]);
    }

    public function test_quote_request_validation()
    {
        $response = $this->postJson('/api/quotes', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email']);
    }
}
```

### Authentication Tests
```php
// tests/Feature/AuthenticationTest.php
class AuthenticationTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_dashboard()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)->get('/admin');

        $response->assertStatus(200);
    }

    public function test_customer_cannot_access_admin_dashboard()
    {
        $customer = User::factory()->create(['role' => 'customer']);

        $response = $this->actingAs($customer)->get('/admin');

        $response->assertStatus(403);
    }

    public function test_api_requires_authentication_for_protected_routes()
    {
        $response = $this->postJson('/api/parcels', []);

        $response->assertStatus(401);
    }
}
```

## 🌐 End-to-End Tests (10% of test suite)

### Browser Tests with Laravel Dusk
```php
// tests/Browser/TrackingJourneyTest.php
class TrackingJourneyTest extends DuskTestCase
{
    use DatabaseMigrations;

    public function test_complete_tracking_user_journey()
    {
        $parcel = Parcel::factory()->create();

        $this->browse(function (Browser $browser) use ($parcel) {
            $browser->visit('/')
                    ->clickLink('Track Now')
                    ->assertPathIs('/track')
                    ->type('tracking_number', $parcel->tracking_number)
                    ->press('Track Package')
                    ->waitForText($parcel->tracking_number)
                    ->assertSee($parcel->status)
                    ->assertSee($parcel->carrier->name);
        });
    }

    public function test_quote_modal_functionality()
    {
        $this->browse(function (Browser $browser) {
            $browser->visit('/')
                    ->click('@get-quote-button')
                    ->waitFor('@quote-modal')
                    ->type('name', 'John Doe')
                    ->type('email', '<EMAIL>')
                    ->type('phone', '+1234567890')
                    ->type('message', 'Need shipping quote')
                    ->press('Submit Quote Request')
                    ->waitForText('Quote request submitted successfully');
        });
    }
}
```

### Product Purchase Journey
```php
// tests/Browser/ProductPurchaseTest.php
class ProductPurchaseTest extends DuskTestCase
{
    public function test_customer_can_purchase_product()
    {
        $user = User::factory()->create(['role' => 'customer']);
        $product = Product::factory()->create(['price' => 25.99]);

        $this->browse(function (Browser $browser) use ($user, $product) {
            $browser->loginAs($user)
                    ->visit('/shop')
                    ->click("@product-{$product->id}")
                    ->assertSee($product->name)
                    ->press('Add to Cart')
                    ->waitForText('Added to cart')
                    ->click('@cart-icon')
                    ->assertSee($product->name)
                    ->press('Checkout')
                    ->waitForLocation('/checkout')
                    ->type('billing_address', '123 Main St')
                    ->press('Place Order')
                    ->waitForText('Order placed successfully');
        });
    }

    public function test_guest_can_inquire_about_product()
    {
        $product = Product::factory()->create();

        $this->browse(function (Browser $browser) use ($product) {
            $browser->visit("/products/{$product->slug}")
                    ->press('Inquire')
                    ->waitFor('@quote-modal')
                    ->assertInputValue('product_name', $product->name)
                    ->type('name', 'Jane Doe')
                    ->type('email', '<EMAIL>')
                    ->press('Send Inquiry')
                    ->waitForText('Inquiry sent successfully');
        });
    }
}
```

## 📊 Performance Tests

### Database Performance Tests
```php
// tests/Performance/DatabasePerformanceTest.php
class DatabasePerformanceTest extends TestCase
{
    use RefreshDatabase;

    public function test_parcel_listing_performance()
    {
        // Create 1000 parcels
        Parcel::factory()->count(1000)->create();

        $startTime = microtime(true);
        
        $parcels = Parcel::with(['carrier', 'user'])
                        ->paginate(20);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.5, $executionTime, 'Query took too long');
        $this->assertCount(20, $parcels->items());
    }

    public function test_tracking_lookup_performance()
    {
        $parcel = Parcel::factory()->create();

        $startTime = microtime(true);
        
        $found = Parcel::where('tracking_number', $parcel->tracking_number)
                      ->with(['carrier', 'trackingEvents'])
                      ->first();
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        $this->assertLessThan(0.1, $executionTime, 'Tracking lookup too slow');
        $this->assertNotNull($found);
    }
}
```

## 🔒 Security Tests

### Security Test Examples
```php
// tests/Security/SecurityTest.php
class SecurityTest extends TestCase
{
    public function test_sql_injection_protection()
    {
        $maliciousInput = "'; DROP TABLE parcels; --";

        $response = $this->get("/track/{$maliciousInput}");

        $response->assertStatus(404);
        $this->assertDatabaseHas('parcels', []); // Table should still exist
    }

    public function test_xss_protection()
    {
        $maliciousScript = '<script>alert("xss")</script>';

        $response = $this->postJson('/api/contact', [
            'name' => $maliciousScript,
            'email' => '<EMAIL>',
            'message' => 'Test message'
        ]);

        $response->assertStatus(422); // Should be rejected by validation
    }

    public function test_csrf_protection()
    {
        $response = $this->post('/contact', [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'message' => 'Test'
        ]);

        $response->assertStatus(419); // CSRF token mismatch
    }
}
```

## 📋 Test Data Management

### Factories
```php
// database/factories/ParcelFactory.php
class ParcelFactory extends Factory
{
    public function definition(): array
    {
        return [
            'tracking_number' => 'ATX-' . date('Y') . '-' . str_pad(fake()->unique()->numberBetween(1, 99999999), 8, '0', STR_PAD_LEFT),
            'carrier_id' => Carrier::factory(),
            'sender_name' => fake()->name(),
            'sender_email' => fake()->email(),
            'sender_address' => fake()->streetAddress(),
            'sender_city' => fake()->city(),
            'sender_state' => fake()->stateAbbr(),
            'sender_postal_code' => fake()->postcode(),
            'recipient_name' => fake()->name(),
            'recipient_email' => fake()->email(),
            'recipient_address' => fake()->streetAddress(),
            'recipient_city' => fake()->city(),
            'recipient_state' => fake()->stateAbbr(),
            'recipient_postal_code' => fake()->postcode(),
            'status' => fake()->randomElement(['pending', 'picked_up', 'in_transit', 'delivered']),
            'description' => fake()->sentence(),
            'weight' => fake()->randomFloat(2, 0.1, 50),
        ];
    }

    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'delivered',
            'delivered_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }
}
```

## 🚀 Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/tests.yml
name: Tests

on: [push, pull_request]

jobs:
  tests:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - uses: actions/checkout@v3
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.1
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv
        
    - name: Install dependencies
      run: |
        composer install --no-progress --prefer-dist --optimize-autoloader
        npm install
        
    - name: Prepare Laravel Application
      run: |
        cp .env.example .env
        php artisan key:generate
        
    - name: Run tests
      run: |
        php artisan test --coverage --min=80
        
    - name: Run Dusk tests
      run: |
        php artisan dusk:chrome-driver
        php artisan serve &
        php artisan dusk
```

## 📊 Test Coverage Requirements

### Coverage Targets
- **Overall Coverage**: Minimum 80%
- **Models**: Minimum 90%
- **Services**: Minimum 85%
- **Controllers**: Minimum 75%
- **Critical Features**: 100% (tracking, quotes, authentication)

### Coverage Commands
```bash
# Generate coverage report
php artisan test --coverage --min=80

# Generate HTML coverage report
php artisan test --coverage-html coverage-report

# Check specific coverage
php artisan test --coverage --min=80 tests/Unit/Models/
```

This comprehensive testing strategy ensures that any developer can understand, maintain, and extend the test suite, providing confidence in code quality and functionality throughout the development lifecycle.
