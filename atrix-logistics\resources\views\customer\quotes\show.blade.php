@extends('layouts.customer')

@section('page-title', 'Quote Details - ' . $quote->quote_number)

@section('content')
<div class="container-fluid">
    <!-- Quote Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-1">
                                <i class="fas fa-file-invoice me-2"></i>
                                Quote #{{ $quote->quote_number }}
                            </h4>
                            <p class="text-muted mb-0">
                                Created on {{ $quote->created_at->format('M j, Y \a\t g:i A') }}
                            </p>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-{{ $quote->status_badge_color }} fs-6 mb-2">
                                {{ $quote->formatted_status }}
                            </span>
                            <br>
                            <span class="badge bg-{{ $quote->priority_badge_color }}">
                                {{ $quote->formatted_priority }} Priority
                            </span>
                        </div>
                    </div>
                </div>
                
                @if($quote->isQuoted() && !$quote->isExpired())
                <div class="card-body border-top bg-light">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="text-success mb-1">
                                <i class="fas fa-check-circle me-2"></i>
                                Your quote is ready!
                            </h5>
                            <p class="mb-0">
                                @if($quote->expires_at)
                                    This quote expires on {{ $quote->expires_at->format('M j, Y') }}
                                    @if($quote->getDaysUntilExpiry() <= 3)
                                        <span class="text-danger fw-bold">({{ $quote->getDaysUntilExpiry() }} days remaining)</span>
                                    @endif
                                @endif
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <form method="POST" action="{{ route('customer.quotes.accept', $quote) }}" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-success me-2">
                                    <i class="fas fa-check me-1"></i> Accept Quote
                                </button>
                            </form>
                            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                <i class="fas fa-times me-1"></i> Reject
                            </button>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Quote Details -->
        <div class="col-lg-8">
            <!-- Service Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Service Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Quote Type:</td>
                                    <td>{{ ucwords($quote->quote_type) }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Service Type:</td>
                                    <td>{{ $quote->formatted_service_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Delivery Speed:</td>
                                    <td>{{ ucwords($quote->delivery_speed) }}</td>
                                </tr>
                                @if($quote->preferred_pickup_date)
                                <tr>
                                    <td class="fw-bold">Preferred Pickup:</td>
                                    <td>{{ $quote->preferred_pickup_date->format('M j, Y') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                @if($quote->required_delivery_date)
                                <tr>
                                    <td class="fw-bold">Required Delivery:</td>
                                    <td>{{ $quote->required_delivery_date->format('M j, Y') }}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="fw-bold">Insurance:</td>
                                    <td>
                                        @if($quote->insurance_required)
                                            <span class="badge bg-success">Required</span>
                                        @else
                                            <span class="badge bg-secondary">Not Required</span>
                                        @endif
                                    </td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Signature:</td>
                                    <td>
                                        @if($quote->signature_required)
                                            <span class="badge bg-success">Required</span>
                                        @else
                                            <span class="badge bg-secondary">Not Required</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($quote->description)
                    <div class="mt-3">
                        <h6>Description:</h6>
                        <p class="text-muted">{{ $quote->description }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Shipping Details -->
            @if($quote->isShippingQuote())
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Shipping Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">
                                <i class="fas fa-arrow-up me-1"></i>
                                Origin
                            </h6>
                            <address class="mb-0">
                                {{ $quote->origin_address }}<br>
                                {{ $quote->origin_city }}, {{ $quote->origin_state }} {{ $quote->origin_postal_code }}<br>
                                {{ $quote->origin_country }}
                            </address>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">
                                <i class="fas fa-arrow-down me-1"></i>
                                Destination
                            </h6>
                            <address class="mb-0">
                                {{ $quote->destination_address }}<br>
                                {{ $quote->destination_city }}, {{ $quote->destination_state }} {{ $quote->destination_postal_code }}<br>
                                {{ $quote->destination_country }}
                            </address>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Package Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Package Count:</td>
                                    <td>{{ $quote->package_count }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Total Weight:</td>
                                    <td>{{ $quote->total_weight }} {{ $quote->weight_unit }}</td>
                                </tr>
                                @if($quote->dimensions)
                                <tr>
                                    <td class="fw-bold">Dimensions:</td>
                                    <td>
                                        {{ $quote->dimensions['length'] ?? 0 }} × 
                                        {{ $quote->dimensions['width'] ?? 0 }} × 
                                        {{ $quote->dimensions['height'] ?? 0 }} 
                                        {{ $quote->dimensions['unit'] ?? 'cm' }}
                                    </td>
                                </tr>
                                @endif
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-bold">Package Type:</td>
                                    <td>{{ $quote->package_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Declared Value:</td>
                                    <td>@currency($quote->declared_value) {{ $quote->currency }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Special Handling:</td>
                                    <td>
                                        @if($quote->fragile)
                                            <span class="badge bg-warning me-1">Fragile</span>
                                        @endif
                                        @if($quote->hazardous)
                                            <span class="badge bg-danger">Hazardous</span>
                                        @endif
                                        @if(!$quote->fragile && !$quote->hazardous)
                                            <span class="text-muted">None</span>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    @if($quote->package_description)
                    <div class="mt-3">
                        <h6>Package Description:</h6>
                        <p class="text-muted">{{ $quote->package_description }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Product Information (for product quotes) -->
            @if($quote->isProductQuote() && $quote->products)
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        Products ({{ $quote->getProductsCount() }} items)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($quote->getProductsWithDetails() as $item)
                                <tr>
                                    <td>
                                        <strong>{{ $item['product']->name }}</strong>
                                        @if($item['notes'])
                                            <br><small class="text-muted">{{ $item['notes'] }}</small>
                                        @endif
                                    </td>
                                    <td>{{ $item['quantity'] }}</td>
                                    <td>@currency($item['price_at_time'])</td>
                                    <td>@currency($item['total'])</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr class="table-active">
                                    <th colspan="3">Products Total:</th>
                                    <th>@currency($quote->products_total)</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Pricing Information -->
            @if($quote->isQuoted())
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        Pricing
                    </h5>
                </div>
                <div class="card-body">
                    @if($quote->pricing_breakdown)
                        @foreach($quote->pricing_breakdown as $item => $amount)
                        <div class="d-flex justify-content-between">
                            <span>{{ ucwords(str_replace('_', ' ', $item)) }}:</span>
                            <span>@currency($amount)</span>
                        </div>
                        @endforeach
                        <hr>
                    @endif
                    
                    @if($quote->discount_amount > 0)
                    <div class="d-flex justify-content-between text-success">
                        <span>Discount:</span>
                        <span>-@currency($quote->discount_amount)</span>
                    </div>
                    @endif
                    
                    <div class="d-flex justify-content-between fw-bold fs-5 border-top pt-2">
                        <span>Total:</span>
                        <span class="text-primary">@currency($quote->final_price ?? $quote->quoted_price) {{ $quote->currency }}</span>
                    </div>
                </div>
            </div>
            @endif

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Contact Information
                    </h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td class="fw-bold">Name:</td>
                            <td>{{ $quote->customer_name }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Email:</td>
                            <td>{{ $quote->customer_email }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Phone:</td>
                            <td>{{ $quote->customer_phone }}</td>
                        </tr>
                        @if($quote->company_name)
                        <tr>
                            <td class="fw-bold">Company:</td>
                            <td>{{ $quote->company_name }}</td>
                        </tr>
                        @endif
                    </table>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('customer.quotes.index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Quotes
                        </a>
                        
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#notesModal">
                            <i class="fas fa-sticky-note me-1"></i> Add Notes
                        </button>
                        
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-1"></i> Print Quote
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Quote Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Reject Quote</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to reject this quote? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ route('customer.quotes.reject', $quote) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-1"></i> Reject Quote
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('customer.quotes.add-notes', $quote) }}">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Notes</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="customer_notes" class="form-label">Your Notes</label>
                        <textarea class="form-control" id="customer_notes" name="customer_notes" rows="4" 
                                  placeholder="Add any additional notes or requirements...">{{ $quote->customer_notes }}</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Save Notes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
