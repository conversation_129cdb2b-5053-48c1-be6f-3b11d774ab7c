<?php

namespace App\Mail;

use App\Models\Parcel;
use App\Models\TrackingEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ParcelStatusUpdated extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $parcel;
    public $trackingEvent;

    /**
     * Create a new message instance.
     */
    public function __construct(Parcel $parcel, TrackingEvent $trackingEvent = null)
    {
        $this->parcel = $parcel;
        $this->trackingEvent = $trackingEvent;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Package Update: ' . $this->parcel->tracking_number,
            from: config('mail.from.address', '<EMAIL>'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.parcel-status-updated',
            with: [
                'parcel' => $this->parcel,
                'trackingEvent' => $this->trackingEvent,
                'trackingUrl' => route('tracking.track') . '?tracking_number=' . $this->parcel->tracking_number,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
