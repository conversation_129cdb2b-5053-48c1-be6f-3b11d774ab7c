<?php

namespace App\Http\Controllers;

use App\Models\UserAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AddressController extends Controller
{
    /**
     * Display user addresses
     */
    public function index(): View
    {
        $addresses = Auth::user()->addresses()->orderBy('is_default', 'desc')->get();
        
        return view('frontend.addresses.index', compact('addresses'));
    }

    /**
     * Show form for creating new address
     */
    public function create(): View
    {
        return view('frontend.addresses.create');
    }

    /**
     * Store new address
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'type' => 'required|in:shipping,billing,both',
                'label' => 'nullable|string|max:255',
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'company' => 'nullable|string|max:255',
                'address_line_1' => 'required|string|max:255',
                'address_line_2' => 'nullable|string|max:255',
                'city' => 'required|string|max:255',
                'state' => 'required|string|max:255',
                'postal_code' => 'required|string|max:20',
                'country' => 'required|string|max:255',
                'phone' => 'nullable|string|max:20',
                'is_default' => 'boolean',
            ]);

            $address = Auth::user()->addresses()->create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Address added successfully.',
                'address' => $address,
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Please fix the validation errors.',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while saving the address.',
            ], 500);
        }
    }

    /**
     * Show form for editing address
     */
    public function edit(UserAddress $address): View
    {
        if ($address->user_id !== Auth::id()) {
            abort(404);
        }

        return view('frontend.addresses.edit', compact('address'));
    }

    /**
     * Update address
     */
    public function update(Request $request, UserAddress $address): JsonResponse
    {
        if ($address->user_id !== Auth::id()) {
            abort(404);
        }

        $request->validate([
            'type' => 'required|in:shipping,billing,both',
            'label' => 'nullable|string|max:255',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'company' => 'nullable|string|max:255',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'is_default' => 'boolean',
        ]);

        $address->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Address updated successfully.',
            'address' => $address,
        ]);
    }

    /**
     * Delete address
     */
    public function destroy(UserAddress $address): JsonResponse
    {
        if ($address->user_id !== Auth::id()) {
            abort(404);
        }

        $address->delete();

        return response()->json([
            'success' => true,
            'message' => 'Address deleted successfully.',
        ]);
    }

    /**
     * Set address as default
     */
    public function setDefault(UserAddress $address): JsonResponse
    {
        if ($address->user_id !== Auth::id()) {
            abort(404);
        }

        // Remove default from other addresses of same type
        Auth::user()->addresses()
            ->where('type', $address->type)
            ->where('id', '!=', $address->id)
            ->update(['is_default' => false]);

        $address->update(['is_default' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Default address updated successfully.',
        ]);
    }

    /**
     * Get addresses for AJAX (used in checkout)
     */
    public function getAddresses(Request $request): JsonResponse
    {
        $type = $request->get('type', 'both');
        
        $query = Auth::user()->addresses();
        
        if ($type !== 'both') {
            $query->where(function($q) use ($type) {
                $q->where('type', $type)->orWhere('type', 'both');
            });
        }
        
        $addresses = $query->orderBy('is_default', 'desc')->get();

        return response()->json([
            'success' => true,
            'addresses' => $addresses,
        ]);
    }
}
