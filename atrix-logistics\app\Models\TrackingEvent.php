<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class TrackingEvent extends Model
{
    protected $fillable = [
        'parcel_id',
        'status',
        'location',
        'description',
        'event_date',
        'is_public',
        'metadata',
    ];

    protected $casts = [
        'event_date' => 'datetime',
        'is_public' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Get the parcel this event belongs to
     */
    public function parcel(): BelongsTo
    {
        return $this->belongsTo(Parcel::class);
    }

    /**
     * Scope to get only public events
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to order by event date
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('event_date', 'desc');
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus(): string
    {
        return Str::title(str_replace('_', ' ', $this->status));
    }

    /**
     * Get status icon
     */
    public function getStatusIcon(): string
    {
        return match($this->status) {
            'pending' => 'clock',
            'picked_up' => 'package',
            'in_transit' => 'truck',
            'out_for_delivery' => 'map-pin',
            'delivered' => 'check-circle',
            'exception' => 'alert-triangle',
            'returned' => 'rotate-ccw',
            default => 'circle',
        };
    }

    /**
     * Get status color
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            'pending' => 'text-secondary',
            'picked_up' => 'text-info',
            'in_transit' => 'text-primary',
            'out_for_delivery' => 'text-warning',
            'delivered' => 'text-success',
            'exception' => 'text-danger',
            'returned' => 'text-dark',
            default => 'text-secondary',
        };
    }
}
