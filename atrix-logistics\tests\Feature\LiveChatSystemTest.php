<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\LiveChatSession;
use App\Models\LiveChatMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class LiveChatSystemTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);
    }

    /** @test */
    public function visitor_can_start_chat_session()
    {
        $response = $this->postJson('/api/live-chat/start-session', [
            'visitor_name' => '<PERSON>',
            'visitor_email' => '<EMAIL>',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'session_id',
                    'message'
                ]);

        $this->assertDatabaseHas('live_chat_sessions', [
            'visitor_name' => '<PERSON>',
            'visitor_email' => '<EMAIL>',
            'status' => 'waiting',
        ]);
    }

    /** @test */
    public function visitor_can_send_message()
    {
        $session = LiveChatSession::create([
            'session_id' => 'test_session_123',
            'visitor_name' => 'John Doe',
            'visitor_email' => '<EMAIL>',
            'status' => 'waiting',
            'last_activity' => now(),
        ]);

        $response = $this->postJson('/api/live-chat/send-message', [
            'session_id' => 'test_session_123',
            'message' => 'Hello, I need help with my order.',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Message sent successfully',
                ]);

        $this->assertDatabaseHas('live_chat_messages', [
            'session_id' => $session->id,
            'sender_type' => 'visitor',
            'message' => 'Hello, I need help with my order.',
        ]);

        // Check that session status changed to active
        $session->refresh();
        $this->assertEquals('active', $session->status);
    }

    /** @test */
    public function visitor_can_get_messages()
    {
        $session = LiveChatSession::create([
            'session_id' => 'test_session_123',
            'visitor_name' => 'John Doe',
            'status' => 'active',
            'last_activity' => now(),
        ]);

        // Create some messages
        LiveChatMessage::create([
            'session_id' => $session->id,
            'sender_type' => 'visitor',
            'message' => 'Hello, I need help.',
        ]);

        LiveChatMessage::create([
            'session_id' => $session->id,
            'sender_type' => 'staff',
            'staff_id' => $this->admin->id,
            'message' => 'Hi! How can I help you today?',
        ]);

        $response = $this->getJson('/api/live-chat/messages/test_session_123');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'messages' => [
                        '*' => [
                            'id',
                            'message',
                            'sender_type',
                            'sender_name',
                            'created_at',
                            'is_read'
                        ]
                    ],
                    'session_status'
                ]);

        $this->assertCount(2, $response->json('messages'));
    }

    /** @test */
    public function admin_can_view_live_chat_dashboard()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/admin/communications/live-chat');

        $response->assertStatus(200)
                ->assertViewIs('admin.live-chat.index');
    }

    /** @test */
    public function admin_can_assign_chat_session()
    {
        $this->actingAs($this->admin);

        $session = LiveChatSession::create([
            'session_id' => 'test_session_123',
            'visitor_name' => 'John Doe',
            'status' => 'waiting',
            'last_activity' => now(),
        ]);

        $response = $this->postJson("/admin/communications/live-chat/sessions/{$session->id}/assign");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Session assigned successfully'
                ]);

        $session->refresh();
        $this->assertEquals($this->admin->id, $session->assigned_to);
        $this->assertEquals('active', $session->status);
    }

    /** @test */
    public function admin_can_send_message_to_visitor()
    {
        $this->actingAs($this->admin);

        $session = LiveChatSession::create([
            'session_id' => 'test_session_123',
            'visitor_name' => 'John Doe',
            'status' => 'active',
            'assigned_to' => $this->admin->id,
            'last_activity' => now(),
        ]);

        $response = $this->postJson("/admin/communications/live-chat/sessions/{$session->id}/send-message", [
            'message' => 'Hello! How can I help you today?',
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Message sent successfully',
                ]);

        $this->assertDatabaseHas('live_chat_messages', [
            'session_id' => $session->id,
            'sender_type' => 'staff',
            'staff_id' => $this->admin->id,
            'message' => 'Hello! How can I help you today?',
        ]);
    }

    /** @test */
    public function admin_can_close_chat_session()
    {
        $this->actingAs($this->admin);

        $session = LiveChatSession::create([
            'session_id' => 'test_session_123',
            'visitor_name' => 'John Doe',
            'status' => 'active',
            'assigned_to' => $this->admin->id,
            'last_activity' => now(),
        ]);

        $response = $this->postJson("/admin/communications/live-chat/sessions/{$session->id}/close");

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Chat session closed successfully'
                ]);

        $session->refresh();
        $this->assertEquals('closed', $session->status);
    }

    /** @test */
    public function admin_can_get_live_chat_stats()
    {
        $this->actingAs($this->admin);

        // Create some test data
        LiveChatSession::create([
            'session_id' => 'active_session',
            'status' => 'active',
            'last_activity' => now(),
        ]);

        LiveChatSession::create([
            'session_id' => 'waiting_session',
            'status' => 'waiting',
            'last_activity' => now(),
        ]);

        $response = $this->getJson('/admin/communications/live-chat/stats');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'total_active',
                    'total_waiting',
                    'total_unread',
                    'my_assigned',
                    'recent_sessions'
                ]);
    }

    /** @test */
    public function visitor_cannot_access_admin_routes()
    {
        $response = $this->get('/admin/communications/live-chat');
        $response->assertRedirect('/login');

        $response = $this->getJson('/admin/communications/live-chat/stats');
        $response->assertStatus(401);
    }
}
