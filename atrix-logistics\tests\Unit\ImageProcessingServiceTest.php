<?php

namespace Tests\Unit;

use App\Services\ImageProcessingService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ImageProcessingServiceTest extends TestCase
{
    protected ImageProcessingService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new ImageProcessingService();
        Storage::fake('public');
    }

    /** @test */
    public function it_can_validate_valid_image()
    {
        $file = UploadedFile::fake()->image('test.jpg', 100, 100);
        
        // This should not throw an exception
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateImage');
        $method->setAccessible(true);
        
        $this->expectNotToPerformAssertions();
        $method->invoke($this->service, $file);
    }

    /** @test */
    public function it_rejects_oversized_images()
    {
        // Create a fake file that's too large (6MB)
        $file = UploadedFile::fake()->create('large.jpg', 6 * 1024);
        
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('validateImage');
        $method->setAccessible(true);
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Image file size must be less than 5MB');
        
        $method->invoke($this->service, $file);
    }

    /** @test */
    public function it_generates_unique_filenames()
    {
        $file = UploadedFile::fake()->image('test.jpg');
        
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('generateFilename');
        $method->setAccessible(true);
        
        $filename1 = $method->invoke($this->service, 'site_logo', $file);
        $filename2 = $method->invoke($this->service, 'site_logo', $file);
        
        $this->assertNotEquals($filename1, $filename2);
        $this->assertStringContainsString('site_logo', $filename1);
    }
}
