<x-guest-layout>
    <!-- Session Status -->
    <x-auth-session-status class="mb-4" :status="session('status')" />

    <!-- Login Method Tabs -->
    <div class="mb-6">
        <div class="flex border-b border-gray-200">
            <button type="button"
                    class="flex-1 py-3 px-4 text-sm font-medium text-center border-b-2 border-indigo-500 text-indigo-600 bg-indigo-50 login-tab active"
                    data-tab="email">
                <i class="fas fa-envelope mr-2"></i>
                Email & Password
            </button>
            <button type="button"
                    class="flex-1 py-3 px-4 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 login-tab"
                    data-tab="phone">
                <i class="fas fa-mobile-alt mr-2"></i>
                Phone & OTP
            </button>
        </div>
    </div>

    <!-- Email Login Form -->
    <div id="email-login" class="login-form active">
        <form method="POST" action="{{ route('login') }}">
            @csrf

            <!-- Email Address -->
            <div>
                <x-input-label for="email" :value="__('Email')" />
                <x-text-input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')" required autofocus autocomplete="username" />
                <x-input-error :messages="$errors->get('email')" class="mt-2" />
            </div>

            <!-- Password -->
            <div class="mt-4">
                <x-input-label for="password" :value="__('Password')" />
                <x-text-input id="password" class="block mt-1 w-full"
                                type="password"
                                name="password"
                                required autocomplete="current-password" />
                <x-input-error :messages="$errors->get('password')" class="mt-2" />
            </div>

            <!-- Remember Me -->
            <div class="block mt-4">
                <label for="remember_me" class="inline-flex items-center">
                    <input id="remember_me" type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" name="remember">
                    <span class="ms-2 text-sm text-gray-600">{{ __('Remember me') }}</span>
                </label>
            </div>

            <div class="flex items-center justify-end mt-4">
                @if (Route::has('password.request'))
                    <a class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" href="{{ route('password.request') }}">
                        {{ __('Forgot your password?') }}
                    </a>
                @endif

                <x-primary-button class="ms-3">
                    {{ __('Log in') }}
                </x-primary-button>
            </div>
        </form>
    </div>

    <!-- Phone OTP Login Form -->
    <div id="phone-login" class="login-form hidden">
        <form id="phone-otp-form">
            @csrf

            <!-- Phone Number Step -->
            <div id="phone-step" class="otp-step active">
                <div>
                    <x-input-label for="phone" value="Phone Number" />
                    <x-text-input id="phone" class="block mt-1 w-full" type="tel" name="phone" placeholder="+****************" required />
                    <div id="phone-error" class="mt-2 text-sm text-red-600 hidden"></div>
                    <p class="mt-1 text-xs text-gray-500">Enter your registered phone number to receive an OTP</p>
                </div>

                <div class="flex items-center justify-end mt-6">
                    <button type="button" id="send-otp-btn" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send OTP
                    </button>
                </div>
            </div>

            <!-- OTP Verification Step -->
            <div id="otp-step" class="otp-step hidden">
                <div>
                    <x-input-label for="otp" value="Verification Code" />
                    <x-text-input id="otp" class="block mt-1 w-full text-center text-2xl tracking-widest" type="text" name="otp" placeholder="000000" maxlength="6" required />
                    <div id="otp-error" class="mt-2 text-sm text-red-600 hidden"></div>
                    <p class="mt-1 text-xs text-gray-500">Enter the 6-digit code sent to your phone</p>
                </div>

                <!-- OTP Timer -->
                <div class="mt-4 text-center">
                    <div id="otp-timer" class="text-sm text-gray-600">
                        Code expires in <span id="timer-countdown">5:00</span>
                    </div>
                    <button type="button" id="resend-otp-btn" class="mt-2 text-sm text-indigo-600 hover:text-indigo-500 hidden">
                        Resend Code
                    </button>
                </div>

                <div class="flex items-center justify-between mt-6">
                    <button type="button" id="back-to-phone-btn" class="inline-flex items-center px-4 py-2 bg-gray-300 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-400 focus:bg-gray-400 active:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back
                    </button>
                    <button type="button" id="verify-otp-btn" class="inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700 focus:bg-indigo-700 active:bg-indigo-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                        <i class="fas fa-check mr-2"></i>
                        Verify & Login
                    </button>
                </div>
            </div>
        </form>
    </div>

    @push('styles')
    <style>
        .login-tab.active {
            border-color: #4f46e5;
            color: #4f46e5;
            background-color: #eef2ff;
        }

        .login-form {
            transition: all 0.3s ease;
        }

        .login-form.hidden {
            display: none;
        }

        .otp-step {
            transition: all 0.3s ease;
        }

        .otp-step.hidden {
            display: none;
        }

        #otp {
            letter-spacing: 0.5em;
        }

        .btn-loading {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-loading .fas {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
    @endpush

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching
            const tabs = document.querySelectorAll('.login-tab');
            const forms = document.querySelectorAll('.login-form');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;

                    // Update tab appearance
                    tabs.forEach(t => {
                        t.classList.remove('active', 'border-indigo-500', 'text-indigo-600', 'bg-indigo-50');
                        t.classList.add('border-transparent', 'text-gray-500');
                    });

                    this.classList.add('active', 'border-indigo-500', 'text-indigo-600', 'bg-indigo-50');
                    this.classList.remove('border-transparent', 'text-gray-500');

                    // Show/hide forms
                    forms.forEach(form => {
                        form.classList.add('hidden');
                        form.classList.remove('active');
                    });

                    const targetForm = document.getElementById(targetTab + '-login');
                    if (targetForm) {
                        targetForm.classList.remove('hidden');
                        targetForm.classList.add('active');
                    }
                });
            });

            // OTP Login functionality
            const phoneInput = document.getElementById('phone');
            const otpInput = document.getElementById('otp');
            const sendOtpBtn = document.getElementById('send-otp-btn');
            const verifyOtpBtn = document.getElementById('verify-otp-btn');
            const resendOtpBtn = document.getElementById('resend-otp-btn');
            const backToPhoneBtn = document.getElementById('back-to-phone-btn');
            const phoneStep = document.getElementById('phone-step');
            const otpStep = document.getElementById('otp-step');
            const phoneError = document.getElementById('phone-error');
            const otpError = document.getElementById('otp-error');
            const timerCountdown = document.getElementById('timer-countdown');

            let otpTimer = null;
            let currentPhone = '';

            // Send OTP
            sendOtpBtn.addEventListener('click', function() {
                const phone = phoneInput.value.trim();

                if (!phone) {
                    showError(phoneError, 'Please enter your phone number');
                    return;
                }

                sendOtp(phone);
            });

            // Verify OTP
            verifyOtpBtn.addEventListener('click', function() {
                const otp = otpInput.value.trim();

                if (!otp || otp.length !== 6) {
                    showError(otpError, 'Please enter the 6-digit verification code');
                    return;
                }

                verifyOtp(currentPhone, otp);
            });

            // Resend OTP
            resendOtpBtn.addEventListener('click', function() {
                sendOtp(currentPhone, true);
            });

            // Back to phone step
            backToPhoneBtn.addEventListener('click', function() {
                showPhoneStep();
                clearTimer();
            });

            // Auto-format phone number
            phoneInput.addEventListener('input', function() {
                let value = this.value.replace(/\D/g, '');
                if (value.length >= 10) {
                    value = value.substring(0, 10);
                    this.value = `(${value.substring(0, 3)}) ${value.substring(3, 6)}-${value.substring(6)}`;
                }
            });

            // Auto-submit OTP when 6 digits entered
            otpInput.addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '');
                if (this.value.length === 6) {
                    setTimeout(() => verifyOtp(currentPhone, this.value), 500);
                }
            });

            function sendOtp(phone, isResend = false) {
                setLoading(sendOtpBtn, true);
                hideError(phoneError);

                fetch('/auth/otp/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ phone: phone })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentPhone = phone;
                        showOtpStep();
                        startTimer(300); // 5 minutes

                        if (isResend) {
                            showSuccess('New verification code sent!');
                        }
                    } else {
                        showError(phoneError, data.message);
                    }
                })
                .catch(error => {
                    showError(phoneError, 'Failed to send OTP. Please try again.');
                })
                .finally(() => {
                    setLoading(sendOtpBtn, false);
                });
            }

            function verifyOtp(phone, otp) {
                setLoading(verifyOtpBtn, true);
                hideError(otpError);

                fetch('/auth/otp/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ phone: phone, otp: otp })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess('Login successful! Redirecting...');
                        setTimeout(() => {
                            window.location.href = data.redirect_url || '/dashboard';
                        }, 1000);
                    } else {
                        showError(otpError, data.message);
                        otpInput.value = '';
                        otpInput.focus();
                    }
                })
                .catch(error => {
                    showError(otpError, 'Verification failed. Please try again.');
                    otpInput.value = '';
                    otpInput.focus();
                })
                .finally(() => {
                    setLoading(verifyOtpBtn, false);
                });
            }

            function showPhoneStep() {
                phoneStep.classList.remove('hidden');
                phoneStep.classList.add('active');
                otpStep.classList.add('hidden');
                otpStep.classList.remove('active');
                phoneInput.focus();
            }

            function showOtpStep() {
                phoneStep.classList.add('hidden');
                phoneStep.classList.remove('active');
                otpStep.classList.remove('hidden');
                otpStep.classList.add('active');
                otpInput.focus();
            }

            function startTimer(seconds) {
                clearTimer();
                resendOtpBtn.classList.add('hidden');

                otpTimer = setInterval(() => {
                    const minutes = Math.floor(seconds / 60);
                    const remainingSeconds = seconds % 60;
                    timerCountdown.textContent = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;

                    if (seconds <= 0) {
                        clearTimer();
                        resendOtpBtn.classList.remove('hidden');
                        timerCountdown.textContent = 'Code expired';
                    }

                    seconds--;
                }, 1000);
            }

            function clearTimer() {
                if (otpTimer) {
                    clearInterval(otpTimer);
                    otpTimer = null;
                }
            }

            function setLoading(button, loading) {
                if (loading) {
                    button.classList.add('btn-loading');
                    button.disabled = true;
                    const icon = button.querySelector('.fas');
                    if (icon) {
                        icon.className = 'fas fa-spinner mr-2';
                    }
                } else {
                    button.classList.remove('btn-loading');
                    button.disabled = false;
                    const icon = button.querySelector('.fas');
                    if (icon) {
                        if (button.id === 'send-otp-btn') {
                            icon.className = 'fas fa-paper-plane mr-2';
                        } else if (button.id === 'verify-otp-btn') {
                            icon.className = 'fas fa-check mr-2';
                        }
                    }
                }
            }

            function showError(element, message) {
                element.textContent = message;
                element.classList.remove('hidden');
            }

            function hideError(element) {
                element.classList.add('hidden');
            }

            function showSuccess(message) {
                // You can implement a toast notification here
                console.log('Success:', message);
            }
        });
    </script>
    @endpush
</x-guest-layout>
