<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Parcel;
use App\Models\SupportTicket;
use App\Models\Wishlist;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class CustomerAnalyticsController extends Controller
{
    /**
     * Display customer analytics dashboard
     */
    public function index(): View
    {
        // Customer Overview Statistics
        $customerStats = $this->getCustomerOverviewStats();

        // Customer Acquisition Metrics
        $acquisitionMetrics = $this->getCustomerAcquisitionMetrics();

        // Customer Lifetime Value
        $lifetimeValueMetrics = $this->getCustomerLifetimeValueMetrics();

        // Purchase Behavior Analysis
        $purchaseBehavior = $this->getPurchaseBehaviorAnalysis();

        // Customer Segmentation
        $customerSegmentation = $this->getCustomerSegmentation();

        // Recent Customer Activity
        $recentActivity = $this->getRecentCustomerActivity();

        // Top Customers
        $topCustomers = $this->getTopCustomers();

        return view('admin.analytics.customers', compact(
            'customerStats',
            'acquisitionMetrics',
            'lifetimeValueMetrics',
            'purchaseBehavior',
            'customerSegmentation',
            'recentActivity',
            'topCustomers'
        ));
    }

    /**
     * Get customer overview statistics
     */
    private function getCustomerOverviewStats(): array
    {
        $totalCustomers = User::where('role', 'customer')->count();
        $activeCustomers = User::where('role', 'customer')
                              ->where('is_active', true)
                              ->count();
        $newCustomersThisMonth = User::where('role', 'customer')
                                   ->whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->count();
        $customersWithOrders = User::where('role', 'customer')
                                  ->whereHas('orders')
                                  ->count();

        return [
            'total_customers' => $totalCustomers,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $totalCustomers - $activeCustomers,
            'new_customers_this_month' => $newCustomersThisMonth,
            'customers_with_orders' => $customersWithOrders,
            'customers_without_orders' => $totalCustomers - $customersWithOrders,
            'conversion_rate' => $totalCustomers > 0 ? round(($customersWithOrders / $totalCustomers) * 100, 2) : 0,
        ];
    }

    /**
     * Get customer acquisition metrics
     */
    private function getCustomerAcquisitionMetrics(): array
    {
        // Monthly customer acquisition for the last 12 months
        $monthlyAcquisition = User::where('role', 'customer')
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function($item) {
                return [
                    'period' => Carbon::create($item->year, $item->month, 1)->format('M Y'),
                    'count' => $item->count,
                    'date' => Carbon::create($item->year, $item->month, 1)
                ];
            });

        // Customer acquisition sources (simulated - you can enhance this based on your tracking)
        // Check if referral_source column exists
        $hasReferralSource = Schema::hasColumn('users', 'referral_source');

        if ($hasReferralSource) {
            $acquisitionSources = [
                'Direct' => User::where('role', 'customer')->whereNull('referral_source')->count(),
                'Organic Search' => User::where('role', 'customer')->where('referral_source', 'organic')->count(),
                'Social Media' => User::where('role', 'customer')->where('referral_source', 'social')->count(),
                'Email Marketing' => User::where('role', 'customer')->where('referral_source', 'email')->count(),
                'Referral' => User::where('role', 'customer')->where('referral_source', 'referral')->count(),
            ];
        } else {
            // Fallback when referral_source column doesn't exist
            $totalCustomers = User::where('role', 'customer')->count();
            $acquisitionSources = [
                'Direct' => $totalCustomers,
                'Organic Search' => 0,
                'Social Media' => 0,
                'Email Marketing' => 0,
                'Referral' => 0,
            ];
        }

        return [
            'monthly_acquisition' => $monthlyAcquisition,
            'acquisition_sources' => $acquisitionSources,
            'total_this_month' => User::where('role', 'customer')
                                    ->whereMonth('created_at', now()->month)
                                    ->count(),
            'growth_rate' => $this->calculateGrowthRate(),
        ];
    }

    /**
     * Get customer lifetime value metrics
     */
    private function getCustomerLifetimeValueMetrics(): array
    {
        // Average Customer Lifetime Value
        $customerLTV = User::where('role', 'customer')
            ->whereHas('orders', function($query) {
                $query->where('payment_status', 'paid');
            })
            ->withSum(['orders as total_spent' => function($query) {
                $query->where('payment_status', 'paid');
            }], 'total_amount')
            ->get();

        $avgLTV = $customerLTV->avg('total_spent') ?? 0;
        $totalRevenue = $customerLTV->sum('total_spent') ?? 0;
        $payingCustomers = $customerLTV->where('total_spent', '>', 0)->count();

        // Customer value segments
        $valueSegments = [
            'high_value' => $customerLTV->where('total_spent', '>=', 1000)->count(),
            'medium_value' => $customerLTV->whereBetween('total_spent', [500, 999.99])->count(),
            'low_value' => $customerLTV->whereBetween('total_spent', [100, 499.99])->count(),
            'minimal_value' => $customerLTV->whereBetween('total_spent', [0.01, 99.99])->count(),
        ];

        // Average order value
        $avgOrderValue = Order::where('payment_status', 'paid')->avg('total_amount') ?? 0;

        // Customer retention metrics
        $retentionMetrics = $this->getCustomerRetentionMetrics();

        return [
            'average_ltv' => round($avgLTV, 2),
            'total_revenue' => round($totalRevenue, 2),
            'paying_customers' => $payingCustomers,
            'average_order_value' => round($avgOrderValue, 2),
            'value_segments' => $valueSegments,
            'retention_metrics' => $retentionMetrics,
        ];
    }

    /**
     * Get purchase behavior analysis
     */
    private function getPurchaseBehaviorAnalysis(): array
    {
        // Order frequency analysis
        $orderFrequency = User::where('role', 'customer')
            ->withCount('orders')
            ->get()
            ->groupBy(function($customer) {
                if ($customer->orders_count == 0) return 'no_orders';
                if ($customer->orders_count == 1) return 'one_time';
                if ($customer->orders_count <= 5) return 'occasional';
                if ($customer->orders_count <= 10) return 'regular';
                return 'frequent';
            })
            ->map->count();

        // Popular products among customers
        $popularProducts = DB::table('order_items')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->join('users', 'orders.customer_id', '=', 'users.id')
            ->where('users.role', 'customer')
            ->where('orders.payment_status', 'paid')
            ->select('order_items.product_name', DB::raw('SUM(order_items.quantity) as total_sold'))
            ->groupBy('order_items.product_name')
            ->orderBy('total_sold', 'desc')
            ->limit(10)
            ->get();

        // Purchase timing analysis
        $purchaseTimings = Order::whereHas('customer', function($query) {
                $query->where('role', 'customer');
            })
            ->where('payment_status', 'paid')
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour');

        // Monthly purchase patterns
        $monthlyPurchases = Order::whereHas('customer', function($query) {
                $query->where('role', 'customer');
            })
            ->where('payment_status', 'paid')
            ->where('created_at', '>=', now()->subMonths(12))
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as orders, SUM(total_amount) as revenue')
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get()
            ->map(function($item) {
                return [
                    'period' => Carbon::create($item->year, $item->month, 1)->format('M Y'),
                    'orders' => $item->orders,
                    'revenue' => $item->revenue,
                ];
            });

        return [
            'order_frequency' => $orderFrequency,
            'popular_products' => $popularProducts,
            'purchase_timings' => $purchaseTimings,
            'monthly_purchases' => $monthlyPurchases,
        ];
    }

    /**
     * Get customer segmentation
     */
    private function getCustomerSegmentation(): array
    {
        // RFM Analysis (Recency, Frequency, Monetary)
        $customers = User::where('role', 'customer')
            ->with(['orders' => function($query) {
                $query->where('payment_status', 'paid');
            }])
            ->get()
            ->map(function($customer) {
                $orders = $customer->orders;
                $lastOrderDate = $orders->max('created_at');
                $totalOrders = $orders->count();
                $totalSpent = $orders->sum('total_amount');

                // Calculate recency (days since last order)
                $recency = $lastOrderDate ? now()->diffInDays($lastOrderDate) : 999;

                return [
                    'customer_id' => $customer->id,
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'recency' => $recency,
                    'frequency' => $totalOrders,
                    'monetary' => $totalSpent,
                ];
            });

        // Segment customers based on RFM scores
        $segments = [
            'champions' => 0,      // High F, High M, Low R
            'loyal_customers' => 0, // High F, Medium M, Low R
            'potential_loyalists' => 0, // Medium F, Medium M, Low R
            'new_customers' => 0,   // Low F, Low M, Low R
            'promising' => 0,       // Low F, High M, Low R
            'need_attention' => 0,  // Medium F, Medium M, Medium R
            'about_to_sleep' => 0,  // Low F, Low M, Medium R
            'at_risk' => 0,         // High F, High M, High R
            'cannot_lose_them' => 0, // High F, High M, Medium R
            'hibernating' => 0,     // Low F, Low M, High R
        ];

        foreach ($customers as $customer) {
            $segment = $this->categorizeCustomer($customer);
            if (isset($segments[$segment])) {
                $segments[$segment]++;
            }
        }

        // Geographic distribution
        $geographicDistribution = User::where('role', 'customer')
            ->selectRaw('COALESCE(state, "Unknown") as state, COUNT(*) as count')
            ->groupBy('state')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get()
            ->pluck('count', 'state');

        return [
            'rfm_segments' => $segments,
            'geographic_distribution' => $geographicDistribution,
            'total_analyzed' => $customers->count(),
        ];
    }

    /**
     * Get recent customer activity
     */
    private function getRecentCustomerActivity(): array
    {
        $recentRegistrations = User::where('role', 'customer')
            ->latest()
            ->limit(10)
            ->get(['id', 'name', 'email', 'created_at']);

        $recentOrders = Order::with('customer')
            ->whereHas('customer', function($query) {
                $query->where('role', 'customer');
            })
            ->latest()
            ->limit(10)
            ->get();

        $recentSupportTickets = SupportTicket::with('user')
            ->whereHas('user', function($query) {
                $query->where('role', 'customer');
            })
            ->latest()
            ->limit(10)
            ->get();

        return [
            'recent_registrations' => $recentRegistrations,
            'recent_orders' => $recentOrders,
            'recent_support_tickets' => $recentSupportTickets,
        ];
    }

    /**
     * Get top customers
     */
    private function getTopCustomers(): array
    {
        $topByRevenue = User::where('role', 'customer')
            ->withSum(['orders as total_revenue' => function($query) {
                $query->where('payment_status', 'paid');
            }], 'total_amount')
            ->withCount(['orders as total_orders' => function($query) {
                $query->where('payment_status', 'paid');
            }])
            ->orderBy('total_revenue', 'desc')
            ->limit(10)
            ->get();

        $topByOrders = User::where('role', 'customer')
            ->withCount(['orders as total_orders'])
            ->withSum(['orders as total_revenue' => function($query) {
                $query->where('payment_status', 'paid');
            }], 'total_amount')
            ->orderBy('total_orders', 'desc')
            ->limit(10)
            ->get();

        return [
            'top_by_revenue' => $topByRevenue,
            'top_by_orders' => $topByOrders,
        ];
    }

    /**
     * Helper Methods
     */
    private function calculateGrowthRate(): float
    {
        $thisMonth = User::where('role', 'customer')
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        $lastMonth = User::where('role', 'customer')
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();

        if ($lastMonth == 0) {
            return $thisMonth > 0 ? 100 : 0;
        }

        return round((($thisMonth - $lastMonth) / $lastMonth) * 100, 2);
    }

    private function getCustomerRetentionMetrics(): array
    {
        // Calculate retention rate (customers who made repeat purchases)
        $totalCustomersWithOrders = User::where('role', 'customer')
            ->whereHas('orders')
            ->count();

        $repeatCustomers = User::where('role', 'customer')
            ->whereHas('orders', function($query) {
                $query->havingRaw('COUNT(*) > 1');
            })
            ->count();

        $retentionRate = $totalCustomersWithOrders > 0
            ? round(($repeatCustomers / $totalCustomersWithOrders) * 100, 2)
            : 0;

        // Churn analysis (customers who haven't ordered in 90 days)
        $churnedCustomers = User::where('role', 'customer')
            ->whereHas('orders', function($query) {
                $query->where('created_at', '<', now()->subDays(90));
            })
            ->whereDoesntHave('orders', function($query) {
                $query->where('created_at', '>=', now()->subDays(90));
            })
            ->count();

        return [
            'retention_rate' => $retentionRate,
            'repeat_customers' => $repeatCustomers,
            'churned_customers' => $churnedCustomers,
        ];
    }

    private function categorizeCustomer(array $customer): string
    {
        $recency = $customer['recency'];
        $frequency = $customer['frequency'];
        $monetary = $customer['monetary'];

        // Define thresholds (you can adjust these based on your business)
        $recencyThresholds = [30, 90]; // Low: <30, Medium: 30-90, High: >90
        $frequencyThresholds = [2, 5];  // Low: <2, Medium: 2-5, High: >5
        $monetaryThresholds = [100, 500]; // Low: <100, Medium: 100-500, High: >500

        // Score recency (lower is better)
        $rScore = $recency <= $recencyThresholds[0] ? 3 : ($recency <= $recencyThresholds[1] ? 2 : 1);

        // Score frequency (higher is better)
        $fScore = $frequency >= $frequencyThresholds[1] ? 3 : ($frequency >= $frequencyThresholds[0] ? 2 : 1);

        // Score monetary (higher is better)
        $mScore = $monetary >= $monetaryThresholds[1] ? 3 : ($monetary >= $monetaryThresholds[0] ? 2 : 1);

        // Categorize based on RFM scores
        if ($rScore >= 2 && $fScore >= 3 && $mScore >= 3) return 'champions';
        if ($rScore >= 2 && $fScore >= 2 && $mScore >= 2) return 'loyal_customers';
        if ($rScore >= 2 && $fScore >= 2 && $mScore >= 1) return 'potential_loyalists';
        if ($rScore >= 2 && $fScore == 1 && $mScore == 1) return 'new_customers';
        if ($rScore >= 2 && $fScore == 1 && $mScore >= 2) return 'promising';
        if ($rScore == 2 && $fScore >= 2 && $mScore >= 2) return 'need_attention';
        if ($rScore == 2 && $fScore == 1 && $mScore == 1) return 'about_to_sleep';
        if ($rScore == 1 && $fScore >= 2 && $mScore >= 2) return 'at_risk';
        if ($rScore == 1 && $fScore >= 3 && $mScore >= 3) return 'cannot_lose_them';

        return 'hibernating';
    }

    /**
     * API endpoints for AJAX requests
     */
    public function getCustomerGrowthData(Request $request): JsonResponse
    {
        $period = $request->get('period', '12'); // months

        $data = User::where('role', 'customer')
            ->where('created_at', '>=', now()->subMonths($period))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return response()->json($data);
    }

    public function getRevenueByCustomerSegment(Request $request): JsonResponse
    {
        $segments = $this->getCustomerSegmentation();

        return response()->json($segments['rfm_segments']);
    }
}
