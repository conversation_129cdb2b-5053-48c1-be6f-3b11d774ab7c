<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageProcessingService
{
    protected ImageManager $imageManager;

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Process and convert uploaded image
     */
    public function processImage(UploadedFile $file, string $directory, string $settingKey): string
    {
        // Validate file
        $this->validateImage($file);

        // Generate unique filename
        $filename = $this->generateFilename($settingKey, $file);

        // Determine conversion type based on setting
        if ($settingKey === 'site_favicon') {
            return $this->convertToIco($file, $directory, $filename);
        } else {
            return $this->convertToWebP($file, $directory, $filename);
        }
    }

    /**
     * Process image with custom options
     */
    public function processImageWithOptions(UploadedFile $file, array $options = []): string
    {
        $options = array_merge([
            'directory' => 'uploads/images',
            'prefix' => 'img',
            'format' => 'webp', // webp, ico, original
            'quality' => 85,
            'max_width' => 1920,
            'max_height' => 1080,
            'resize_for_thumbnails' => false,
            'thumbnail_sizes' => [], // ['small' => [150, 150], 'medium' => [300, 300]]
        ], $options);

        // Validate file
        $this->validateImage($file);

        // Generate unique filename
        $filename = $this->generateFilename($options['prefix'], $file);

        // Process based on format
        switch ($options['format']) {
            case 'ico':
                return $this->convertToIco($file, $options['directory'], $filename);
            case 'webp':
                return $this->convertToWebPWithOptions($file, $options['directory'], $filename, $options);
            case 'original':
                return $this->storeOriginal($file, $options['directory'], $filename);
            default:
                return $this->convertToWebPWithOptions($file, $options['directory'], $filename, $options);
        }
    }

    /**
     * Convert image to WebP format
     */
    public function convertToWebP(UploadedFile $file, string $directory, string $filename): string
    {
        return $this->convertToWebPWithOptions($file, $directory, $filename, [
            'quality' => 85,
            'max_width' => 1920,
            'max_height' => 1080
        ]);
    }

    /**
     * Convert image to WebP format with custom options
     */
    public function convertToWebPWithOptions(UploadedFile $file, string $directory, string $filename, array $options): string
    {
        // Create image instance
        $image = $this->imageManager->read($file->getPathname());

        // Optimize and resize if needed
        $image = $this->optimizeImageWithOptions($image, $options);

        // Generate WebP filename
        $webpFilename = pathinfo($filename, PATHINFO_FILENAME) . '.webp';
        $fullPath = $directory . '/' . $webpFilename;

        // Convert to WebP and save
        $webpData = $image->toWebp($options['quality'] ?? 85);
        Storage::disk('public')->put($fullPath, $webpData);

        return $fullPath;
    }

    /**
     * Store original file without conversion
     */
    public function storeOriginal(UploadedFile $file, string $directory, string $filename): string
    {
        $fullPath = $directory . '/' . $filename;
        Storage::disk('public')->putFileAs($directory, $file, $filename);
        return $fullPath;
    }

    /**
     * Convert image to ICO format for favicon
     */
    public function convertToIco(UploadedFile $file, string $directory, string $filename): string
    {
        // Create image instance
        $image = $this->imageManager->read($file->getPathname());

        // Resize to standard favicon sizes and optimize
        $image = $image->resize(32, 32);

        // Generate ICO filename
        $icoFilename = pathinfo($filename, PATHINFO_FILENAME) . '.ico';
        $fullPath = $directory . '/' . $icoFilename;

        // For ICO, we'll save as PNG first (most browsers support PNG favicons)
        // True ICO conversion requires additional libraries
        $pngData = $image->toPng();
        Storage::disk('public')->put($fullPath, $pngData);

        return $fullPath;
    }

    /**
     * Optimize image (resize, compress)
     */
    protected function optimizeImage($image)
    {
        return $this->optimizeImageWithOptions($image, [
            'max_width' => 1920,
            'max_height' => 1080
        ]);
    }

    /**
     * Optimize image with custom options
     */
    protected function optimizeImageWithOptions($image, array $options)
    {
        // Get current dimensions
        $width = $image->width();
        $height = $image->height();

        // Set maximum dimensions
        $maxWidth = $options['max_width'] ?? 1920;
        $maxHeight = $options['max_height'] ?? 1080;

        // Resize if image is too large
        if ($width > $maxWidth || $height > $maxHeight) {
            $image = $image->resize($maxWidth, $maxHeight, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            });
        }

        return $image;
    }

    /**
     * Validate uploaded image
     */
    protected function validateImage(UploadedFile $file): void
    {
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            throw new \InvalidArgumentException('Image file size must be less than 5MB');
        }

        // Check MIME type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid image format. Only JPEG, PNG, GIF, and WebP are allowed');
        }

        // Additional security check - verify it's actually an image
        $imageInfo = getimagesize($file->getPathname());
        if ($imageInfo === false) {
            throw new \InvalidArgumentException('Invalid image file');
        }
    }

    /**
     * Generate unique filename
     */
    protected function generateFilename(string $settingKey, UploadedFile $file): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        $extension = $file->getClientOriginalExtension();
        
        return "{$settingKey}_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Delete old image file
     */
    public function deleteOldImage(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }

        return true;
    }

    /**
     * Quick helper methods for common use cases
     */
    public function processProductImage(UploadedFile $file): string
    {
        return $this->processImageWithOptions($file, [
            'directory' => 'uploads/products',
            'prefix' => 'product',
            'format' => 'webp',
            'quality' => 85,
            'max_width' => 1200,
            'max_height' => 1200
        ]);
    }

    public function processSliderImage(UploadedFile $file): string
    {
        return $this->processImageWithOptions($file, [
            'directory' => 'uploads/sliders',
            'prefix' => 'slider',
            'format' => 'webp',
            'quality' => 90,
            'max_width' => 1920,
            'max_height' => 1080
        ]);
    }

    public function processCategoryImage(UploadedFile $file): string
    {
        return $this->processImageWithOptions($file, [
            'directory' => 'uploads/categories',
            'prefix' => 'category',
            'format' => 'webp',
            'quality' => 85,
            'max_width' => 800,
            'max_height' => 600
        ]);
    }

    public function processTeamMemberPhoto(UploadedFile $file): string
    {
        return $this->processImageWithOptions($file, [
            'directory' => 'uploads/team',
            'prefix' => 'team',
            'format' => 'webp',
            'quality' => 85,
            'max_width' => 750,
            'max_height' => 750
        ]);
    }
}
