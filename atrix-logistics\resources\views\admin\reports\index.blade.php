@extends('layouts.admin')

@section('title', 'Reports & Analytics')
@section('page-title', 'Reports & Analytics')

@section('page-actions')
    <div class="btn-group" role="group">
        <button type="button" class="btn btn-outline-primary" onclick="refreshReports()">
            <i class="fas fa-sync me-1"></i> Refresh Data
        </button>
        <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#exportModal">
            <i class="fas fa-download me-1"></i> Export Data
        </button>
    </div>
@endsection

@section('content')
    <!-- Period Selection -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-0">Analytics Period</h6>
                            <small class="text-muted">Select the time period for analytics data</small>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group w-100" role="group" id="periodSelector">
                                <input type="radio" class="btn-check" name="period" id="period7" value="7">
                                <label class="btn btn-outline-primary" for="period7">7 Days</label>

                                <input type="radio" class="btn-check" name="period" id="period30" value="30" checked>
                                <label class="btn btn-outline-primary" for="period30">30 Days</label>

                                <input type="radio" class="btn-check" name="period" id="period90" value="90">
                                <label class="btn btn-outline-primary" for="period90">90 Days</label>

                                <input type="radio" class="btn-check" name="period" id="period365" value="365">
                                <label class="btn btn-outline-primary" for="period365">1 Year</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Parcels Trend Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Parcels Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="parcelsTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Status Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Status Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusDistributionChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue and Carrier Performance -->
    <div class="row mb-4">
        <!-- Revenue Trend -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Revenue Trend</h6>
                </div>
                <div class="card-body">
                    <canvas id="revenueTrendChart" height="150"></canvas>
                </div>
            </div>
        </div>

        <!-- Carrier Performance -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Carrier Performance</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm" id="carrierPerformanceTable">
                            <thead>
                                <tr>
                                    <th>Carrier</th>
                                    <th>Parcels</th>
                                    <th>Delivery Rate</th>
                                    <th>Avg Cost</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Analytics -->
    <div class="row">
        <!-- Top Customers -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Top Customers</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="topCustomersTable">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Email</th>
                                    <th>Parcels</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Registration Trend -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Growth</h6>
                </div>
                <div class="card-body">
                    <canvas id="customerTrendChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div class="modal fade" id="exportModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Export Data</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="{{ route('admin.reports.export-parcels') }}" method="GET">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="export_status" class="form-label">Status Filter</label>
                                <select class="form-select" id="export_status" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="pending">Pending</option>
                                    <option value="picked_up">Picked Up</option>
                                    <option value="in_transit">In Transit</option>
                                    <option value="out_for_delivery">Out for Delivery</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="exception">Exception</option>
                                    <option value="returned">Returned</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="export_carrier" class="form-label">Carrier Filter</label>
                                <select class="form-select" id="export_carrier" name="carrier_id">
                                    <option value="">All Carriers</option>
                                    <!-- Will be populated via AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="export_date_from" class="form-label">From Date</label>
                                <input type="date" class="form-control" id="export_date_from" name="date_from">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="export_date_to" class="form-label">To Date</label>
                                <input type="date" class="form-control" id="export_date_to" name="date_to">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-download me-1"></i> Export CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    let parcelsTrendChart, statusDistributionChart, revenueTrendChart, customerTrendChart;

    // Initialize charts on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadReportsData();
        loadCustomerAnalytics();
        
        // Period selector change event
        document.querySelectorAll('input[name="period"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.checked) {
                    loadReportsData();
                }
            });
        });
    });

    function getSelectedPeriod() {
        return document.querySelector('input[name="period"]:checked').value;
    }

    function loadReportsData() {
        const period = getSelectedPeriod();
        
        fetch(`{{ route('admin.reports.parcels-analytics') }}?period=${period}`)
            .then(response => response.json())
            .then(data => {
                updateParcelsTrendChart(data.daily_parcels);
                updateStatusDistributionChart(data.status_distribution);
                updateRevenueTrendChart(data.daily_revenue);
                updateCarrierPerformanceTable(data.carrier_stats);
            })
            .catch(error => {
                console.error('Error loading reports data:', error);
                alert('Error loading reports data. Please try again.');
            });
    }

    function loadCustomerAnalytics() {
        fetch(`{{ route('admin.reports.customer-analytics') }}`)
            .then(response => response.json())
            .then(data => {
                updateTopCustomersTable(data.top_customers);
                updateCustomerTrendChart(data.customer_trend);
            })
            .catch(error => {
                console.error('Error loading customer analytics:', error);
            });
    }

    function updateParcelsTrendChart(data) {
        const ctx = document.getElementById('parcelsTrendChart').getContext('2d');
        
        if (parcelsTrendChart) {
            parcelsTrendChart.destroy();
        }
        
        const labels = Object.keys(data);
        const values = Object.values(data);
        
        parcelsTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Parcels Created',
                    data: values,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    function updateStatusDistributionChart(data) {
        const ctx = document.getElementById('statusDistributionChart').getContext('2d');
        
        if (statusDistributionChart) {
            statusDistributionChart.destroy();
        }
        
        const labels = Object.keys(data).map(status => status.replace('_', ' ').toUpperCase());
        const values = Object.values(data);
        
        statusDistributionChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: [
                        '#6c757d', '#17a2b8', '#007bff', '#ffc107', 
                        '#28a745', '#dc3545', '#6f42c1'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    function updateRevenueTrendChart(data) {
        const ctx = document.getElementById('revenueTrendChart').getContext('2d');
        
        if (revenueTrendChart) {
            revenueTrendChart.destroy();
        }
        
        const labels = Object.keys(data);
        const values = Object.values(data);
        
        revenueTrendChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Revenue ($)',
                    data: values,
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: '#28a745',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                }
            }
        });
    }

    function updateCustomerTrendChart(data) {
        const ctx = document.getElementById('customerTrendChart').getContext('2d');
        
        if (customerTrendChart) {
            customerTrendChart.destroy();
        }
        
        const labels = Object.keys(data);
        const values = Object.values(data);
        
        customerTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'New Customers',
                    data: values,
                    borderColor: '#764ba2',
                    backgroundColor: 'rgba(118, 75, 162, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }

    function updateCarrierPerformanceTable(data) {
        const tbody = document.querySelector('#carrierPerformanceTable tbody');
        tbody.innerHTML = '';
        
        data.forEach(carrier => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td><strong>${carrier.carrier_name}</strong></td>
                <td>${carrier.total_parcels}</td>
                <td>
                    <span class="badge bg-${carrier.delivery_rate >= 90 ? 'success' : carrier.delivery_rate >= 70 ? 'warning' : 'danger'}">
                        ${carrier.delivery_rate}%
                    </span>
                </td>
                <td>${formatCurrency(carrier.avg_cost)}</td>
            `;
        });
    }

    function updateTopCustomersTable(data) {
        const tbody = document.querySelector('#topCustomersTable tbody');
        tbody.innerHTML = '';
        
        data.forEach(customer => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td><strong>${customer.name}</strong></td>
                <td>${customer.email}</td>
                <td><span class="badge bg-primary">${customer.parcels_count}</span></td>
                <td><strong>${formatCurrency(customer.total_spent)}</strong></td>
            `;
        });
    }

    function refreshReports() {
        loadReportsData();
        loadCustomerAnalytics();
    }
</script>
@endpush
