<?php

namespace App\Services;

use App\Models\BlogPost;
use App\Models\Category;
use App\Models\Product;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;

class SeoService
{
    /**
     * Generate structured data for the current page
     */
    public function generateStructuredData(string $type, array $data = []): array
    {
        $baseData = [
            '@context' => 'https://schema.org',
        ];

        switch ($type) {
            case 'organization':
                return array_merge($baseData, [
                    '@type' => 'Organization',
                    'name' => $data['name'] ?? config('app.name'),
                    'url' => $data['url'] ?? url('/'),
                    'logo' => $data['logo'] ?? asset('images/logo.png'),
                    'description' => $data['description'] ?? SiteSetting::getValue('site_description'),
                    'address' => $data['address'] ?? [],
                    'contactPoint' => $data['contactPoint'] ?? [],
                    'sameAs' => $data['socialMedia'] ?? [],
                ]);

            case 'website':
                return array_merge($baseData, [
                    '@type' => 'WebSite',
                    'name' => $data['name'] ?? config('app.name'),
                    'url' => $data['url'] ?? url('/'),
                    'description' => $data['description'] ?? SiteSetting::getValue('site_description'),
                    'potentialAction' => [
                        '@type' => 'SearchAction',
                        'target' => [
                            '@type' => 'EntryPoint',
                            'urlTemplate' => url('/search?q={search_term_string}'),
                        ],
                        'query-input' => 'required name=search_term_string',
                    ],
                ]);

            case 'breadcrumb':
                return array_merge($baseData, [
                    '@type' => 'BreadcrumbList',
                    'itemListElement' => $this->generateBreadcrumbItems($data['breadcrumbs'] ?? []),
                ]);

            case 'product':
                return array_merge($baseData, [
                    '@type' => 'Product',
                    'name' => $data['name'],
                    'description' => $data['description'],
                    'image' => $data['image'] ?? [],
                    'brand' => [
                        '@type' => 'Brand',
                        'name' => $data['brand'] ?? config('app.name'),
                    ],
                    'offers' => [
                        '@type' => 'Offer',
                        'price' => $data['price'] ?? '0',
                        'priceCurrency' => $data['currency'] ?? 'USD',
                        'availability' => $data['availability'] ?? 'https://schema.org/InStock',
                        'seller' => [
                            '@type' => 'Organization',
                            'name' => config('app.name'),
                        ],
                    ],
                ]);

            case 'service':
                return array_merge($baseData, [
                    '@type' => 'Service',
                    'name' => $data['name'],
                    'description' => $data['description'],
                    'provider' => [
                        '@type' => 'Organization',
                        'name' => config('app.name'),
                    ],
                    'areaServed' => $data['areaServed'] ?? 'Worldwide',
                    'serviceType' => $data['serviceType'] ?? 'Logistics',
                ]);

            default:
                return $baseData;
        }
    }

    /**
     * Generate breadcrumb items for structured data
     */
    private function generateBreadcrumbItems(array $breadcrumbs): array
    {
        $items = [];
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $items[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $breadcrumb['title'],
                'item' => $breadcrumb['url'] ?? null,
            ];
        }

        return $items;
    }

    /**
     * Generate Open Graph meta tags
     */
    public function generateOpenGraphTags(array $data): array
    {
        $tags = [
            'og:type' => $data['type'] ?? 'website',
            'og:title' => $data['title'] ?? config('app.name'),
            'og:description' => $data['description'] ?? SiteSetting::getValue('site_description'),
            'og:url' => $data['url'] ?? Request::url(),
            'og:site_name' => config('app.name'),
            'og:locale' => $data['locale'] ?? 'en_US',
        ];

        if (!empty($data['image'])) {
            $tags['og:image'] = $data['image'];
            $tags['og:image:alt'] = $data['image_alt'] ?? $data['title'];
        }

        if (!empty($data['video'])) {
            $tags['og:video'] = $data['video'];
        }

        return $tags;
    }

    /**
     * Generate Twitter Card meta tags
     */
    public function generateTwitterCardTags(array $data): array
    {
        $tags = [
            'twitter:card' => $data['card_type'] ?? 'summary_large_image',
            'twitter:title' => $data['title'] ?? config('app.name'),
            'twitter:description' => $data['description'] ?? SiteSetting::getValue('site_description'),
        ];

        if (!empty($data['image'])) {
            $tags['twitter:image'] = $data['image'];
            $tags['twitter:image:alt'] = $data['image_alt'] ?? $data['title'];
        }

        $twitterHandle = SiteSetting::getValue('twitter_handle');
        if ($twitterHandle) {
            $tags['twitter:site'] = '@' . ltrim($twitterHandle, '@');
            $tags['twitter:creator'] = '@' . ltrim($twitterHandle, '@');
        }

        return $tags;
    }

    /**
     * Generate canonical URL
     */
    public function generateCanonicalUrl(string $url = null): string
    {
        $url = $url ?: Request::url();
        
        // Remove query parameters for canonical URL
        $parsedUrl = parse_url($url);
        $canonical = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        
        if (isset($parsedUrl['port']) && $parsedUrl['port'] != 80 && $parsedUrl['port'] != 443) {
            $canonical .= ':' . $parsedUrl['port'];
        }
        
        $canonical .= $parsedUrl['path'] ?? '/';
        
        return $canonical;
    }

    /**
     * Generate meta robots tag
     */
    public function generateRobotsTag(array $options = []): string
    {
        $robots = [];
        
        $robots[] = $options['index'] ?? true ? 'index' : 'noindex';
        $robots[] = $options['follow'] ?? true ? 'follow' : 'nofollow';
        
        if ($options['noarchive'] ?? false) {
            $robots[] = 'noarchive';
        }
        
        if ($options['nosnippet'] ?? false) {
            $robots[] = 'nosnippet';
        }
        
        if ($options['noimageindex'] ?? false) {
            $robots[] = 'noimageindex';
        }
        
        return implode(', ', $robots);
    }

    /**
     * Get SEO-optimized title
     */
    public function optimizeTitle(string $title, string $siteName = null): string
    {
        $siteName = $siteName ?: config('app.name');
        
        // If title already contains site name, return as is
        if (str_contains($title, $siteName)) {
            return $title;
        }
        
        // Add site name to title
        return $title . ' - ' . $siteName;
    }

    /**
     * Generate meta description with optimal length
     */
    public function optimizeMetaDescription(string $description): string
    {
        // Optimal length is 150-160 characters
        if (strlen($description) <= 160) {
            return $description;
        }
        
        // Truncate at word boundary
        $truncated = substr($description, 0, 157);
        $lastSpace = strrpos($truncated, ' ');
        
        if ($lastSpace !== false) {
            $truncated = substr($truncated, 0, $lastSpace);
        }
        
        return $truncated . '...';
    }

    /**
     * Generate sitemap data for blog posts
     */
    public function getBlogSitemapData(): array
    {
        return Cache::remember('blog_sitemap_data', 3600, function () {
            return BlogPost::published()
                ->select('slug', 'updated_at', 'published_at')
                ->orderBy('updated_at', 'desc')
                ->get()
                ->map(function ($post) {
                    return [
                        'url' => route('blog.show', $post->slug),
                        'lastmod' => $post->updated_at->toISOString(),
                        'changefreq' => 'monthly',
                        'priority' => '0.7',
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Generate sitemap data for products
     */
    public function getProductSitemapData(): array
    {
        return Cache::remember('product_sitemap_data', 3600, function () {
            return Product::where('is_active', true)
                ->select('slug', 'updated_at')
                ->orderBy('updated_at', 'desc')
                ->get()
                ->map(function ($product) {
                    return [
                        'url' => route('products.show', $product->slug),
                        'lastmod' => $product->updated_at->toISOString(),
                        'changefreq' => 'weekly',
                        'priority' => '0.8',
                    ];
                })
                ->toArray();
        });
    }
}
