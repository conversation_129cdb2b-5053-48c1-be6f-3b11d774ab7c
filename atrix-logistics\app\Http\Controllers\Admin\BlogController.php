<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BlogPost;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BlogController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin');
    }

    /**
     * Display a listing of blog posts
     */
    public function index(Request $request)
    {
        $query = BlogPost::with('author');

        // Search functionality
        if ($request->filled('search')) {
            $searchTerm = $request->get('search');
            $query->where(function ($q) use ($searchTerm) {
                $q->where('title', 'like', "%{$searchTerm}%")
                  ->orWhere('content', 'like', "%{$searchTerm}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Author filter
        if ($request->filled('author')) {
            $query->where('author_id', $request->get('author'));
        }

        $posts = $query->orderBy('created_at', 'desc')->paginate(15);
        
        // Get authors for filter
        $authors = User::whereIn('role', ['admin', 'staff'])
            ->orderBy('name')
            ->get();

        return view('admin.blog.index', compact('posts', 'authors'));
    }

    /**
     * Show the form for creating a new blog post
     */
    public function create()
    {
        $authors = User::whereIn('role', ['admin', 'staff'])
            ->orderBy('name')
            ->get();

        return view('admin.blog.create', compact('authors'));
    }

    /**
     * Store a newly created blog post
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:blog_posts,slug',
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'author_id' => 'required|exists:users,id',
            'status' => 'required|in:draft,published,archived',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $request->file('featured_image')
                ->store('blog/featured-images', 'public');
        }

        // Process tags
        if (!empty($validated['tags'])) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        }

        // Set published_at if status is published and no date is set
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $post = BlogPost::create($validated);

        return redirect()
            ->route('admin.blog.show', $post)
            ->with('success', 'Blog post created successfully!');
    }

    /**
     * Display the specified blog post
     */
    public function show(BlogPost $blogPost)
    {
        $blogPost->load('author');
        return view('admin.blog.show', compact('blogPost'));
    }

    /**
     * Show the form for editing the specified blog post
     */
    public function edit(BlogPost $blogPost)
    {
        $authors = User::whereIn('role', ['admin', 'staff'])
            ->orderBy('name')
            ->get();

        return view('admin.blog.edit', compact('blogPost', 'authors'));
    }

    /**
     * Update the specified blog post
     */
    public function update(Request $request, BlogPost $blogPost)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('blog_posts')->ignore($blogPost->id)],
            'excerpt' => 'nullable|string|max:500',
            'content' => 'required|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'author_id' => 'required|exists:users,id',
            'status' => 'required|in:draft,published,archived',
            'published_at' => 'nullable|date',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'meta_keywords' => 'nullable|string|max:500',
            'tags' => 'nullable|string',
            'is_featured' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title']);
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            // Delete old image
            if ($blogPost->featured_image) {
                Storage::disk('public')->delete($blogPost->featured_image);
            }
            
            $validated['featured_image'] = $request->file('featured_image')
                ->store('blog/featured-images', 'public');
        }

        // Process tags
        if (!empty($validated['tags'])) {
            $validated['tags'] = array_map('trim', explode(',', $validated['tags']));
        } else {
            $validated['tags'] = null;
        }

        // Set published_at if status is published and no date is set
        if ($validated['status'] === 'published' && empty($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $blogPost->update($validated);

        return redirect()
            ->route('admin.blog.show', $blogPost)
            ->with('success', 'Blog post updated successfully!');
    }

    /**
     * Remove the specified blog post
     */
    public function destroy(BlogPost $blogPost)
    {
        // Delete featured image
        if ($blogPost->featured_image) {
            Storage::disk('public')->delete($blogPost->featured_image);
        }

        $blogPost->delete();

        return redirect()
            ->route('admin.blog.index')
            ->with('success', 'Blog post deleted successfully!');
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request)
    {
        $request->validate([
            'action' => 'required|in:delete,publish,draft,archive',
            'selected_posts' => 'required|array|min:1',
            'selected_posts.*' => 'exists:blog_posts,id',
        ]);

        $posts = BlogPost::whereIn('id', $request->selected_posts);

        switch ($request->action) {
            case 'delete':
                $posts->delete();
                $message = 'Selected posts deleted successfully!';
                break;
            case 'publish':
                $posts->update(['status' => 'published', 'published_at' => now()]);
                $message = 'Selected posts published successfully!';
                break;
            case 'draft':
                $posts->update(['status' => 'draft']);
                $message = 'Selected posts moved to draft successfully!';
                break;
            case 'archive':
                $posts->update(['status' => 'archived']);
                $message = 'Selected posts archived successfully!';
                break;
        }

        return redirect()
            ->route('admin.blog.index')
            ->with('success', $message);
    }
}
