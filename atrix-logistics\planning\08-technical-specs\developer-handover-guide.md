# 👨‍💻 Developer Handover Guide - Atrix Logistics

## 🎯 Purpose
This document ensures any developer can seamlessly continue development from any point in the project.

## 🏗️ Project Architecture Overview

### Technology Stack
```
Frontend: Blade Templates + Alpine.js + Bootstrap + Atrix CSS
Backend: Laravel 10.48.29 + MySQL + Laravel Filament
Assets: Vite + NPM
Admin: Laravel Filament
Authentication: <PERSON><PERSON> Breeze
```

### Directory Structure
```
laravel-app/
├── app/
│   ├── Models/           # Eloquent models
│   ├── Http/Controllers/ # Request handling
│   ├── Services/         # Business logic
│   ├── Repositories/     # Data access layer
│   ├── Filament/         # Admin panel resources
│   └── Policies/         # Authorization
├── database/
│   ├── migrations/       # Database schema
│   ├── seeders/          # Sample data
│   └── factories/        # Test data generation
├── resources/
│   ├── views/            # Blade templates
│   ├── css/              # Custom styles
│   └── js/               # Custom JavaScript
└── public/assets/        # Atrix template assets
```

## 🔧 Development Environment Setup

### Prerequisites
```bash
# Required software
PHP 8.1+
Composer 2.0+
Node.js 18+
MySQL 8.0+
Git
```

### Quick Start
```bash
# 1. Clone repository
git clone [repository-url]
cd atrix-logistics

# 2. Install dependencies
composer install
npm install

# 3. Environment setup
cp .env.example .env
php artisan key:generate

# 4. Database setup (MySQL database: celtickurier)
php artisan migrate
php artisan db:seed

# 5. Build assets
npm run dev

# 6. Start development server
php artisan serve
```

## 📋 Development Workflow

### Git Branching Strategy
```
main/master     # Production-ready code
develop         # Integration branch
feature/*       # New features
bugfix/*        # Bug fixes
hotfix/*        # Emergency fixes
```

### Commit Message Format
```
type(scope): description

Types: feat, fix, docs, style, refactor, test, chore
Scope: auth, tracking, products, admin, etc.

Example: feat(tracking): add real-time parcel status updates
```

### Code Review Checklist
- [ ] Code follows Laravel conventions
- [ ] Tests written and passing
- [ ] Documentation updated
- [ ] No security vulnerabilities
- [ ] Performance considerations addressed

## 🗄️ Database Management

### Migration Naming Convention
```php
// Format: YYYY_MM_DD_HHMMSS_action_table_name.php
2024_01_15_100000_create_parcels_table.php
2024_01_15_110000_add_tracking_to_parcels_table.php
```

### Model Relationships
```php
// Key relationships to understand
User -> hasMany(Parcels)
Parcel -> belongsTo(Carrier)
Parcel -> hasMany(TrackingEvents)
Product -> belongsToMany(Categories)
Order -> hasMany(OrderItems)
QuoteRequest -> hasMany(QuoteRequestItems)
```

### Seeder Dependencies
```php
// Run in this order
1. CarrierSeeder
2. UserSeeder
3. CategorySeeder
4. ProductSeeder
5. SiteSettingsSeeder
6. TeamMemberSeeder
7. BranchSeeder
```

## 🎨 Frontend Development

### Blade Component Structure
```php
// Component naming convention
<x-layout.header />
<x-forms.quote-modal />
<x-tracking.status-badge />
<x-products.card />
```

### CSS Organization
```scss
// File structure
resources/css/
├── app.css           # Main application styles
├── components/       # Component-specific styles
├── pages/           # Page-specific styles
└── utilities/       # Utility classes
```

### JavaScript Guidelines
```javascript
// Use Alpine.js for interactivity
// jQuery only for Atrix template compatibility
// Modular approach for custom functionality
```

## 🔐 Authentication & Authorization

### User Roles
```php
'admin'    # Full system access
'staff'    # Limited admin access
'customer' # Customer portal access
```

### Permission Structure
```php
// Permissions are role-based
Admin: all permissions
Staff: parcels.*, products.*, orders.*
Customer: own data only
```

## 📦 Key Features Implementation

### Parcel Tracking System
```php
// Core files
app/Models/Parcel.php
app/Services/ParcelService.php
app/Http/Controllers/TrackingController.php
resources/views/tracking/
```

### Quote Modal System
```php
// Core files
app/Models/QuoteRequest.php
app/Http/Controllers/QuoteController.php
resources/views/components/quote-modal.blade.php
```

### CMS System
```php
// Core files
app/Models/SiteSetting.php
app/Filament/Resources/SiteSettingResource.php
app/Services/CmsService.php
```

## 🧪 Testing Strategy

### Test Structure
```php
tests/
├── Unit/           # Model and service tests
├── Feature/        # HTTP and integration tests
└── Browser/        # Laravel Dusk tests
```

### Running Tests
```bash
# Unit tests
php artisan test --testsuite=Unit

# Feature tests
php artisan test --testsuite=Feature

# All tests
php artisan test

# With coverage
php artisan test --coverage
```

## 🚀 Deployment Guide

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations run
- [ ] Assets compiled and optimized
- [ ] Cache cleared and optimized
- [ ] SSL certificate installed
- [ ] Backup procedures in place

### Deployment Commands
```bash
# Production deployment
composer install --optimize-autoloader --no-dev
php artisan config:cache
php artisan route:cache
php artisan view:cache
npm run build
```

## 📊 Monitoring & Maintenance

### Log Files
```
storage/logs/laravel.log    # Application logs
storage/logs/query.log      # Database queries
storage/logs/mail.log       # Email logs
```

### Performance Monitoring
```php
// Key metrics to monitor
- Page load times
- Database query performance
- Memory usage
- Error rates
```

## 🆘 Troubleshooting

### Common Issues
```bash
# Permission issues
sudo chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Cache issues
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Asset issues
npm run build
php artisan storage:link
```

### Debug Mode
```php
// Enable in .env for development only
APP_DEBUG=true
APP_LOG_LEVEL=debug
```

## 📞 Support & Resources

### Documentation Links
- Laravel Documentation: https://laravel.com/docs
- Filament Documentation: https://filamentphp.com/docs
- Atrix Template Documentation: [template-docs-link]

### Key Contacts
- Project Manager: [contact-info]
- Lead Developer: [contact-info]
- DevOps Engineer: [contact-info]

### Emergency Procedures
1. Check application logs
2. Verify database connectivity
3. Check server resources
4. Contact system administrator
5. Escalate to development team

## 📋 Handover Checklist

### Code Handover
- [ ] All code committed and pushed
- [ ] Documentation updated
- [ ] Tests passing
- [ ] Environment documented
- [ ] Dependencies listed

### Knowledge Transfer
- [ ] Architecture overview provided
- [ ] Key features demonstrated
- [ ] Development workflow explained
- [ ] Deployment process documented
- [ ] Troubleshooting guide reviewed

### Access & Credentials
- [ ] Repository access granted
- [ ] Development environment access
- [ ] Database access provided
- [ ] Server access configured
- [ ] Third-party service credentials shared

This guide ensures any developer can pick up development seamlessly at any point in the project lifecycle.
