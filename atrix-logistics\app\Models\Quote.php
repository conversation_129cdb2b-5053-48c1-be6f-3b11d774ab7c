<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Quote extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'quote_number',
        'quote_type',
        'user_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'company_name',
        'service_type',
        'priority',
        'description',
        'requirements',
        'origin_address',
        'origin_city',
        'origin_state',
        'origin_postal_code',
        'origin_country',
        'destination_address',
        'destination_city',
        'destination_state',
        'destination_postal_code',
        'destination_country',
        'package_count',
        'total_weight',
        'weight_unit',
        'dimensions',
        'package_type',
        'package_description',
        'fragile',
        'hazardous',
        'declared_value',
        'preferred_pickup_date',
        'required_delivery_date',
        'delivery_speed',
        'status',
        'quoted_price',
        'pricing_breakdown',
        'discount_amount',
        'final_price',
        'currency',
        'assigned_to',
        'admin_notes',
        'customer_notes',
        'quoted_at',
        'expires_at',
        'accepted_at',
        'rejected_at',
        'insurance_required',
        'signature_required',
        'additional_services',
        'attachments',
        'metadata',
        'products',
        'products_total',
        'product_requirements',
        'quote_source',
    ];

    protected $casts = [
        'requirements' => 'array',
        'dimensions' => 'array',
        'fragile' => 'boolean',
        'hazardous' => 'boolean',
        'insurance_required' => 'boolean',
        'signature_required' => 'boolean',
        'additional_services' => 'array',
        'attachments' => 'array',
        'metadata' => 'array',
        'products' => 'array',
        'quoted_at' => 'datetime',
        'expires_at' => 'datetime',
        'accepted_at' => 'datetime',
        'rejected_at' => 'datetime',
        'preferred_pickup_date' => 'date',
        'required_delivery_date' => 'date',
        'total_weight' => 'decimal:2',
        'declared_value' => 'decimal:2',
        'quoted_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'final_price' => 'decimal:2',
        'products_total' => 'decimal:2',
    ];

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get products from the products array with full product details
     */
    public function getProductsWithDetails()
    {
        if (!$this->products) {
            return collect();
        }

        $productIds = array_column($this->products, 'product_id');
        $products = \App\Models\Product::whereIn('id', $productIds)->get()->keyBy('id');

        return collect($this->products)->map(function ($item) use ($products) {
            $product = $products->get($item['product_id']);
            if ($product) {
                return [
                    'product' => $product,
                    'quantity' => $item['quantity'],
                    'price_at_time' => $item['price_at_time'] ?? $product->price,
                    'total' => ($item['price_at_time'] ?? $product->price) * $item['quantity'],
                    'notes' => $item['notes'] ?? null,
                ];
            }
            return null;
        })->filter();
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeReviewing($query)
    {
        return $query->where('status', 'reviewing');
    }

    public function scopeQuoted($query)
    {
        return $query->where('status', 'quoted');
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', 'accepted');
    }

    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
                    ->orWhere(function($q) {
                        $q->where('expires_at', '<', now())
                          ->whereIn('status', ['quoted']);
                    });
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['pending', 'reviewing', 'quoted']);
    }

    /**
     * Accessors & Mutators
     */
    public function getFormattedStatusAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->status));
    }

    public function getFormattedServiceTypeAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->service_type));
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'reviewing' => 'info',
            'quoted' => 'primary',
            'accepted' => 'success',
            'rejected' => 'danger',
            'expired' => 'secondary',
            'converted' => 'success',
            default => 'secondary'
        };
    }

    public function getPriorityBadgeColorAttribute(): string
    {
        return match($this->priority) {
            'standard' => 'secondary',
            'urgent' => 'warning',
            'express' => 'danger',
            default => 'secondary'
        };
    }

    public function getFormattedPriorityAttribute(): string
    {
        return ucwords($this->priority);
    }

    public function getTotalVolumeAttribute(): ?float
    {
        if (!$this->dimensions) {
            return null;
        }

        $length = $this->dimensions['length'] ?? 0;
        $width = $this->dimensions['width'] ?? 0;
        $height = $this->dimensions['height'] ?? 0;

        return $length * $width * $height;
    }

    /**
     * Helper Methods
     */
    public function isShippingQuote(): bool
    {
        return $this->quote_type === 'shipping';
    }

    public function isProductQuote(): bool
    {
        return $this->quote_type === 'product';
    }

    public function getProductsCount(): int
    {
        if (!$this->products) {
            return 0;
        }

        return array_sum(array_column($this->products, 'quantity'));
    }

    public function getProductsValue(): float
    {
        return $this->products_total ?? 0;
    }

    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isReviewing(): bool
    {
        return $this->status === 'reviewing';
    }

    public function isQuoted(): bool
    {
        return $this->status === 'quoted';
    }

    public function isAccepted(): bool
    {
        return $this->status === 'accepted';
    }

    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->expires_at && $this->expires_at->isPast() && $this->status === 'quoted');
    }

    public function canBeQuoted(): bool
    {
        return in_array($this->status, ['pending', 'reviewing']);
    }

    public function canBeAccepted(): bool
    {
        return $this->status === 'quoted' && !$this->isExpired();
    }

    public function canBeRejected(): bool
    {
        return in_array($this->status, ['quoted', 'pending', 'reviewing']);
    }

    public function canBeEdited(): bool
    {
        return in_array($this->status, ['pending', 'reviewing']);
    }

    public function getDaysUntilExpiry(): ?int
    {
        if (!$this->expires_at) {
            return null;
        }

        return now()->diffInDays($this->expires_at, false);
    }

    /**
     * Static Methods
     */
    public static function generateQuoteNumber(): string
    {
        $prefix = 'QTE';
        $timestamp = now()->format('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        $quoteNumber = $prefix . $timestamp . $random;

        // Ensure uniqueness
        $counter = 1;
        $originalQuoteNumber = $quoteNumber;
        while (static::where('quote_number', $quoteNumber)->exists()) {
            $quoteNumber = $originalQuoteNumber . $counter;
            $counter++;
        }

        return $quoteNumber;
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quote) {
            if (empty($quote->quote_number)) {
                $quote->quote_number = static::generateQuoteNumber();
            }
        });
    }
}
