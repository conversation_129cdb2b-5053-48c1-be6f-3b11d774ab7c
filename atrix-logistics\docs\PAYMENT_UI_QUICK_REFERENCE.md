# Payment UI Quick Reference Guide

## 🚀 Quick Start

When implementing a new payment flow, follow these steps:

### 1. Copy Reference Template
Use `resources/views/customer/payments/show.blade.php` as your starting point.

### 2. Update Controller
```php
public function showPayment($item): View|RedirectResponse
{
    $paymentMethods = $this->getAvailablePaymentMethods();
    $currency = [
        'code' => SiteSetting::getValue('base_currency', 'USD'),
        'symbol' => SiteSetting::getValue('currency_symbol', '$'),
    ];
    return view('your.payment.view', compact('item', 'paymentMethods', 'currency'));
}
```

### 3. Required Blade Sections
```blade
@extends('layouts.customer')
@section('title', 'Payment - Your Item')
@section('page-title', 'Secure Payment')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Security Notice -->
        <!-- Item Summary -->
        <!-- Payment Methods -->
        <!-- Trust Indicators -->
    </div>
</div>
@endsection

@push('styles')
<!-- Copy from reference template -->
@endpush

@push('scripts')
<!-- Copy from reference template -->
@endpush
```

## 📋 Essential Components Checklist

- [ ] **Security Notice** - Blue alert with shield icon at top
- [ ] **Item Summary** - Card with receipt icon showing what customer is paying for
- [ ] **Payment Method Cards** - 2-column grid with radio button cards
- [ ] **Payment Details** - Dynamic content that shows based on selected method
- [ ] **Security Features** - 3-column grid with SSL, PCI, Fraud protection badges
- [ ] **Submit Button** - Dynamic button that changes text and shows amount
- [ ] **Trust Indicators** - Small text at bottom about security
- [ ] **Copy-to-Clipboard** - For bank account details
- [ ] **Loading States** - Spinner during payment processing

## 🎨 Required CSS Classes

```css
.payment-method-card { height: 100%; }
.payment-method-card .btn { min-height: 150px; transition: all 0.3s ease; }
.payment-method-card .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }
.payment-method-card input[type="radio"]:checked + label { background-color: var(--bs-primary); color: white; border-color: var(--bs-primary); }
.payment-info { animation: fadeIn 0.3s ease-in-out; }
@keyframes fadeIn { from { opacity: 0; transform: translateY(-10px); } to { opacity: 1; transform: translateY(0); } }
```

## ⚡ Required JavaScript Functions

### Payment Method Switching
```javascript
function showPaymentInfo() {
    paymentInfos.forEach(info => info.style.display = 'none');
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
    const selectedInfo = document.getElementById(selectedMethod + '-info');
    if (selectedInfo) selectedInfo.style.display = 'block';
    updateSubmitButton(selectedMethod);
}
```

### Copy to Clipboard
```javascript
function copyToClipboard(text, label = 'Text') {
    navigator.clipboard.writeText(text).then(() => {
        // Show success toast
    });
}
```

## 🔧 Payment Method Configuration

```php
private function getAvailablePaymentMethods(): array
{
    $methods = [];
    
    if (config('services.stripe.key')) {
        $methods['stripe'] = [
            'enabled' => true,
            'name' => 'Credit/Debit Card (Stripe)',
            'description' => 'Secure payment via Stripe',
            'icon' => 'fab fa-cc-stripe',  // ⚠️ MUST include full FontAwesome class!
            'color' => 'primary',
            'supports_cards' => true,
            'supports_3ds' => true,
            'processing_fee' => 0,
        ];
    }

    // Always include manual payment
    $methods['admin_approval'] = [
        'enabled' => true,
        'name' => 'Bank Transfer / Manual Payment',
        'description' => 'Pay via bank transfer or other offline methods',
        'icon' => 'fas fa-university',  // ⚠️ MUST include full FontAwesome class!
        'color' => 'secondary',
        'supports_cards' => false,
        'supports_3ds' => false,
        'processing_fee' => 0,
    ];
    
    return $methods;
}
```

## 🏗️ HTML Structure Template

```html
<!-- Security Notice -->
<div class="alert alert-info mb-4">
    <div class="d-flex align-items-center">
        <i class="fas fa-shield-alt fa-2x me-3"></i>
        <div>
            <h6 class="mb-1">🔒 Secure Payment Processing</h6>
            <p class="mb-0">Your payment information is processed securely...</p>
        </div>
    </div>
</div>

<!-- Item Summary -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>Order Summary</h5>
    </div>
    <div class="card-body">
        <!-- Summary content -->
    </div>
</div>

<!-- Payment Methods -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>Choose Payment Method</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ $actionUrl }}" id="paymentForm">
            @csrf
            
            @if(count($paymentMethods) > 0)
                <!-- Payment method cards -->
                <div class="row">
                    @foreach($paymentMethods as $method => $config)
                        @if($config['enabled'])
                        <div class="col-md-6 mb-3">
                            <div class="payment-method-card">
                                <input type="radio" class="btn-check" name="payment_method" id="method_{{ $method }}" value="{{ $method }}" {{ $loop->first ? 'checked' : '' }}>
                                <label class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" for="method_{{ $method }}">
                                    <i class="{{ $config['icon'] }} fa-2x mb-2"></i>
                                    <h6 class="mb-1">{{ $config['name'] }}</h6>
                                    <small class="text-muted text-center">{{ $config['description'] }}</small>
                                    @if($config['supports_3ds'])
                                        <span class="badge bg-success mt-2">
                                            <i class="fas fa-shield-alt me-1"></i>3D Secure
                                        </span>
                                    @endif
                                </label>
                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>
                
                <!-- Payment method details -->
                <div class="mt-4">
                    <!-- Stripe info -->
                    <div id="stripe-info" class="payment-info" style="display: none;">
                        <!-- Stripe details -->
                    </div>
                    
                    <!-- PayPal info -->
                    <div id="paypal-info" class="payment-info" style="display: none;">
                        <!-- PayPal details -->
                    </div>
                    
                    <!-- Manual payment info -->
                    <div id="admin_approval-info" class="payment-info" style="display: none;">
                        <!-- Bank transfer details -->
                    </div>
                </div>
                
                <!-- Security features -->
                <div class="row mt-4">
                    <div class="col-md-4 text-center">
                        <i class="fas fa-lock fa-2x text-success mb-2"></i>
                        <h6>SSL Encrypted</h6>
                        <small class="text-muted">256-bit SSL encryption</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                        <h6>PCI Compliant</h6>
                        <small class="text-muted">Industry standard security</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <i class="fas fa-user-shield fa-2x text-success mb-2"></i>
                        <h6>Fraud Protection</h6>
                        <small class="text-muted">Advanced fraud detection</small>
                    </div>
                </div>
                
                <!-- Submit button -->
                <div class="d-grid gap-2 mt-4">
                    <button type="submit" class="btn btn-primary btn-lg" id="submitPayment">
                        <i class="fas fa-credit-card me-2"></i>
                        Proceed to Secure Payment
                        <span class="ms-2">{{ $currency['symbol'] }}{{ number_format($amount, 2) }}</span>
                    </button>
                    <a href="{{ $backUrl }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Previous Page
                    </a>
                </div>
                
            @else
                <div class="alert alert-warning text-center">
                    <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                    <h5>No Payment Methods Available</h5>
                    <p class="mb-3">Payment processing is currently unavailable. Please contact our support team for assistance.</p>
                    <a href="{{ route('customer.support.create') }}" class="btn btn-warning">
                        <i class="fas fa-headset me-2"></i>
                        Contact Support
                    </a>
                </div>
            @endif
        </form>
    </div>
</div>

<!-- Trust indicators -->
<div class="row mt-4 text-center">
    <div class="col-12">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            Your payment information is processed securely. We use industry-standard encryption and never store your card details.
        </small>
    </div>
</div>
```

## 🔍 Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| Payment methods not showing | Check `getAvailablePaymentMethods()` configuration |
| **Icons showing as "X" or broken** | **CRITICAL: Use full FontAwesome class in config: `'icon' => 'fab fa-cc-stripe'` not `'icon' => 'cc-stripe'`** |
| JavaScript errors | Check browser console, ensure all scripts are loaded |
| Styling issues | Verify CSS is loaded and Bootstrap classes are available |
| Copy button not working | Check `copyToClipboard()` function and clipboard permissions |
| Manual payment details not showing | Ensure admin has configured bank details in site settings |

## 📱 Mobile Responsiveness

- Use `col-md-6` for payment method cards (stacks on mobile)
- Ensure buttons are large enough for touch (min 44px)
- Test on various screen sizes
- Verify text is readable without zooming

## 🔒 Security Best Practices

- Never store card details client-side
- Always include CSRF tokens
- Validate all inputs server-side
- Use HTTPS for all payment pages
- Don't expose sensitive data in error messages
- Implement proper session management

## 📞 Need Help?

1. Check the full documentation: `docs/PAYMENT_UI_STANDARDS.md`
2. Review reference implementation: `resources/views/customer/payments/show.blade.php`
3. Test with existing working examples: parcel payments, order payments
4. Contact the development team for complex integrations

Remember: **Consistency is key!** Always use the standardized UI to ensure a professional customer experience.
