<?php

namespace App\Http\Controllers;

use App\Models\LiveChatSession;
use App\Models\LiveChatMessage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class LiveChatController extends Controller
{
    /**
     * Start a new chat session
     */
    public function startSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'visitor_name' => 'nullable|string|max:255',
            'visitor_email' => 'nullable|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $sessionId = LiveChatSession::generateSessionId();

        $session = LiveChatSession::create([
            'session_id' => $sessionId,
            'visitor_name' => $request->visitor_name,
            'visitor_email' => $request->visitor_email,
            'visitor_ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'status' => 'waiting',
            'last_activity' => now(),
        ]);

        return response()->json([
            'success' => true,
            'session_id' => $sessionId,
            'message' => 'Chat session started successfully'
        ]);
    }

    /**
     * Send a message from visitor
     */
    public function sendMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string|exists:live_chat_sessions,session_id',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $session = LiveChatSession::where('session_id', $request->session_id)->first();
        
        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        // Create the message
        $message = LiveChatMessage::create([
            'session_id' => $session->id,
            'sender_type' => 'visitor',
            'message' => $request->message,
        ]);

        // Update session activity and status
        $session->updateActivity();
        if ($session->status === 'waiting') {
            $session->update(['status' => 'active']);
        }

        // TODO: Broadcast the message to staff in real-time
        // broadcast(new NewChatMessage($message));

        return response()->json([
            'success' => true,
            'message' => 'Message sent successfully',
            'data' => [
                'id' => $message->id,
                'message' => $message->message,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
            ]
        ]);
    }

    /**
     * Get messages for a session
     */
    public function getMessages(Request $request, string $sessionId): JsonResponse
    {
        $session = LiveChatSession::where('session_id', $sessionId)->first();
        
        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $messages = $session->messages()
            ->with('staff:id,name')
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender_type' => $message->sender_type,
                    'sender_name' => $message->sender_name,
                    'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                    'is_read' => $message->is_read,
                ];
            });

        // Update session activity
        $session->updateActivity();

        return response()->json([
            'success' => true,
            'messages' => $messages,
            'session_status' => $session->status,
        ]);
    }

    /**
     * Check for new messages (polling endpoint)
     */
    public function checkNewMessages(Request $request, string $sessionId): JsonResponse
    {
        $session = LiveChatSession::where('session_id', $sessionId)->first();
        
        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $lastMessageId = $request->query('last_message_id', 0);
        
        $newMessages = $session->messages()
            ->where('id', '>', $lastMessageId)
            ->where('sender_type', 'staff') // Only get staff replies
            ->with('staff:id,name')
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->id,
                    'message' => $message->message,
                    'sender_type' => $message->sender_type,
                    'sender_name' => $message->sender_name,
                    'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                ];
            });

        // Update session activity
        $session->updateActivity();

        return response()->json([
            'success' => true,
            'new_messages' => $newMessages,
            'session_status' => $session->status,
        ]);
    }

    /**
     * End a chat session
     */
    public function endSession(Request $request, string $sessionId): JsonResponse
    {
        $session = LiveChatSession::where('session_id', $sessionId)->first();
        
        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $session->update(['status' => 'closed']);

        return response()->json([
            'success' => true,
            'message' => 'Chat session ended successfully'
        ]);
    }
}
