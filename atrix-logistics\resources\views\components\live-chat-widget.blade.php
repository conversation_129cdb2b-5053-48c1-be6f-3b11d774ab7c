<!-- Live Chat Widget -->
<div id="liveChatWidget" class="fixed bottom-6 right-6 z-50 font-sans">
    <!-- Chat Toggle Button -->
    <button id="chatToggleBtn" class="relative w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center">
        <i class="fas fa-comments text-2xl"></i>
        <span id="chatBadge" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center hidden">0</span>
    </button>

    <!-- Chat Window -->
    <div id="chatWindow" class="absolute bottom-20 right-0 w-80 h-96 bg-white rounded-2xl shadow-2xl hidden flex-col overflow-hidden border border-gray-200">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-t-2xl">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-headset text-lg"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg">Live Support</h3>
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                            <p class="text-green-100 text-sm">Online now</p>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button id="minimizeChat" class="w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-minus text-sm"></i>
                    </button>
                    <button id="closeChat" class="w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Welcome Screen -->
            <div id="welcomeScreen" class="flex-1 flex items-center justify-center p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-comments text-green-600 text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold text-gray-800 mb-2">Start a conversation</h4>
                    <p class="text-gray-600 mb-6">Get instant help from our support team</p>

                    <form id="startChatForm" class="space-y-4">
                        <input type="text" id="visitorName" placeholder="Your name (optional)"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <input type="email" id="visitorEmail" placeholder="Your email (optional)"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                        <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center">
                            <i class="fas fa-play mr-2"></i> Start Chat
                        </button>
                    </form>
                </div>
            </div>

            <!-- Chat Messages -->
            <div id="chatMessages" class="flex-1 p-4 overflow-y-auto bg-gray-50 hidden">
                <!-- Messages will be loaded here -->
            </div>
        </div>

        <!-- Chat Input -->
        <div id="chatInput" class="p-4 border-t border-gray-200 bg-white rounded-b-2xl hidden">
            <form id="messageForm" class="flex space-x-2">
                <input type="text" id="messageInput" placeholder="Type your message..." required
                       class="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                <button type="submit" class="w-10 h-10 bg-green-600 hover:bg-green-700 text-white rounded-full flex items-center justify-center transition-colors">
                    <i class="fas fa-paper-plane text-sm"></i>
                </button>
            </form>
        </div>
    </div>
</div>

<style>
/* Custom styles for chat messages and animations */
#chatMessages {
    scroll-behavior: smooth;
}

#chatMessages::-webkit-scrollbar {
    width: 6px;
}

#chatMessages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#chatMessages::-webkit-scrollbar-thumb {
    background: #16a34a;
    border-radius: 3px;
}

#chatMessages::-webkit-scrollbar-thumb:hover {
    background: #15803d;
}

.message-item {
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
}

.message-item.visitor {
    align-items: flex-end;
}

.message-item.staff {
    align-items: flex-start;
}

.message-bubble {
    max-width: 80%;
    padding: 8px 12px;
    border-radius: 16px;
    word-wrap: break-word;
    word-break: break-word;
    display: inline-block;
    font-size: 14px;
    line-height: 1.4;
}

.message-visitor {
    background: #16a34a;
    color: white;
    border-bottom-right-radius: 4px;
}

.message-staff {
    background: white;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-bottom-left-radius: 4px;
}

.message-time {
    font-size: 10px;
    color: #6b7280;
    margin-top: 4px;
    opacity: 0.8;
}

.message-visitor .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.typing-indicator {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: #6b7280;
    font-style: italic;
    font-size: 14px;
}

.typing-dots {
    display: inline-flex;
    margin-left: 8px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #6b7280;
    margin: 0 1px;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    #liveChatWidget {
        bottom: 1rem;
        right: 1rem;
    }

    #chatWindow {
        width: 18rem;
        height: 22rem;
    }

    #chatToggleBtn {
        width: 3.5rem;
        height: 3.5rem;
    }

    #chatToggleBtn i {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    #chatWindow {
        width: calc(100vw - 2rem);
        right: -1rem;
        height: 20rem;
    }

    .message-bubble {
        max-width: 85%;
        padding: 6px 10px;
        font-size: 13px;
    }

    #chatMessages {
        padding: 0.75rem;
    }
}
</style>

<script>
class LiveChatWidget {
    constructor() {
        this.sessionId = null;
        this.isOpen = false;
        this.lastMessageId = 0;
        this.pollingInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSessionFromStorage();
    }

    bindEvents() {
        document.getElementById('chatToggleBtn').addEventListener('click', () => this.toggleChat());
        document.getElementById('minimizeChat').addEventListener('click', () => this.minimizeChat());
        document.getElementById('closeChat').addEventListener('click', () => this.closeChat());
        document.getElementById('startChatForm').addEventListener('submit', (e) => this.startChat(e));
        document.getElementById('messageForm').addEventListener('submit', (e) => this.sendMessage(e));
    }

    toggleChat() {
        const chatWindow = document.getElementById('chatWindow');
        if (this.isOpen) {
            this.minimizeChat();
        } else {
            chatWindow.classList.remove('hidden');
            chatWindow.classList.add('flex');
            this.isOpen = true;
            if (this.sessionId) {
                this.showChatInterface();
                this.loadMessages();
                this.startPolling();
                this.focusMessageInput();
            }
        }
    }

    focusMessageInput() {
        setTimeout(() => {
            const messageInput = document.getElementById('messageInput');
            if (messageInput && !messageInput.closest('.hidden')) {
                messageInput.focus();
            }
        }, 300);
    }

    minimizeChat() {
        const chatWindow = document.getElementById('chatWindow');
        chatWindow.classList.add('hidden');
        chatWindow.classList.remove('flex');
        this.isOpen = false;
        this.stopPolling();
    }

    closeChat() {
        if (this.sessionId) {
            this.endSession();
        }
        this.minimizeChat();
        this.resetChat();
    }

    async startChat(e) {
        e.preventDefault();
        
        const name = document.getElementById('visitorName').value;
        const email = document.getElementById('visitorEmail').value;

        try {
            const response = await fetch('/api/live-chat/start-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    visitor_name: name,
                    visitor_email: email
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.sessionId = data.session_id;
                localStorage.setItem('liveChatSessionId', this.sessionId);
                this.showChatInterface();
                this.addWelcomeMessage();
                this.startPolling();
            } else {
                alert('Failed to start chat session. Please try again.');
            }
        } catch (error) {
            console.error('Error starting chat:', error);
            alert('Failed to start chat session. Please try again.');
        }
    }

    showChatInterface() {
        document.getElementById('welcomeScreen').classList.add('hidden');
        document.getElementById('chatMessages').classList.remove('hidden');
        document.getElementById('chatInput').classList.remove('hidden');
        this.focusMessageInput();
    }

    addWelcomeMessage() {
        const welcomeMessage = {
            message: "Hello! 👋 Welcome to Atrix Logistics support. I'm here to help you with any questions about our shipping services, tracking, or logistics solutions. How can I assist you today?",
            created_at: new Date().toISOString(),
            sender_type: 'staff'
        };
        this.addMessageToChat(welcomeMessage, false);

        // Add quick action buttons
        setTimeout(() => {
            this.addQuickActions();
        }, 1000);
    }

    addQuickActions() {
        const messagesContainer = document.getElementById('chatMessages');
        const quickActionsDiv = document.createElement('div');
        quickActionsDiv.className = 'message-item staff mb-4';
        quickActionsDiv.innerHTML = `
            <div class="flex flex-wrap gap-2 max-w-xs">
                <button onclick="liveChatWidget.sendQuickMessage('I need help with tracking')"
                        class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors">
                    📦 Track Package
                </button>
                <button onclick="liveChatWidget.sendQuickMessage('I want to get a shipping quote')"
                        class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors">
                    💰 Get Quote
                </button>
                <button onclick="liveChatWidget.sendQuickMessage('I have a question about your services')"
                        class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-lg transition-colors">
                    ❓ General Question
                </button>
            </div>
        `;
        messagesContainer.appendChild(quickActionsDiv);
        this.scrollToBottom();
    }

    sendQuickMessage(message) {
        const messageInput = document.getElementById('messageInput');
        messageInput.value = message;
        document.getElementById('messageForm').dispatchEvent(new Event('submit'));
    }

    showTypingIndicator() {
        // Remove existing typing indicator
        this.hideTypingIndicator();

        const messagesContainer = document.getElementById('chatMessages');
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <i class="fas fa-headset text-gray-500 text-xs"></i>
                </div>
                <div class="flex items-center space-x-1">
                    <span class="text-gray-500 text-sm">Support is typing</span>
                    <div class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        `;
        messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    async sendMessage(e) {
        e.preventDefault();

        const messageInput = document.getElementById('messageInput');
        const submitBtn = e.target.querySelector('button[type="submit"]');
        const message = messageInput.value.trim();

        if (!message || !this.sessionId) return;

        // Disable input and show loading
        messageInput.disabled = true;
        submitBtn.disabled = true;
        const originalBtnContent = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        try {
            const response = await fetch('/api/live-chat/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    message: message
                })
            });

            const data = await response.json();

            if (data.success) {
                messageInput.value = '';
                this.addMessageToChat(data.data, true);
                this.lastMessageId = data.data.id;
                messageInput.focus();

                // Show typing indicator for staff response
                this.showTypingIndicator();

                // Remove typing indicator after a few seconds
                setTimeout(() => {
                    this.hideTypingIndicator();
                }, 3000);
            } else {
                console.error('Send message error:', data);
                this.showError('Failed to send message. Please try again.');
            }
        } catch (error) {
            console.error('Error sending message:', error);
            this.showError('Failed to send message. Please check your connection.');
        } finally {
            // Re-enable input
            messageInput.disabled = false;
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnContent;
        }
    }

    showError(message) {
        const messagesContainer = document.getElementById('chatMessages');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message-item staff';
        errorDiv.innerHTML = `
            <div class="message-bubble bg-red-50 text-red-800 border border-red-200">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                ${message}
            </div>
        `;
        messagesContainer.appendChild(errorDiv);
        this.scrollToBottom();
    }

    addMessageToChat(message, isFromVisitor = false) {
        const messagesContainer = document.getElementById('chatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-item ${isFromVisitor ? 'visitor' : 'staff'}`;

        const messageText = typeof message === 'string' ? message : message.message;
        const messageTime = message.created_at ? new Date(message.created_at).toLocaleTimeString() : new Date().toLocaleTimeString();

        messageDiv.innerHTML = `
            <div class="message-bubble ${isFromVisitor ? 'message-visitor' : 'message-staff'}">
                ${this.escapeHtml(messageText)}
                <div class="message-time">${messageTime}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('chatMessages');
        setTimeout(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, 100);
    }

    async loadMessages() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`/api/live-chat/messages/${this.sessionId}`);
            const data = await response.json();
            
            if (data.success) {
                const messagesContainer = document.getElementById('chatMessages');
                messagesContainer.innerHTML = '';
                
                data.messages.forEach(message => {
                    this.addMessageToChat(message, message.sender_type === 'visitor');
                    this.lastMessageId = Math.max(this.lastMessageId, message.id);
                });
            }
        } catch (error) {
            console.error('Error loading messages:', error);
        }
    }

    async checkNewMessages() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`/api/live-chat/check-new-messages/${this.sessionId}?last_message_id=${this.lastMessageId}`);
            const data = await response.json();
            
            if (data.success && data.new_messages.length > 0) {
                // Hide typing indicator when new messages arrive
                this.hideTypingIndicator();

                data.new_messages.forEach(message => {
                    this.addMessageToChat(message, false);
                    this.lastMessageId = message.id;
                });

                // Update badge if chat is minimized
                if (!this.isOpen) {
                    this.updateBadge();
                }
            }
        } catch (error) {
            console.error('Error checking new messages:', error);
        }
    }

    updateBadge() {
        // This would need to track unread messages count
        // For now, just show a simple indicator
        const badge = document.getElementById('chatBadge');
        badge.classList.remove('hidden');
        badge.textContent = '!';
    }

    startPolling() {
        this.stopPolling();
        this.pollingInterval = setInterval(() => this.checkNewMessages(), 3000);
    }

    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }
    }

    async endSession() {
        if (!this.sessionId) return;

        try {
            await fetch(`/api/live-chat/end-session/${this.sessionId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            });
        } catch (error) {
            console.error('Error ending session:', error);
        }
    }

    resetChat() {
        this.sessionId = null;
        this.lastMessageId = 0;
        localStorage.removeItem('liveChatSessionId');
        document.getElementById('welcomeScreen').classList.remove('hidden');
        document.getElementById('chatMessages').classList.add('hidden');
        document.getElementById('chatInput').classList.add('hidden');
        document.getElementById('chatMessages').innerHTML = '';
        document.getElementById('chatBadge').classList.add('hidden');
    }

    loadSessionFromStorage() {
        const storedSessionId = localStorage.getItem('liveChatSessionId');
        if (storedSessionId) {
            this.sessionId = storedSessionId;
            // If we have a stored session, check if it's still valid
            this.validateSession();
        }
    }

    async validateSession() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`/api/live-chat/messages/${this.sessionId}`);
            const data = await response.json();

            if (!data.success) {
                // Session is invalid, clear it
                this.resetChat();
            }
        } catch (error) {
            console.error('Error validating session:', error);
            // On error, assume session is invalid
            this.resetChat();
        }
    }
}

// Initialize the chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.liveChatWidget = new LiveChatWidget();
});
</script>
