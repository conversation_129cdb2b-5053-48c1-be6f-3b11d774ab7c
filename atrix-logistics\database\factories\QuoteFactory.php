<?php

namespace Database\Factories;

use App\Models\Quote;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class QuoteFactory extends Factory
{
    protected $model = Quote::class;

    public function definition(): array
    {
        $customer = User::factory()->create(['role' => 'customer']);
        
        return [
            'quote_number' => 'QTE' . date('Ymd') . str_pad(fake()->numberBetween(1, 999), 3, '0', STR_PAD_LEFT),
            'quote_type' => 'shipping',
            'user_id' => $customer->id,
            'customer_name' => $customer->name,
            'customer_email' => $customer->email,
            'customer_phone' => $customer->phone,
            'company_name' => $customer->company_name,
            'service_type' => fake()->randomElement([
                'domestic_shipping',
                'international_shipping',
                'express_delivery',
                'freight_shipping',
                'warehousing',
                'custom_logistics'
            ]),
            'priority' => fake()->randomElement(['standard', 'urgent', 'express']),
            'description' => fake()->paragraph(),
            'origin_address' => fake()->streetAddress(),
            'origin_city' => fake()->city(),
            'origin_state' => fake()->state(),
            'origin_postal_code' => fake()->postcode(),
            'origin_country' => fake()->country(),
            'destination_address' => fake()->streetAddress(),
            'destination_city' => fake()->city(),
            'destination_state' => fake()->state(),
            'destination_postal_code' => fake()->postcode(),
            'destination_country' => fake()->country(),
            'package_count' => fake()->numberBetween(1, 10),
            'total_weight' => fake()->randomFloat(2, 0.1, 100),
            'weight_unit' => fake()->randomElement(['kg', 'lbs']),
            'package_type' => fake()->randomElement(['Box', 'Envelope', 'Pallet', 'Tube']),
            'package_description' => fake()->sentence(),
            'declared_value' => fake()->randomFloat(2, 10, 1000),
            'delivery_speed' => fake()->randomElement(['standard', 'express', 'overnight', 'same_day']),
            'fragile' => fake()->boolean(30),
            'hazardous' => fake()->boolean(10),
            'insurance_required' => fake()->boolean(40),
            'signature_required' => fake()->boolean(50),
            'status' => 'pending',
            'currency' => 'USD',
        ];
    }

    public function shipping(): static
    {
        return $this->state(fn (array $attributes) => [
            'quote_type' => 'shipping',
            'service_type' => fake()->randomElement([
                'domestic_shipping',
                'international_shipping',
                'express_delivery',
                'freight_shipping'
            ]),
        ]);
    }

    public function product(): static
    {
        return $this->state(fn (array $attributes) => [
            'quote_type' => 'product',
            'service_type' => 'product_inquiry',
            'products' => [
                [
                    'product_id' => 1,
                    'product_name' => 'Test Product',
                    'product_sku' => 'TEST-001',
                    'quantity' => 2,
                    'price_at_time' => 25.99,
                    'total' => 51.98,
                    'notes' => null
                ]
            ],
            'products_total' => 51.98,
            'product_requirements' => fake()->sentence(),
        ]);
    }

    public function quoted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'quoted',
            'quoted_price' => fake()->randomFloat(2, 50, 500),
            'final_price' => fake()->randomFloat(2, 50, 500),
            'quoted_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'expires_at' => fake()->dateTimeBetween('now', '+2 weeks'),
        ]);
    }

    public function accepted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'accepted',
            'quoted_price' => fake()->randomFloat(2, 50, 500),
            'final_price' => fake()->randomFloat(2, 50, 500),
            'quoted_at' => fake()->dateTimeBetween('-2 weeks', '-1 week'),
            'accepted_at' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'quoted_price' => fake()->randomFloat(2, 50, 500),
            'final_price' => fake()->randomFloat(2, 50, 500),
            'quoted_at' => fake()->dateTimeBetween('-2 weeks', '-1 week'),
            'rejected_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'customer_notes' => fake()->sentence(),
        ]);
    }
}
