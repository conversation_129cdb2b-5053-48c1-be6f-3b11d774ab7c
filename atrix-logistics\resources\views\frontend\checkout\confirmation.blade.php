@extends('layouts.frontend')

@section('title', 'Order Confirmation')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Header -->
        <div class="text-center mb-8">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-900">Order Confirmed!</h1>
            <p class="mt-2 text-lg text-gray-600">Thank you for your order. We'll send you a confirmation email shortly.</p>
        </div>

        <!-- Order Details -->
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Order Header -->
            <div class="bg-green-50 px-6 py-4 border-b border-green-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">Order #{{ $order->order_number }}</h2>
                        <p class="text-sm text-gray-600">Placed on {{ $order->created_at->format('F j, Y \a\t g:i A') }}</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            {{ $order->formatted_status }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="px-6 py-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div class="space-y-4">
                    @foreach($order->items as $item)
                    <div class="flex items-center space-x-4 py-4 border-b border-gray-200 last:border-b-0">
                        <div class="flex-shrink-0">
                            @if($item->product && $item->product->featured_image_url)
                                <img src="{{ $item->product->featured_image_url }}" 
                                     alt="{{ $item->product_name }}" 
                                     class="h-16 w-16 object-cover rounded-lg">
                            @else
                                <div class="h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-box text-gray-400"></i>
                                </div>
                            @endif
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900">{{ $item->product_name }}</h4>
                            @if($item->product_sku)
                                <p class="text-sm text-gray-500">SKU: {{ $item->product_sku }}</p>
                            @endif
                            <p class="text-sm text-gray-600">Quantity: {{ $item->quantity }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-gray-900">${{ number_format($item->total_price, 2) }}</p>
                            <p class="text-sm text-gray-500">${{ number_format($item->unit_price, 2) }} each</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Order Summary -->
            <div class="bg-gray-50 px-6 py-4">
                <div class="space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="text-gray-900">${{ number_format($order->subtotal, 2) }}</span>
                    </div>
                    @if($order->tax_amount > 0)
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Tax</span>
                        <span class="text-gray-900">${{ number_format($order->tax_amount, 2) }}</span>
                    </div>
                    @endif
                    @if($order->shipping_amount > 0)
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Shipping</span>
                        <span class="text-gray-900">${{ number_format($order->shipping_amount, 2) }}</span>
                    </div>
                    @endif
                    @if($order->discount_amount > 0)
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Discount</span>
                        <span class="text-red-600">-${{ number_format($order->discount_amount, 2) }}</span>
                    </div>
                    @endif
                    <div class="border-t border-gray-200 pt-2">
                        <div class="flex justify-between">
                            <span class="text-lg font-semibold text-gray-900">Total</span>
                            <span class="text-lg font-semibold text-green-600">${{ number_format($order->total_amount, 2) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping & Billing Info -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
            <!-- Shipping Address -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900">{{ $order->shipping_first_name }} {{ $order->shipping_last_name }}</p>
                    @if($order->shipping_company)
                        <p>{{ $order->shipping_company }}</p>
                    @endif
                    <p>{{ $order->shipping_address_1 }}</p>
                    @if($order->shipping_address_2)
                        <p>{{ $order->shipping_address_2 }}</p>
                    @endif
                    <p>{{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_postal_code }}</p>
                    <p>{{ $order->shipping_country }}</p>
                </div>
            </div>

            <!-- Billing Address -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Billing Address</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    <p class="font-medium text-gray-900">{{ $order->billing_first_name }} {{ $order->billing_last_name }}</p>
                    @if($order->billing_company)
                        <p>{{ $order->billing_company }}</p>
                    @endif
                    <p>{{ $order->billing_address_1 }}</p>
                    @if($order->billing_address_2)
                        <p>{{ $order->billing_address_2 }}</p>
                    @endif
                    <p>{{ $order->billing_city }}, {{ $order->billing_state }} {{ $order->billing_postal_code }}</p>
                    <p>{{ $order->billing_country }}</p>
                </div>
            </div>
        </div>

        <!-- Payment Info -->
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm text-gray-600">Payment Method</p>
                    <p class="font-medium text-gray-900">{{ $order->formatted_payment_method }}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm text-gray-600">Payment Status</p>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium 
                        @if($order->payment_status === 'paid') bg-green-100 text-green-800
                        @elseif($order->payment_status === 'pending') bg-yellow-100 text-yellow-800
                        @else bg-red-100 text-red-800 @endif">
                        {{ $order->formatted_payment_status }}
                    </span>
                </div>
            </div>
            
            @if($order->payment_method === 'manual' && $order->payment_status === 'pending')
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex">
                    <i class="fas fa-info-circle text-yellow-400 mt-0.5 mr-3"></i>
                    <div>
                        <h4 class="text-sm font-medium text-yellow-800">Payment Instructions</h4>
                        <p class="text-sm text-yellow-700 mt-1">
                            Payment instructions will be sent via email. Please check your inbox for details on how to complete your payment.
                        </p>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Order Notes -->
        @if($order->notes)
        <div class="bg-white rounded-lg shadow-lg p-6 mt-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Notes</h3>
            <p class="text-gray-600">{{ $order->notes }}</p>
        </div>
        @endif

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mt-8">
            <a href="{{ route('customer.orders') }}" 
               class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                View All Orders
            </a>
            <a href="{{ route('home') }}" 
               class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 px-6 rounded-lg font-semibold text-center transition-colors">
                Continue Shopping
            </a>
        </div>
    </div>
</div>
@endsection
