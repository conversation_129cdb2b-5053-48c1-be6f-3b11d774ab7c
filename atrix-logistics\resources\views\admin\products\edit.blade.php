@extends('layouts.admin')

@section('title', 'Edit Product')
@section('page-title', 'Edit Product')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.show', $product) }}" class="btn btn-outline-info">
            <i class="fas fa-eye me-1"></i> View Product
        </a>
        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="step-progress">
                        <div class="step-item active">
                            <div class="step-number">1</div>
                            <div class="step-label">Basic Info</div>
                        </div>
                        <div class="step-item active">
                            <div class="step-number">2</div>
                            <div class="step-label">Details</div>
                        </div>
                        <div class="step-item active">
                            <div class="step-number">3</div>
                            <div class="step-label">Pricing & Stock</div>
                        </div>
                        <div class="step-item active">
                            <div class="step-number">4</div>
                            <div class="step-label">Images & SEO</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form action="{{ route('admin.products.update', $product) }}" method="POST" enctype="multipart/form-data" id="editProductForm">
        @csrf
        @method('PUT')
        
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Product Name *</label>
                                <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                       id="name" name="name" value="{{ old('name', $product->name) }}" required>
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="sku" class="form-label">SKU *</label>
                                <input type="text" class="form-control @error('sku') is-invalid @enderror" 
                                       id="sku" name="sku" value="{{ old('sku', $product->sku) }}" required>
                                @error('sku')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="category_id" class="form-label">Category *</label>
                                <select class="form-select @error('category_id') is-invalid @enderror" 
                                        id="category_id" name="category_id" required>
                                    <option value="">Select Category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" 
                                                {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                        @if($category->children->count() > 0)
                                            @foreach($category->children as $child)
                                                <option value="{{ $child->id }}" 
                                                        {{ old('category_id', $product->category_id) == $child->id ? 'selected' : '' }}>
                                                    &nbsp;&nbsp;└─ {{ $child->name }}
                                                </option>
                                            @endforeach
                                        @endif
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="weight" class="form-label">Weight (kg)</label>
                                <input type="number" class="form-control @error('weight') is-invalid @enderror" 
                                       id="weight" name="weight" step="0.01" min="0" 
                                       value="{{ old('weight', $product->weight) }}">
                                @error('weight')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description</label>
                            <textarea class="form-control @error('short_description') is-invalid @enderror" 
                                      id="short_description" name="short_description" rows="3" 
                                      maxlength="500">{{ old('short_description', $product->short_description) }}</textarea>
                            <div class="form-text">Maximum 500 characters</div>
                            @error('short_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="6">{{ old('description', $product->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i>Additional Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="additional_information" class="form-label">Additional Information</label>
                            <textarea class="form-control @error('additional_information') is-invalid @enderror" 
                                      id="additional_information" name="additional_information" rows="6">{{ old('additional_information', $product->additional_information) }}</textarea>
                            @error('additional_information')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="technical_specifications" class="form-label">Technical Specifications</label>
                            <textarea class="form-control @error('technical_specifications') is-invalid @enderror" 
                                      id="technical_specifications" name="technical_specifications" rows="6">{{ old('technical_specifications', $product->technical_specifications) }}</textarea>
                            @error('technical_specifications')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Pricing & Stock -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-dollar-sign me-2"></i>Pricing & Stock</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Regular Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">@currencySymbol</span>
                                    <input type="number" class="form-control @error('price') is-invalid @enderror"
                                           id="price" name="price" step="0.01" min="0"
                                           value="{{ old('price', $product->price) }}" required>
                                </div>
                                @error('price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="sale_price" class="form-label">Sale Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">@currencySymbol</span>
                                    <input type="number" class="form-control @error('sale_price') is-invalid @enderror"
                                           id="sale_price" name="sale_price" step="0.01" min="0"
                                           value="{{ old('sale_price', $product->sale_price) }}">
                                </div>
                                @error('sale_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="cost_price" class="form-label">Cost Price</label>
                                <div class="input-group">
                                    <span class="input-group-text">@currencySymbol</span>
                                    <input type="number" class="form-control @error('cost_price') is-invalid @enderror"
                                           id="cost_price" name="cost_price" step="0.01" min="0"
                                           value="{{ old('cost_price', $product->cost_price) }}">
                                </div>
                                @error('cost_price')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="date_on_sale_from" class="form-label">Sale Start Date</label>
                                <input type="datetime-local" class="form-control @error('date_on_sale_from') is-invalid @enderror"
                                       id="date_on_sale_from" name="date_on_sale_from"
                                       value="{{ old('date_on_sale_from', $product->date_on_sale_from ? $product->date_on_sale_from->format('Y-m-d\TH:i') : '') }}">
                                @error('date_on_sale_from')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="date_on_sale_to" class="form-label">Sale End Date</label>
                                <input type="datetime-local" class="form-control @error('date_on_sale_to') is-invalid @enderror"
                                       id="date_on_sale_to" name="date_on_sale_to"
                                       value="{{ old('date_on_sale_to', $product->date_on_sale_to ? $product->date_on_sale_to->format('Y-m-d\TH:i') : '') }}">
                                @error('date_on_sale_to')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="manage_stock" name="manage_stock"
                                           value="1" {{ old('manage_stock', $product->manage_stock) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="manage_stock">
                                        Manage Stock
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="hide_price" name="hide_price"
                                           value="1" {{ old('hide_price', $product->hide_price) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="hide_price">
                                        Hide Price from Customers
                                    </label>
                                    <small class="form-text text-muted d-block">When enabled, customers will see "Request Quote" instead of the price</small>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="stock-fields" style="{{ old('manage_stock', $product->manage_stock) ? '' : 'display: none;' }}">
                            <div class="col-md-4 mb-3">
                                <label for="stock_quantity" class="form-label">Stock Quantity</label>
                                <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror"
                                       id="stock_quantity" name="stock_quantity" min="0"
                                       value="{{ old('stock_quantity', $product->stock_quantity) }}">
                                @error('stock_quantity')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="min_stock_level" class="form-label">Low Stock Alert</label>
                                <input type="number" class="form-control @error('min_stock_level') is-invalid @enderror"
                                       id="min_stock_level" name="min_stock_level" min="0"
                                       value="{{ old('min_stock_level', $product->min_stock_level) }}">
                                @error('min_stock_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="stock_status" class="form-label">Stock Status</label>
                                <select class="form-select @error('stock_status') is-invalid @enderror"
                                        id="stock_status" name="stock_status">
                                    <option value="in_stock" {{ old('stock_status', $product->stock_status) == 'in_stock' ? 'selected' : '' }}>In Stock</option>
                                    <option value="out_of_stock" {{ old('stock_status', $product->stock_status) == 'out_of_stock' ? 'selected' : '' }}>Out of Stock</option>
                                    <option value="on_backorder" {{ old('stock_status', $product->stock_status) == 'on_backorder' ? 'selected' : '' }}>On Backorder</option>
                                </select>
                                @error('stock_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product Images -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-images me-2"></i>Product Images</h5>
                    </div>
                    <div class="card-body">
                        <!-- Featured Image -->
                        <div class="mb-4">
                            <label class="form-label">Featured Image</label>
                            @if($product->featured_image)
                                <div class="current-image mb-3">
                                    <img src="{{ asset('storage/' . $product->featured_image) }}"
                                         alt="Current featured image" class="img-thumbnail" style="max-width: 200px;">
                                    <div class="mt-2">
                                        <small class="text-muted">Current featured image</small>
                                    </div>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('featured_image') is-invalid @enderror"
                                   id="featured_image" name="featured_image" accept="image/*">
                            <div class="form-text">Upload a new image to replace the current one. Leave empty to keep current image.</div>
                            @error('featured_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Gallery Images -->
                        <div class="mb-3">
                            <label class="form-label">Gallery Images</label>
                            @if($product->gallery_images && count($product->gallery_images) > 0)
                                <div class="current-gallery mb-3">
                                    <div class="row">
                                        @foreach($product->gallery_images as $index => $image)
                                            <div class="col-md-3 mb-3">
                                                <div class="position-relative">
                                                    <img src="{{ asset('storage/' . $image) }}"
                                                         alt="Gallery image {{ $index + 1 }}"
                                                         class="img-thumbnail w-100">
                                                    <div class="mt-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox"
                                                                   name="remove_gallery_images[]" value="{{ $index }}"
                                                                   id="remove_gallery_{{ $index }}">
                                                            <label class="form-check-label" for="remove_gallery_{{ $index }}">
                                                                <small class="text-danger">Remove</small>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('gallery_images') is-invalid @enderror"
                                   id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                            <div class="form-text">Select multiple images to add to the gallery. Existing images will be kept unless marked for removal.</div>
                            @error('gallery_images')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- SEO & Meta -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>SEO & Meta Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">Meta Title</label>
                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                   id="meta_title" name="meta_title" maxlength="60"
                                   value="{{ old('meta_title', $product->meta_title) }}">
                            <div class="form-text">Recommended: 50-60 characters</div>
                            @error('meta_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                      id="meta_description" name="meta_description" rows="3"
                                      maxlength="160">{{ old('meta_description', $product->meta_description) }}</textarea>
                            <div class="form-text">Recommended: 150-160 characters</div>
                            @error('meta_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                            <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror"
                                   id="meta_keywords" name="meta_keywords"
                                   value="{{ old('meta_keywords', $product->meta_keywords) }}">
                            <div class="form-text">Separate keywords with commas</div>
                            @error('meta_keywords')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="tags" class="form-label">Product Tags</label>
                            <input type="text" class="form-control @error('tags') is-invalid @enderror"
                                   id="tags" name="tags"
                                   value="{{ old('tags', is_array($product->tags) ? implode(', ', $product->tags) : $product->tags) }}">
                            <div class="form-text">Separate tags with commas (e.g., shipping, container, storage)</div>
                            @error('tags')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Product Status -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-toggle-on me-2"></i>Product Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       value="1" {{ old('is_active', $product->is_active) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                       value="1" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_featured">
                                    Featured Product
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="reviews_allowed" name="reviews_allowed" 
                                       value="1" {{ old('reviews_allowed', $product->reviews_allowed) ? 'checked' : '' }}>
                                <label class="form-check-label" for="reviews_allowed">
                                    Allow Reviews
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="sort_order" class="form-label">Sort Order</label>
                            <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                   id="sort_order" name="sort_order" min="0" 
                                   value="{{ old('sort_order', $product->sort_order) }}">
                            @error('sort_order')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Product Type -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cube me-2"></i>Product Type</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_digital" name="is_digital" 
                                       value="1" {{ old('is_digital', $product->is_digital) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_digital">
                                    Digital Product
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_virtual" name="is_virtual" 
                                       value="1" {{ old('is_virtual', $product->is_virtual) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_virtual">
                                    Virtual Product
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dimensions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-ruler-combined me-2"></i>Dimensions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-4 mb-3">
                                <label for="length" class="form-label">Length</label>
                                <input type="number" class="form-control @error('dimensions.length') is-invalid @enderror" 
                                       id="length" name="dimensions[length]" step="0.01" min="0" 
                                       value="{{ old('dimensions.length', $product->dimensions['length'] ?? '') }}">
                                @error('dimensions.length')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-4 mb-3">
                                <label for="width" class="form-label">Width</label>
                                <input type="number" class="form-control @error('dimensions.width') is-invalid @enderror" 
                                       id="width" name="dimensions[width]" step="0.01" min="0" 
                                       value="{{ old('dimensions.width', $product->dimensions['width'] ?? '') }}">
                                @error('dimensions.width')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-4 mb-3">
                                <label for="height" class="form-label">Height</label>
                                <input type="number" class="form-control @error('dimensions.height') is-invalid @enderror" 
                                       id="height" name="dimensions[height]" step="0.01" min="0" 
                                       value="{{ old('dimensions.height', $product->dimensions['height'] ?? '') }}">
                                @error('dimensions.height')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <small class="text-muted">Dimensions in meters</small>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Update Product
                            </button>
                            <a href="{{ route('admin.products.show', $product) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@push('styles')
<style>
.step-progress {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
}

.step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.step-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 60%;
    width: 80%;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.step-item.active:not(:last-child)::after {
    background-color: #0d6efd;
}

.step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 8px;
    position: relative;
    z-index: 2;
}

.step-item.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step-label {
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.step-item.active .step-label {
    color: #0d6efd;
    font-weight: 500;
}

.current-image img, .current-gallery img {
    border: 2px solid #dee2e6;
    border-radius: 8px;
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Stock management toggle
    const manageStockCheckbox = document.getElementById('manage_stock');
    const stockFields = document.getElementById('stock-fields');

    if (manageStockCheckbox) {
        manageStockCheckbox.addEventListener('change', function() {
            if (this.checked) {
                stockFields.style.display = '';
            } else {
                stockFields.style.display = 'none';
            }
        });
    }

    // Auto-generate slug from name
    const nameInput = document.getElementById('name');
    const skuInput = document.getElementById('sku');

    if (nameInput && skuInput) {
        nameInput.addEventListener('input', function() {
            // Only auto-generate if SKU is empty
            if (!skuInput.value) {
                const slug = this.value
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-')
                    .substring(0, 20)
                    .toUpperCase();
                skuInput.value = slug;
            }
        });
    }

    // Character counters
    const metaTitleInput = document.getElementById('meta_title');
    const metaDescInput = document.getElementById('meta_description');

    if (metaTitleInput) {
        addCharacterCounter(metaTitleInput, 60);
    }

    if (metaDescInput) {
        addCharacterCounter(metaDescInput, 160);
    }

    function addCharacterCounter(element, maxLength) {
        const counter = document.createElement('div');
        counter.className = 'form-text';
        counter.style.textAlign = 'right';

        function updateCounter() {
            const remaining = maxLength - element.value.length;
            counter.textContent = `${element.value.length}/${maxLength} characters`;
            counter.style.color = remaining < 10 ? '#dc3545' : '#6c757d';
        }

        element.addEventListener('input', updateCounter);
        element.parentNode.appendChild(counter);
        updateCounter();
    }

    // Form validation
    const form = document.getElementById('editProductForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
                return false;
            }
        });
    }

    // Image preview
    const featuredImageInput = document.getElementById('featured_image');
    const galleryImagesInput = document.getElementById('gallery_images');

    if (featuredImageInput) {
        featuredImageInput.addEventListener('change', function() {
            previewImage(this, 'featured-preview');
        });
    }

    if (galleryImagesInput) {
        galleryImagesInput.addEventListener('change', function() {
            previewMultipleImages(this, 'gallery-preview');
        });
    }

    function previewImage(input, containerId) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                let container = document.getElementById(containerId);
                if (!container) {
                    container = document.createElement('div');
                    container.id = containerId;
                    container.className = 'mt-2';
                    input.parentNode.appendChild(container);
                }
                container.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                    <div class="mt-1"><small class="text-muted">New image preview</small></div>
                `;
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    function previewMultipleImages(input, containerId) {
        if (input.files) {
            let container = document.getElementById(containerId);
            if (!container) {
                container = document.createElement('div');
                container.id = containerId;
                container.className = 'mt-2';
                input.parentNode.appendChild(container);
            }

            container.innerHTML = '<div class="row"></div>';
            const row = container.querySelector('.row');

            Array.from(input.files).forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const col = document.createElement('div');
                    col.className = 'col-md-3 mb-2';
                    col.innerHTML = `
                        <img src="${e.target.result}" alt="Preview ${index + 1}" class="img-thumbnail w-100">
                        <small class="text-muted d-block mt-1">New image ${index + 1}</small>
                    `;
                    row.appendChild(col);
                };
                reader.readAsDataURL(file);
            });
        }
    }
});
</script>
@endpush
