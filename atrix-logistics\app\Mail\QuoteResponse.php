<?php

namespace App\Mail;

use App\Models\Quote;
use App\Models\SiteSetting;
use App\Services\DocumentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Mail\Mailables\Attachment;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;

class QuoteResponse extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected Quote $quote;
    protected array $siteSettings;
    protected ?string $quotePdfPath = null;

    /**
     * Create a new message instance.
     */
    public function __construct(Quote $quote, bool $attachPdf = true)
    {
        $this->quote = $quote;
        $this->siteSettings = cache()->remember('site_settings', 3600, function () {
            return SiteSetting::pluck('value', 'key')->toArray();
        });

        // Generate PDF if requested
        if ($attachPdf) {
            try {
                $documentService = app(DocumentGenerationService::class);
                $this->quotePdfPath = $documentService->generateQuote($this->quote);
            } catch (\Exception $e) {
                // Log error but don't fail email sending
                logger()->error('Failed to generate quote PDF for email', [
                    'quote_id' => $this->quote->id,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $siteName = $this->siteSettings['site_name'] ?? config('app.name');
        
        return new Envelope(
            subject: "Quote Response #{$this->quote->quote_number} - {$siteName}",
            from: $this->siteSettings['notification_email'] ?? config('mail.from.address'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.quote-response',
            with: [
                'quote' => $this->quote,
                'siteSettings' => $this->siteSettings,
                'quoteUrl' => route('quotes.show', $this->quote->quote_number),
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        $attachments = [];

        if ($this->quotePdfPath && Storage::disk('public')->exists($this->quotePdfPath)) {
            $attachments[] = Attachment::fromStorageDisk('public', $this->quotePdfPath)
                ->as("quote_{$this->quote->quote_number}.pdf")
                ->withMime('application/pdf');
        }

        return $attachments;
    }

    /**
     * Clean up generated PDF after sending
     */
    public function __destruct()
    {
        if ($this->quotePdfPath && Storage::disk('public')->exists($this->quotePdfPath)) {
            // Optionally delete the temporary PDF file
            // Storage::disk('public')->delete($this->quotePdfPath);
        }
    }
}
