@extends('layouts.frontend')

@section('title', ($pageTitle ?? 'Tracking Results') . ' - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))
@section('description', 'View detailed tracking information for your shipment including current status, location, and delivery updates.')

@section('content')
<!-- Hero Section -->
<section class="bg-green-600 text-white py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center space-y-4">
            <h1 class="text-3xl lg:text-5xl font-bold font-heading">
                Tracking Results
            </h1>
            <p class="text-xl text-green-100">
                Tracking Number: <span class="font-semibold">{{ $parcel->tracking_number }}</span>
            </p>
        </div>
    </div>
</section>

<!-- Tracking Information -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <!-- Package Overview -->
            <div class="bg-white rounded-2xl shadow-2xl p-8 mb-12 animate-on-scroll">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Package Details -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Package Details</h2>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Tracking Number:</span>
                                <span class="font-semibold text-gray-900">{{ $parcel->tracking_number }}</span>
                            </div>
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Status:</span>
                                <span class="px-3 py-1 rounded-full text-sm font-medium
                                    @if($parcel->status === 'delivered') bg-green-100 text-green-800
                                    @elseif($parcel->status === 'in_transit') bg-blue-100 text-blue-800
                                    @elseif($parcel->status === 'pending') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst(str_replace('_', ' ', $parcel->status)) }}
                                </span>
                            </div>
                            @if($parcel->sender_name)
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Sender:</span>
                                <span class="font-semibold text-gray-900">{{ $parcel->sender_name }}</span>
                            </div>
                            @endif
                            @if($parcel->recipient_name)
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Recipient:</span>
                                <span class="font-semibold text-gray-900">{{ $parcel->recipient_name }}</span>
                            </div>
                            @endif
                            @if($parcel->weight)
                            <div class="flex justify-between items-center py-3 border-b border-gray-100">
                                <span class="text-gray-600">Weight:</span>
                                <span class="font-semibold text-gray-900">{{ $parcel->weight }} kg</span>
                            </div>
                            @endif
                            @if($parcel->estimated_delivery_date)
                            <div class="flex justify-between items-center py-3">
                                <span class="text-gray-600">Estimated Delivery:</span>
                                <span class="font-semibold text-gray-900">{{ $parcel->estimated_delivery_date->format('M d, Y') }}</span>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Current Status -->
                    <div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Current Status</h2>
                        <div class="bg-gray-50 rounded-xl p-6">
                            <div class="text-center">
                                <div class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center
                                    @if($parcel->status === 'delivered') bg-green-100
                                    @elseif($parcel->status === 'in_transit') bg-blue-100
                                    @elseif($parcel->status === 'pending') bg-yellow-100
                                    @else bg-gray-100
                                    @endif">
                                    <i class="text-3xl
                                        @if($parcel->status === 'delivered') fas fa-check text-green-600
                                        @elseif($parcel->status === 'in_transit') fas fa-truck text-blue-600
                                        @elseif($parcel->status === 'pending') fas fa-clock text-yellow-600
                                        @else fas fa-box text-gray-600
                                        @endif"></i>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 mb-2">
                                    {{ ucfirst(str_replace('_', ' ', $parcel->status)) }}
                                </h3>
                                @if($parcel->current_location)
                                <p class="text-gray-600 mb-4">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    {{ $parcel->current_location }}
                                </p>
                                @endif
                                <p class="text-sm text-gray-500">
                                    Last updated: {{ $parcel->updated_at->format('M d, Y \a\t g:i A') }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tracking Timeline -->
            @if($parcel->trackingEvents && $parcel->trackingEvents->count() > 0)
            <div class="bg-white rounded-2xl shadow-2xl p-8 animate-on-scroll">
                <h2 class="text-2xl font-bold text-gray-900 mb-8">Tracking Timeline</h2>
                
                <div class="relative">
                    <!-- Timeline line -->
                    <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                    
                    <div class="space-y-8">
                        @foreach($parcel->trackingEvents->sortByDesc('event_date') as $event)
                        <div class="relative flex items-start">
                            <!-- Timeline dot -->
                            <div class="relative z-10 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-map-marker-alt text-green-600"></i>
                            </div>
                            
                            <!-- Event content -->
                            <div class="ml-6 flex-1">
                                <div class="bg-gray-50 rounded-lg p-6">
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900">{{ $event->event_type }}</h3>
                                        <span class="text-sm text-gray-500">{{ $event->event_date->format('M d, Y \a\t g:i A') }}</span>
                                    </div>
                                    @if($event->location)
                                    <p class="text-gray-600 mb-2">
                                        <i class="fas fa-map-marker-alt mr-2"></i>
                                        {{ $event->location }}
                                    </p>
                                    @endif
                                    @if($event->description)
                                    <p class="text-gray-700">{{ $event->description }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="mt-12 text-center space-y-4">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('tracking.index') }}" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-search mr-2"></i>Track Another Package
                    </a>
                    <button onclick="window.print()" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-8 py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-print mr-2"></i>Print Details
                    </button>
                </div>
                
                <p class="text-sm text-gray-500">
                    Need help? <a href="{{ route('contact') }}" class="text-green-600 hover:text-green-700 font-medium">Contact our support team</a>
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Additional Services -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">Need More Services?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Ship Another Package -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shipping-fast text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Ship Another Package</h3>
                    <p class="text-gray-600 mb-4">Need to send another package? Get a quote and schedule pickup.</p>
                    <button onclick="openQuoteModal()" class="text-green-600 hover:text-green-700 font-semibold">
                        Get Quote <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>

                <!-- Customer Support -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-headset text-2xl text-blue-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Customer Support</h3>
                    <p class="text-gray-600 mb-4">Have questions about your shipment? Our team is here to help.</p>
                    <a href="{{ route('contact') }}" class="text-blue-600 hover:text-blue-700 font-semibold">
                        Contact Us <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>

                <!-- Delivery Options -->
                <div class="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
                    <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-truck text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Delivery Options</h3>
                    <p class="text-gray-600 mb-4">Explore our various delivery speeds and special services.</p>
                    <a href="#" class="text-purple-600 hover:text-purple-700 font-semibold">
                        Learn More <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
