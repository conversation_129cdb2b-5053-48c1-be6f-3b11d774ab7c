# ⚙️ Laravel Technical Specifications - Atrix Logistics

## 🎯 Project Requirements

### Core Technology Stack
- **Laravel Framework:** 10.48.29 (Exact version requirement)
- **PHP Version:** 8.1+ (Laravel 10 requirement)
- **Database:** MySQL 8.0+ or PostgreSQL 13+
- **Web Server:** Nginx or Apache
- **Node.js:** 18+ (for asset compilation)
- **Composer:** 2.0+ (PHP dependency management)

### Additional Laravel Packages
```json
{
  "require": {
    "laravel/framework": "^10.48.29",
    "laravel/sanctum": "^3.3",
    "laravel/tinker": "^2.8",
    "filament/filament": "^3.0",
    "spatie/laravel-permission": "^5.11",
    "intervention/image": "^2.7",
    "barryvdh/laravel-dompdf": "^2.0",
    "maatwebsite/excel": "^3.1"
  },
  "require-dev": {
    "laravel/breeze": "^1.26",
    "laravel/pint": "^1.0",
    "laravel/sail": "^1.18",
    "mockery/mockery": "^1.4.4",
    "nunomaduro/collision": "^7.0",
    "phpunit/phpunit": "^10.1",
    "spatie/laravel-ignition": "^2.0"
  }
}
```

---

## 🏗️ Project Structure

### Laravel Application Structure
```
laravel-app/
├── app/
│   ├── Console/
│   ├── Exceptions/
│   ├── Filament/           # Admin panel resources
│   │   ├── Resources/
│   │   ├── Pages/
│   │   └── Widgets/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Admin/      # Admin controllers
│   │   │   ├── Api/        # API controllers
│   │   │   └── Web/        # Web controllers
│   │   ├── Middleware/
│   │   ├── Requests/       # Form requests
│   │   └── Resources/      # API resources
│   ├── Models/
│   │   ├── User.php
│   │   ├── Parcel.php
│   │   ├── Carrier.php
│   │   ├── Product.php
│   │   ├── Order.php
│   │   └── CmsContent.php
│   ├── Policies/           # Authorization policies
│   ├── Providers/
│   └── Services/           # Business logic services
├── bootstrap/
├── config/
├── database/
│   ├── factories/
│   ├── migrations/
│   └── seeders/
├── public/
│   ├── assets/             # Atrix template assets
│   │   ├── css/
│   │   ├── js/
│   │   ├── images/
│   │   └── fonts/
│   └── storage/            # Uploaded files
├── resources/
│   ├── css/
│   ├── js/
│   └── views/
│       ├── layouts/        # Blade layouts
│       ├── components/     # Blade components
│       ├── pages/          # Page templates
│       └── admin/          # Admin templates
├── routes/
│   ├── web.php
│   ├── api.php
│   └── admin.php
├── storage/
└── tests/
```

---

## 🗄️ Database Configuration

### Environment Configuration (.env)
```env
APP_NAME="Atrix Logistics"
APP_ENV=local
APP_KEY=base64:generated_key_here
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=atrix_logistics
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Custom Application Settings
TRACKING_PREFIX=ATX
DEFAULT_CARRIER_ID=3
COMPANY_NAME="Atrix Logistics"
COMPANY_EMAIL="<EMAIL>"
COMPANY_PHONE="+****************"
```

### Database Configuration (config/database.php)
```php
'mysql' => [
    'driver' => 'mysql',
    'url' => env('DATABASE_URL'),
    'host' => env('DB_HOST', '127.0.0.1'),
    'port' => env('DB_PORT', '3306'),
    'database' => env('DB_DATABASE', 'atrix_logistics'),
    'username' => env('DB_USERNAME', 'forge'),
    'password' => env('DB_PASSWORD', ''),
    'unix_socket' => env('DB_SOCKET', ''),
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
    'prefix_indexes' => true,
    'strict' => true,
    'engine' => null,
    'options' => extension_loaded('pdo_mysql') ? array_filter([
        PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
    ]) : [],
],
```

---

## 🔐 Authentication & Authorization

### Laravel Breeze Setup
```bash
# Install Laravel Breeze
composer require laravel/breeze --dev
php artisan breeze:install blade
npm install && npm run build
php artisan migrate
```

### Role-Based Access Control
```php
// config/permission.php
return [
    'models' => [
        'permission' => Spatie\Permission\Models\Permission::class,
        'role' => Spatie\Permission\Models\Role::class,
    ],
    
    'table_names' => [
        'roles' => 'roles',
        'permissions' => 'permissions',
        'model_has_permissions' => 'model_has_permissions',
        'model_has_roles' => 'model_has_roles',
        'role_has_permissions' => 'role_has_permissions',
    ],
];
```

### User Model Enhancement
```php
// app/Models/User.php
use Spatie\Permission\Traits\HasRoles;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;
    
    protected $fillable = [
        'name', 'email', 'password', 'role', 'phone', 
        'address', 'city', 'state', 'postal_code', 'country'
    ];
    
    protected $hidden = ['password', 'remember_token'];
    
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
    ];
    
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }
    
    public function isCustomer(): bool
    {
        return $this->role === 'customer';
    }
}
```

---

## 🎨 Frontend Asset Management

### Vite Configuration (vite.config.js)
```javascript
import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/css/atrix.css',
                'resources/js/atrix.js'
            ],
            refresh: true,
        }),
    ],
    build: {
        rollupOptions: {
            output: {
                manualChunks: {
                    vendor: ['jquery', 'bootstrap'],
                    atrix: ['owl.carousel', 'wow.js']
                }
            }
        }
    }
});
```

### Asset Integration Strategy
```php
// resources/views/layouts/app.blade.php
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ config('app.name', 'Atrix Logistics') }}</title>
    
    <!-- Atrix Template CSS -->
    <link href="{{ asset('assets/css/font-awesome-all.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/flaticon.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/owl.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/bootstrap.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/jquery.fancybox.min.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/animate.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/color.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/global.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/elpath.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/style.css') }}" rel="stylesheet">
    <link href="{{ asset('assets/css/responsive.css') }}" rel="stylesheet">
    
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body>
    <!-- Preloader -->
    <div class="loader-wrap">
        <div class="preloader">
            <div class="preloader-close">x</div>
            <div id="handle-preloader" class="handle-preloader">
                <div class="animation-preloader">
                    <div class="spinner"></div>
                    <div class="txt-loading">
                        <span data-text-preloader="A" class="letters-loading">A</span>
                        <span data-text-preloader="T" class="letters-loading">T</span>
                        <span data-text-preloader="R" class="letters-loading">R</span>
                        <span data-text-preloader="I" class="letters-loading">I</span>
                        <span data-text-preloader="X" class="letters-loading">X</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="page-wrapper">
        @include('layouts.header')
        
        <main>
            {{ $slot }}
        </main>
        
        @include('layouts.footer')
    </div>
    
    @include('layouts.side-panel')
    @include('layouts.search-popup')
    
    <!-- Atrix Template JS -->
    <script src="{{ asset('assets/js/jquery.js') }}"></script>
    <script src="{{ asset('assets/js/popper.min.js') }}"></script>
    <script src="{{ asset('assets/js/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/owl.js') }}"></script>
    <script src="{{ asset('assets/js/wow.js') }}"></script>
    <script src="{{ asset('assets/js/validation.js') }}"></script>
    <script src="{{ asset('assets/js/jquery.fancybox.js') }}"></script>
    <script src="{{ asset('assets/js/appear.js') }}"></script>
    <script src="{{ asset('assets/js/scrollbar.js') }}"></script>
    <script src="{{ asset('assets/js/isotope.js') }}"></script>
    <script src="{{ asset('assets/js/jquery.nice-select.min.js') }}"></script>
    <script src="{{ asset('assets/js/jQuery.style.switcher.min.js') }}"></script>
    <script src="{{ asset('assets/js/jquery-ui.js') }}"></script>
    <script src="{{ asset('assets/js/nav-tool.js') }}"></script>
    <script src="{{ asset('assets/js/plugins.js') }}"></script>
    <script src="{{ asset('assets/js/script.js') }}"></script>
</body>
</html>
```

---

## 🛠️ Laravel Filament Admin Panel

### Filament Configuration
```php
// config/filament.php
return [
    'default_filesystem_disk' => env('FILAMENT_FILESYSTEM_DISK', 'public'),
    'assets_path' => null,
    'cache_path' => base_path('bootstrap/cache/filament'),
    'livewire_loading_delay' => 'default',
];
```

### Admin Panel Provider
```php
// app/Providers/FilamentServiceProvider.php
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;

public function boot(): void
{
    Filament::serving(function () {
        Filament::registerNavigationGroups([
            NavigationGroup::make('Logistics')
                ->label('Logistics Management')
                ->icon('heroicon-o-truck'),
            NavigationGroup::make('E-commerce')
                ->label('E-commerce')
                ->icon('heroicon-o-shopping-cart'),
            NavigationGroup::make('Content')
                ->label('Content Management')
                ->icon('heroicon-o-document-text'),
            NavigationGroup::make('System')
                ->label('System')
                ->icon('heroicon-o-cog'),
        ]);
    });
}
```

---

## 🔄 Service Layer Architecture

### Service Classes Structure
```php
// app/Services/ParcelService.php
class ParcelService
{
    public function createParcel(array $data): Parcel
    {
        $trackingNumber = $this->generateTrackingNumber();
        
        return Parcel::create([
            'tracking_number' => $trackingNumber,
            ...$data
        ]);
    }
    
    public function updateParcelStatus(Parcel $parcel, string $status, array $eventData = []): void
    {
        $parcel->update(['status' => $status]);
        
        $this->createTrackingEvent($parcel, $status, $eventData);
    }
    
    private function generateTrackingNumber(): string
    {
        $prefix = config('app.tracking_prefix', 'ATX');
        $year = date('Y');
        $sequence = str_pad(Parcel::count() + 1, 8, '0', STR_PAD_LEFT);
        
        return "{$prefix}-{$year}-{$sequence}";
    }
}
```

### Repository Pattern
```php
// app/Repositories/ParcelRepository.php
interface ParcelRepositoryInterface
{
    public function findByTrackingNumber(string $trackingNumber): ?Parcel;
    public function getRecentParcels(int $limit = 10): Collection;
    public function getParcelsByStatus(string $status): Collection;
}

class ParcelRepository implements ParcelRepositoryInterface
{
    public function findByTrackingNumber(string $trackingNumber): ?Parcel
    {
        return Parcel::where('tracking_number', $trackingNumber)
                    ->with(['carrier', 'trackingEvents'])
                    ->first();
    }
}
```

---

## 📊 Performance Optimization

### Caching Strategy
```php
// config/cache.php
'stores' => [
    'redis' => [
        'driver' => 'redis',
        'connection' => 'cache',
        'lock_connection' => 'default',
    ],
    
    'file' => [
        'driver' => 'file',
        'path' => storage_path('framework/cache/data'),
    ],
],

// Cache usage in services
class CmsContentService
{
    public function getHomepageContent(): Collection
    {
        return Cache::remember('homepage_content', 3600, function () {
            return CmsContent::where('section', 'homepage')
                           ->where('is_active', true)
                           ->orderBy('sort_order')
                           ->get();
        });
    }
}
```

### Database Optimization
```php
// Database query optimization
class ParcelController extends Controller
{
    public function index()
    {
        $parcels = Parcel::with(['carrier:id,name', 'user:id,name'])
                        ->select(['id', 'tracking_number', 'status', 'carrier_id', 'user_id'])
                        ->paginate(20);
        
        return view('admin.parcels.index', compact('parcels'));
    }
}
```

---

## 🧪 Testing Configuration

### PHPUnit Configuration
```xml
<!-- phpunit.xml -->
<phpunit bootstrap="vendor/autoload.php">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
    </php>
</phpunit>
```

### Test Examples
```php
// tests/Feature/ParcelTrackingTest.php
class ParcelTrackingTest extends TestCase
{
    public function test_user_can_track_parcel_with_valid_tracking_number()
    {
        $parcel = Parcel::factory()->create();
        
        $response = $this->get("/track/{$parcel->tracking_number}");
        
        $response->assertStatus(200)
                ->assertSee($parcel->tracking_number)
                ->assertSee($parcel->status);
    }
}
```
