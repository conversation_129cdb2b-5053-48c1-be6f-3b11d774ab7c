@extends('layouts.admin')

@section('title', 'Parcel Details - ' . $parcel->tracking_number)
@section('page-title', 'Parcel Details')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.parcels.edit', $parcel) }}" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i> Edit
        </a>
        <a href="{{ route('admin.parcels.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to List
        </a>
        <button type="button" class="btn btn-outline-primary" onclick="window.print()">
            <i class="fas fa-print me-1"></i> Print
        </button>
    </div>
@endsection

@section('content')
    <!-- Pa<PERSON><PERSON>er -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-2">{{ $parcel->tracking_number }}</h3>
                            <p class="text-muted mb-0">{{ $parcel->description }}</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <span class="badge bg-{{ $parcel->getStatusBadgeColor() }} fs-6 mb-2">
                                {{ $parcel->getFormattedStatus() }}
                            </span><br>
                            @if($parcel->is_paid)
                                <span class="badge bg-success">Paid</span>
                            @else
                                <span class="badge bg-warning">Unpaid</span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Parcel Information -->
        <div class="col-lg-8">
            <!-- Sender & Recipient Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Shipping Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">From (Sender)</h6>
                            <address>
                                <strong>{{ $parcel->sender_name }}</strong><br>
                                {{ $parcel->sender_address }}<br>
                                {{ $parcel->sender_city }}, {{ $parcel->sender_state }} {{ $parcel->sender_postal_code }}<br>
                                {{ $parcel->sender_country }}<br>
                                <i class="fas fa-envelope"></i> {{ $parcel->sender_email }}<br>
                                <i class="fas fa-phone"></i> {{ $parcel->sender_phone }}
                            </address>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary">To (Recipient)</h6>
                            <address>
                                <strong>{{ $parcel->recipient_name }}</strong><br>
                                {{ $parcel->recipient_address }}<br>
                                {{ $parcel->recipient_city }}, {{ $parcel->recipient_state }} {{ $parcel->recipient_postal_code }}<br>
                                {{ $parcel->recipient_country }}<br>
                                <i class="fas fa-envelope"></i> {{ $parcel->recipient_email }}<br>
                                <i class="fas fa-phone"></i> {{ $parcel->recipient_phone }}
                            </address>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Package Details</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Description:</strong></td>
                                    <td>{{ $parcel->description }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Weight:</strong></td>
                                    <td>{{ $parcel->weight ? $parcel->weight . ' kg' : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Dimensions:</strong></td>
                                    <td>{{ $parcel->dimensions ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Declared Value:</strong></td>
                                    <td>@currency($parcel->declared_value)</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Service Type:</strong></td>
                                    <td>{{ ucfirst($parcel->service_type) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Carrier:</strong></td>
                                    <td>{{ $parcel->carrier->name }} ({{ $parcel->carrier->code }})</td>
                                </tr>
                                <tr>
                                    <td><strong>Shipping Cost:</strong></td>
                                    <td>@currency($parcel->shipping_cost)</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Cost:</strong></td>
                                    <td><strong>@currency($parcel->total_cost)</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    @if($parcel->special_instructions)
                        <div class="mt-3">
                            <strong>Special Instructions:</strong>
                            <p class="text-muted">{{ $parcel->special_instructions }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Tracking History -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Tracking History</h6>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#addEventModal">
                        <i class="fas fa-plus me-1"></i> Add Event
                    </button>
                </div>
                <div class="card-body">
                    @if($parcel->trackingEvents->count() > 0)
                        <div class="timeline">
                            @foreach($parcel->trackingEvents as $index => $event)
                                <div class="timeline-item {{ $index === 0 ? 'current' : ($event->status === 'delivered' ? 'active' : '') }}">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="fw-bold mb-1">
                                                <i class="fas fa-{{ $event->getStatusIcon() }} me-2 {{ $event->getStatusColor() }}"></i>
                                                {{ $event->getFormattedStatus() }}
                                            </h6>
                                            <p class="mb-1">{{ $event->description }}</p>
                                            @if($event->location)
                                                <small class="text-muted">
                                                    <i class="fas fa-map-pin me-1"></i>{{ $event->location }}
                                                </small>
                                            @endif
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-{{ $event->is_public ? 'eye' : 'eye-slash' }} me-1"></i>
                                                {{ $event->is_public ? 'Public' : 'Internal' }}
                                            </small>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted d-block">{{ $event->event_date->format('M d, Y') }}</small>
                                            <small class="text-muted">{{ $event->event_date->format('h:i A') }}</small>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No tracking events yet</h6>
                            <p class="text-muted">Tracking events will appear here as the package moves through the system.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar Information -->
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Information</h6>
                </div>
                <div class="card-body">
                    <table class="table table-borderless table-sm">
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ $parcel->created_at->format('M d, Y h:i A') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ $parcel->updated_at->format('M d, Y h:i A') }}</td>
                        </tr>
                        @if($parcel->shipped_at)
                            <tr>
                                <td><strong>Shipped:</strong></td>
                                <td>{{ $parcel->shipped_at->format('M d, Y h:i A') }}</td>
                            </tr>
                        @endif
                        @if($parcel->delivered_at)
                            <tr>
                                <td><strong>Delivered:</strong></td>
                                <td>{{ $parcel->delivered_at->format('M d, Y h:i A') }}</td>
                            </tr>
                        @endif
                        @if($parcel->estimated_delivery_date)
                            <tr>
                                <td><strong>Est. Delivery:</strong></td>
                                <td>{{ $parcel->estimated_delivery_date->format('M d, Y') }}</td>
                            </tr>
                        @endif
                    </table>
                </div>
            </div>

            <!-- Customer Information -->
            @if($parcel->user)
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Customer</h6>
                    </div>
                    <div class="card-body">
                        <h6>{{ $parcel->user->name }}</h6>
                        <p class="text-muted mb-2">{{ $parcel->user->email }}</p>
                        <small class="text-muted">
                            <i class="fas fa-user-clock me-1"></i>
                            Member since {{ $parcel->user->created_at->format('M Y') }}
                        </small><br>
                        <small class="text-muted">
                            <i class="fas fa-box me-1"></i>
                            {{ $parcel->user->parcels()->count() }} total parcels
                        </small>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('tracking.track') }}?tracking_number={{ $parcel->tracking_number }}" 
                           class="btn btn-outline-primary" target="_blank">
                            <i class="fas fa-external-link-alt me-1"></i> View Public Tracking
                        </a>
                        <a href="{{ route('admin.parcels.edit', $parcel) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i> Edit Parcel
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="alert('Email customer feature coming soon!')">
                            <i class="fas fa-envelope me-1"></i> Email Customer
                        </button>
                        <button type="button" class="btn btn-outline-danger" 
                                onclick="confirmDelete('{{ $parcel->tracking_number }}', '{{ route('admin.parcels.destroy', $parcel) }}')">
                            <i class="fas fa-trash me-1"></i> Delete Parcel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete parcel <strong id="deleteTrackingNumber"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Parcel</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Tracking Event Modal -->
    <div class="modal fade" id="addEventModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Tracking Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addEventForm" method="POST" action="{{ route('admin.tracking-events.store') }}">
                    @csrf
                    <input type="hidden" name="parcel_id" value="{{ $parcel->id }}">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="event_status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="event_status" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="picked_up">Picked Up</option>
                                    <option value="in_transit">In Transit</option>
                                    <option value="out_for_delivery">Out for Delivery</option>
                                    <option value="delivered">Delivered</option>
                                    <option value="exception">Exception</option>
                                    <option value="returned">Returned</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="event_date" class="form-label">Event Date & Time <span class="text-danger">*</span></label>
                                <input type="datetime-local" class="form-control" id="event_date" name="event_date"
                                       value="{{ now()->format('Y-m-d\TH:i') }}" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="event_location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="event_location" name="location"
                                   placeholder="e.g., Chicago, IL" required>
                        </div>
                        <div class="mb-3">
                            <label for="event_description" class="form-label">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="event_description" name="description" rows="3"
                                      placeholder="Describe what happened..." required></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_is_public" name="is_public" value="1" checked>
                                <label class="form-check-label" for="event_is_public">
                                    Visible to customers
                                </label>
                                <small class="form-text text-muted d-block">Uncheck to make this event internal only</small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Event
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        padding-left: 40px;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -8px;
        top: 8px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: #6c757d;
        border: 3px solid white;
        box-shadow: 0 0 0 3px #e9ecef;
    }
    .timeline-item.active::before {
        background: #28a745;
        box-shadow: 0 0 0 3px #28a745;
    }
    .timeline-item.current::before {
        background: #007bff;
        box-shadow: 0 0 0 3px #007bff;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { box-shadow: 0 0 0 3px #007bff; }
        50% { box-shadow: 0 0 0 8px rgba(0, 123, 255, 0.3); }
        100% { box-shadow: 0 0 0 3px #007bff; }
    }
</style>
@endpush

@push('scripts')
<script>
    function confirmDelete(trackingNumber, deleteUrl) {
        document.getElementById('deleteTrackingNumber').textContent = trackingNumber;
        document.getElementById('deleteForm').action = deleteUrl;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
</script>
@endpush
