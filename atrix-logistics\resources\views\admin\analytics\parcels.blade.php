@extends('layouts.admin')

@section('page-title', 'Parcel Analytics & Statistics')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.parcels.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-box me-1"></i> Manage Parcels
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-calendar me-1"></i> Period: {{ $period }} days
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="?period=7">Last 7 days</a></li>
            <li><a class="dropdown-item" href="?period=30">Last 30 days</a></li>
            <li><a class="dropdown-item" href="?period=90">Last 90 days</a></li>
            <li><a class="dropdown-item" href="?period=365">Last year</a></li>
        </ul>
        <button type="button" class="btn btn-outline-success" onclick="exportAnalytics()">
            <i class="fas fa-download me-1"></i> Export Report
        </button>
    </div>
@endsection

@section('content')
    <!-- Overview Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($overviewStats['total_parcels']) }}</h4>
                            <p class="mb-0">Total Parcels</p>
                            <small class="opacity-75">
                                @if($overviewStats['parcels_growth'] >= 0)
                                    <i class="fas fa-arrow-up"></i> +{{ number_format($overviewStats['parcels_growth'], 1) }}%
                                @else
                                    <i class="fas fa-arrow-down"></i> {{ number_format($overviewStats['parcels_growth'], 1) }}%
                                @endif
                                vs previous period
                            </small>
                        </div>
                        <div>
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($overviewStats['delivery_rate'], 1) }}%</h4>
                            <p class="mb-0">Delivery Rate</p>
                            <small class="opacity-75">{{ number_format($overviewStats['delivered_parcels']) }} delivered</small>
                        </div>
                        <div>
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">@currency($overviewStats['total_revenue'])</h4>
                            <p class="mb-0">Total Revenue</p>
                            <small class="opacity-75">
                                @if($overviewStats['revenue_growth'] >= 0)
                                    <i class="fas fa-arrow-up"></i> +{{ number_format($overviewStats['revenue_growth'], 1) }}%
                                @else
                                    <i class="fas fa-arrow-down"></i> {{ number_format($overviewStats['revenue_growth'], 1) }}%
                                @endif
                                vs previous period
                            </small>
                        </div>
                        <div>
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0">{{ number_format($overviewStats['exception_rate'], 1) }}%</h4>
                            <p class="mb-0">Exception Rate</p>
                            <small class="opacity-75">{{ number_format($overviewStats['exception_parcels']) }} exceptions</small>
                        </div>
                        <div>
                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Parcel Trends Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Parcel Trends Over Time
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="parcelTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Status Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Status Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="statusDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        Performance Metrics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="border-end">
                                <h3 class="text-primary mb-1">{{ $performanceMetrics['avg_delivery_time_days'] }}</h3>
                                <p class="text-muted mb-0">Avg Delivery Time (Days)</p>
                                <small class="text-muted">{{ $performanceMetrics['avg_delivery_time_hours'] }} hours</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border-end">
                                <h3 class="text-success mb-1">{{ $performanceMetrics['on_time_delivery_rate'] }}%</h3>
                                <p class="text-muted mb-0">On-Time Delivery Rate</p>
                                <small class="text-muted">{{ $performanceMetrics['total_delivered'] }} deliveries</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border-end">
                                <h3 class="text-info mb-1">{{ $performanceMetrics['avg_processing_time_days'] }}</h3>
                                <p class="text-muted mb-0">Avg Processing Time (Days)</p>
                                <small class="text-muted">{{ $performanceMetrics['avg_processing_time_hours'] }} hours</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <h3 class="text-warning mb-1">@currency($overviewStats['average_parcel_value'])</h3>
                            <p class="text-muted mb-0">Average Parcel Value</p>
                            <small class="text-muted">Per parcel revenue</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Revenue Analytics -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area me-2"></i>
                        Revenue Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        Payment Status
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($revenueAnalytics['payment_status'] as $status)
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge bg-{{ $status->is_paid ? 'success' : 'warning' }}">
                                {{ $status->is_paid ? 'Paid' : 'Unpaid' }}
                            </span>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">@currency($status->total)</div>
                            <small class="text-muted">{{ $status->count }} parcels</small>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Service Type Analytics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shipping-fast me-2"></i>
                        Service Type Performance
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Service Type</th>
                                    <th>Total Parcels</th>
                                    <th>Delivery Rate</th>
                                    <th>Exception Rate</th>
                                    <th>Avg Cost</th>
                                    <th>Total Revenue</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($serviceTypeAnalytics as $service)
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ ucfirst($service['service_type']) }}</span>
                                    </td>
                                    <td>{{ number_format($service['total_parcels']) }}</td>
                                    <td>
                                        <span class="badge bg-{{ $service['delivery_rate'] >= 90 ? 'success' : ($service['delivery_rate'] >= 75 ? 'warning' : 'danger') }}">
                                            {{ number_format($service['delivery_rate'], 1) }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $service['exception_rate'] <= 5 ? 'success' : ($service['exception_rate'] <= 10 ? 'warning' : 'danger') }}">
                                            {{ number_format($service['exception_rate'], 1) }}%
                                        </span>
                                    </td>
                                    <td>@currency($service['avg_cost'])</td>
                                    <td>@currency($service['total_revenue'])</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Carrier Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-truck me-2"></i>
                        Carrier Performance Comparison
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Carrier</th>
                                    <th>Total Parcels</th>
                                    <th>Delivered</th>
                                    <th>Delivery Rate</th>
                                    <th>Exception Rate</th>
                                    <th>Total Revenue</th>
                                    <th>Avg Parcel Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($carrierPerformance as $carrier)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if(isset($carrier['logo']) && $carrier['logo'])
                                                <img src="{{ asset('storage/' . $carrier['logo']) }}" alt="{{ $carrier['name'] }}"
                                                     class="me-2" style="width: 30px; height: 30px; object-fit: contain;">
                                            @else
                                                <div class="me-2 d-flex align-items-center justify-content-center bg-primary text-white rounded"
                                                     style="width: 30px; height: 30px; font-size: 12px; font-weight: bold;">
                                                    {{ strtoupper(substr($carrier['name'], 0, 2)) }}
                                                </div>
                                            @endif
                                            <strong>{{ $carrier['name'] }}</strong>
                                        </div>
                                    </td>
                                    <td>{{ number_format($carrier['total_parcels']) }}</td>
                                    <td>{{ number_format($carrier['delivered_parcels']) }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{{ $carrier['delivery_rate'] >= 90 ? 'success' : ($carrier['delivery_rate'] >= 75 ? 'warning' : 'danger') }}" 
                                                 style="width: {{ $carrier['delivery_rate'] }}%">
                                                {{ number_format($carrier['delivery_rate'], 1) }}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $carrier['exception_rate'] <= 5 ? 'success' : ($carrier['exception_rate'] <= 10 ? 'warning' : 'danger') }}">
                                            {{ number_format($carrier['exception_rate'], 1) }}%
                                        </span>
                                    </td>
                                    <td>@currency($carrier['total_revenue'] ?? 0)</td>
                                    <td>@currency($carrier['avg_parcel_value'])</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Geographic Distribution -->
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Top Sender Cities
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($geographicData['top_sender_cities'] as $city)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ $city->sender_city }}, {{ $city->sender_state }}</span>
                        <span class="badge bg-primary">{{ $city->count }}</span>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-pin me-2"></i>
                        Top Recipient Cities
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($geographicData['top_recipient_cities'] as $city)
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ $city->recipient_city }}, {{ $city->recipient_state }}</span>
                        <span class="badge bg-success">{{ $city->count }}</span>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Chart configurations
const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'top',
        }
    },
    scales: {
        y: {
            beginAtZero: true
        }
    }
};

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeParcelTrendsChart();
    initializeStatusDistributionChart();
    initializeRevenueTrendsChart();
});

// Parcel Trends Chart
function initializeParcelTrendsChart() {
    const ctx = document.getElementById('parcelTrendsChart').getContext('2d');

    fetch(`{{ route('admin.analytics.parcels.trends-data') }}?period={{ $period }}`)
        .then(response => response.json())
        .then(data => {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.map(item => new Date(item.date).toLocaleDateString()),
                    datasets: [{
                        label: 'Parcels Created',
                        data: data.map(item => item.count),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1,
                        fill: true
                    }]
                },
                options: {
                    ...chartOptions,
                    plugins: {
                        ...chartOptions.plugins,
                        title: {
                            display: true,
                            text: 'Daily Parcel Creation Trends'
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error loading parcel trends data:', error);
        });
}

// Status Distribution Chart
function initializeStatusDistributionChart() {
    const ctx = document.getElementById('statusDistributionChart').getContext('2d');

    fetch(`{{ route('admin.analytics.parcels.status-data') }}?period={{ $period }}`)
        .then(response => response.json())
        .then(data => {
            const statusColors = {
                'pending': '#6c757d',
                'picked_up': '#17a2b8',
                'in_transit': '#007bff',
                'out_for_delivery': '#ffc107',
                'delivered': '#28a745',
                'exception': '#dc3545',
                'returned': '#343a40'
            };

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => item.status.replace('_', ' ').toUpperCase()),
                    datasets: [{
                        data: data.map(item => item.count),
                        backgroundColor: data.map(item => statusColors[item.status] || '#6c757d'),
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        },
                        title: {
                            display: true,
                            text: 'Parcel Status Distribution'
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error loading status distribution data:', error);
        });
}

// Revenue Trends Chart
function initializeRevenueTrendsChart() {
    const ctx = document.getElementById('revenueTrendsChart').getContext('2d');

    fetch(`{{ route('admin.analytics.parcels.revenue-data') }}?period={{ $period }}`)
        .then(response => response.json())
        .then(data => {
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: data.map(item => new Date(item.date).toLocaleDateString()),
                    datasets: [{
                        label: 'Daily Revenue ($)',
                        data: data.map(item => parseFloat(item.revenue)),
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    ...chartOptions,
                    plugins: {
                        ...chartOptions.plugins,
                        title: {
                            display: true,
                            text: 'Daily Revenue Trends'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('Error loading revenue trends data:', error);
        });
}

// Export analytics function
function exportAnalytics() {
    // Create export data
    const exportData = {
        period: '{{ $period }}',
        overview: @json($overviewStats),
        performance: @json($performanceMetrics),
        revenue: @json($revenueAnalytics),
        carriers: @json($carrierPerformance),
        services: @json($serviceTypeAnalytics),
        geographic: @json($geographicData)
    };

    // Convert to CSV or trigger download
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", `parcel-analytics-${new Date().toISOString().split('T')[0]}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();

    // Show success message
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        Analytics report exported successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Auto-refresh data every 5 minutes
setInterval(function() {
    initializeParcelTrendsChart();
    initializeStatusDistributionChart();
    initializeRevenueTrendsChart();
}, 300000); // 5 minutes
</script>
@endpush

@push('styles')
<style>
.border-end {
    border-right: 1px solid #dee2e6 !important;
}

.progress {
    background-color: #e9ecef;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75em;
}

/* Chart containers */
#parcelTrendsChart,
#statusDistributionChart,
#revenueTrendsChart {
    max-height: 400px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 1px solid #dee2e6 !important;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }

    .border-end:last-child {
        border-bottom: none !important;
        margin-bottom: 0;
        padding-bottom: 0;
    }
}

/* Animation for cards */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

/* Status badges */
.badge.bg-success { background-color: #28a745 !important; }
.badge.bg-warning { background-color: #ffc107 !important; color: #212529 !important; }
.badge.bg-danger { background-color: #dc3545 !important; }
.badge.bg-primary { background-color: #007bff !important; }
.badge.bg-info { background-color: #17a2b8 !important; }
.badge.bg-secondary { background-color: #6c757d !important; }
</style>
@endpush
