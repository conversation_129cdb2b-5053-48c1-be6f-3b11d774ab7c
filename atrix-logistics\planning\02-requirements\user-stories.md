# 👥 User Stories - Atrix Logistics

## 🎭 User Personas

### 👤 Admin User
**Role:** Site Administrator/Staff Member
**Goals:** Manage website content, track parcels, handle orders
**Tech Savvy:** High
**Access Level:** Full administrative access

### 🛒 Customer (Registered)
**Role:** Registered customer who shops products
**Goals:** Browse products, place orders, track purchases
**Tech Savvy:** Medium
**Access Level:** Customer account access

### 👁️ Visitor (Anonymous)
**Role:** Website visitor/potential customer
**Goals:** Browse information, track parcels, get quotes
**Tech Savvy:** Low to Medium
**Access Level:** Public access only

## 📋 Epic 1: Website Management (Admin)

### 🔐 Authentication & Access
**As an admin user, I want to:**
- Log into a secure admin dashboard
- Manage my admin profile and password
- Have role-based access to different features
- Stay logged in securely across sessions

### 📦 Parcel Management
**As an admin user, I want to:**
- Register new parcels in the system
- Update parcel status and location
- Add tracking events and notes
- View all parcels in a searchable list
- Generate tracking numbers automatically
- Assign parcels to different carriers

### 🛍️ Product Management
**As an admin user, I want to:**
- Add new products to the catalog
- Edit product details and pricing
- Upload product images
- Manage product inventory levels
- Set product categories and tags
- Enable/disable products

### 📝 Content Management
**As an admin user, I want to:**
- Update site branding (logo, site title, favicon)
- Manage homepage content and banners/sliders
- Edit about us content and company information
- Update team member information with photos and roles
- Manage blog posts and news articles
- Edit service descriptions and pricing
- Update contact information (email, phone, address)
- Manage company branches and locations
- Control testimonials and reviews
- Manage site-wide settings and configurations

### 📊 Order Management
**As an admin user, I want to:**
- View all customer orders
- Update order status
- Process order fulfillment
- Handle order cancellations
- Generate order reports
- Manage customer communications

## 📋 Epic 2: Customer Experience (Registered Users)

### 🔐 Account Management
**As a registered customer, I want to:**
- Create an account with email verification
- Log in and out securely
- Update my profile information
- View my order history
- Reset my password if forgotten

### 🛒 Shopping Experience
**As a registered customer, I want to:**
- Browse products by category
- Search for specific products
- View detailed product information
- Add products to shopping cart
- Proceed through checkout process
- Receive order confirmation
- Inquire about products without purchasing
- Request quotes for multiple products

### 📦 Order Tracking
**As a registered customer, I want to:**
- View my order status
- Track my shipments
- Receive notifications about order updates
- Download order receipts
- Contact support about orders

## 📋 Epic 3: Public Access (Visitors)

### 🌐 Website Browsing
**As a website visitor, I want to:**
- View the homepage with company information
- Browse available services
- Read about the company
- View testimonials and reviews
- Access contact information
- Read blog posts and news

### 📦 Parcel Tracking
**As a website visitor, I want to:**
- Enter a tracking number to check status
- View detailed tracking information
- See tracking history and events
- Get estimated delivery dates
- Access tracking without registration

### 🛒 Product Browsing
**As a website visitor, I want to:**
- Browse products without registration
- View product details and images
- See pricing information
- View product categories
- Search for products
- See "coming soon" message if no products
- Inquire about products without registration
- Request quotes for products I'm interested in
- Add multiple products to quote request

### 📞 Contact & Quotes
**As a website visitor, I want to:**
- Fill out contact forms
- Request free quotes through modal popup
- Request quotes for specific products
- View company contact information
- Access customer support
- Submit inquiries about services
- Use "Track Now" from navigation and footer
- Access quote modal from any page

## 📋 Epic 4: Quote & Inquiry System

### 💬 Quote Modal System
**As any user, I want to:**
- Access "Get A Quote" modal from any page
- Fill out quote request forms quickly
- Add product details to quote requests
- Submit quotes without registration
- Receive confirmation of quote submission

### 🛍️ Product Inquiry System
**As a product viewer, I want to:**
- Choose between "Add to Cart" (requires login) or "Inquire" (no login)
- Have product details pre-filled in quote modal
- Add multiple products to single quote request
- Modify product quantities in quote
- Submit product inquiries easily

### 🧭 Enhanced Navigation
**As any user, I want to:**
- See "Track Now" in main navigation
- Access "Track Now" from footer
- Use consistent navigation across all pages
- Access quote modal from navigation
- Have quick access to key functions

## 📋 Epic 5: System Features

### 🔍 Search & Navigation
**As any user, I want to:**
- Use the search functionality
- Navigate easily between pages
- Access consistent header/footer
- Use mobile-friendly navigation
- Find information quickly

### 📱 Responsive Design
**As any user, I want to:**
- Access the site on any device
- Have consistent experience across devices
- Fast loading times
- Readable content on mobile
- Touch-friendly interactions

### 🎨 User Interface
**As any user, I want to:**
- See professional design
- Experience smooth animations
- Have intuitive interactions
- See loading indicators
- Access help information

## ✅ Acceptance Criteria Template

### For each user story:
- **Given** [initial context]
- **When** [action performed]
- **Then** [expected outcome]
- **And** [additional conditions]

### Example:
**Story:** As a visitor, I want to track a parcel
- **Given** I am on the tracking page
- **When** I enter a valid tracking number
- **Then** I should see the current status and location
- **And** I should see the tracking history
- **And** I should see estimated delivery date

## 🎯 Priority Levels

### High Priority (Must Have)
- Admin authentication and dashboard
- Parcel tracking system
- Basic content management
- Responsive design

### Medium Priority (Should Have)
- Product catalog and shopping
- Advanced content management
- Order management system
- Customer accounts

### Low Priority (Could Have)
- Advanced reporting
- Email notifications
- Advanced search features
- Social media integration
