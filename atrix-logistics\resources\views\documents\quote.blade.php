@extends('documents.base')

@section('title', 'Quote #' . $quote->quote_number)
@section('document-title', 'QUOTATION')

@section('header-right-extra')
    <div style="margin-top: 10px;">
        <span class="badge badge-{{ $quote->status === 'approved' ? 'success' : ($quote->status === 'pending' ? 'warning' : 'info') }}">
            {{ ucfirst($quote->status) }}
        </span>
        <br>
        <span class="badge badge-{{ $quote->priority === 'high' ? 'danger' : ($quote->priority === 'medium' ? 'warning' : 'info') }}" style="margin-top: 5px;">
            {{ ucfirst($quote->priority) }} Priority
        </span>
    </div>
@endsection

@section('content')
    <!-- Customer Information -->
    <div class="address-section">
        <div class="address-box">
            <div class="address-title">Quote For:</div>
            <div class="address-content">
                <strong>{{ $quote->customer_name }}</strong><br>
                @if($quote->customer_email)
                    {{ $quote->customer_email }}<br>
                @endif
                @if($quote->customer_phone)
                    Phone: {{ $quote->customer_phone }}<br>
                @endif
                @if($quote->customer_company)
                    Company: {{ $quote->customer_company }}<br>
                @endif
                @if($quote->customer_address)
                    {{ $quote->customer_address }}
                @endif
            </div>
        </div>
        <div class="address-box">
            <div class="address-title">Quote Details:</div>
            <div class="address-content">
                <strong>Quote Number:</strong> {{ $quote->quote_number }}<br>
                <strong>Quote Date:</strong> {{ $quote->created_at->format('M j, Y') }}<br>
                <strong>Valid Until:</strong> {{ $quote->expires_at ? $quote->expires_at->format('M j, Y') : 'N/A' }}<br>
                <strong>Assigned To:</strong> {{ $quote->assignedTo->name ?? 'Unassigned' }}<br>
                <strong>Service Type:</strong> {{ ucfirst($quote->service_type) }}
            </div>
        </div>
    </div>

    <!-- Quote Information -->
    <div class="info-box">
        <div class="info-box-title">Service Requirements</div>
        <table style="width: 100%; border: none;">
            @if($quote->origin_address)
            <tr>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Origin:</strong></td>
                <td style="border: none; padding: 5px 0; width: 75%;">{{ $quote->origin_address }}</td>
            </tr>
            @endif
            @if($quote->destination_address)
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Destination:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $quote->destination_address }}</td>
            </tr>
            @endif
            @if($quote->package_details)
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Package Details:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $quote->package_details }}</td>
            </tr>
            @endif
            @if($quote->special_requirements)
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Special Requirements:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ $quote->special_requirements }}</td>
            </tr>
            @endif
        </table>
    </div>

    <!-- Service Breakdown -->
    <div class="section">
        <div class="section-title">Service Breakdown</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 10%;">#</th>
                    <th style="width: 50%;">Service Description</th>
                    <th style="width: 15%; text-align: center;">Quantity</th>
                    <th style="width: 25%; text-align: right;">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>
                        <strong>{{ ucfirst($quote->service_type) }} Service</strong><br>
                        <small class="text-muted">
                            @if($quote->origin_address && $quote->destination_address)
                                From: {{ $quote->origin_address }}<br>
                                To: {{ $quote->destination_address }}
                            @endif
                        </small>
                    </td>
                    <td class="text-center">1</td>
                    <td class="text-right">
                        @if($quote->quoted_price)
                            @currency($quote->quoted_price)
                        @else
                            <em>To be determined</em>
                        @endif
                    </td>
                </tr>
                @if($quote->additional_services)
                <tr>
                    <td>2</td>
                    <td>
                        <strong>Additional Services</strong><br>
                        <small class="text-muted">{{ $quote->additional_services }}</small>
                    </td>
                    <td class="text-center">1</td>
                    <td class="text-right">
                        @if($quote->additional_charges)
                            @currency($quote->additional_charges)
                        @else
                            <em>Included</em>
                        @endif
                    </td>
                </tr>
                @endif
            </tbody>
        </table>
    </div>

    <!-- Quote Totals -->
    @if($quote->quoted_price)
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="total-label">Base Service:</td>
                <td class="total-value">@currency($quote->quoted_price)</td>
            </tr>
            @if($quote->additional_charges)
            <tr>
                <td class="total-label">Additional Services:</td>
                <td class="total-value">@currency($quote->additional_charges)</td>
            </tr>
            @endif
            @if($quote->tax_amount)
            <tr>
                <td class="total-label">Tax:</td>
                <td class="total-value">@currency($quote->tax_amount)</td>
            </tr>
            @endif
            <tr class="grand-total">
                <td class="total-label">Total Quote:</td>
                <td class="total-value">@currency($quote->total_amount ?? $quote->quoted_price)</td>
            </tr>
        </table>
    </div>
    @endif

    <!-- Quote Validity and Terms -->
    <div class="section">
        <div class="section-title">Quote Validity & Terms</div>
        <div class="info-box">
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="border: none; padding: 5px 0; width: 25%;"><strong>Valid Until:</strong></td>
                    <td style="border: none; padding: 5px 0; width: 25%;">
                        {{ $quote->expires_at ? $quote->expires_at->format('M j, Y') : '30 days from quote date' }}
                    </td>
                    <td style="border: none; padding: 5px 0; width: 25%;"><strong>Estimated Delivery:</strong></td>
                    <td style="border: none; padding: 5px 0; width: 25%;">
                        {{ $quote->estimated_delivery ?? 'To be confirmed' }}
                    </td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Payment Terms:</strong></td>
                    <td style="border: none; padding: 5px 0;" colspan="3">
                        {{ $quote->payment_terms ?? 'Payment due upon service completion' }}
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Additional Notes -->
    @if($quote->notes || $quote->internal_notes)
    <div class="section">
        <div class="section-title">Additional Information</div>
        @if($quote->notes)
        <div class="info-box">
            <div class="info-box-title">Customer Notes</div>
            <p style="margin: 0; font-size: 11px;">{{ $quote->notes }}</p>
        </div>
        @endif
        @if($quote->internal_notes)
        <div class="info-box">
            <div class="info-box-title">Service Notes</div>
            <p style="margin: 0; font-size: 11px;">{{ $quote->internal_notes }}</p>
        </div>
        @endif
    </div>
    @endif

    <!-- Terms and Conditions -->
    <div class="section">
        <div class="section-title">Terms & Conditions</div>
        <div style="font-size: 10px; line-height: 1.4; color: #666;">
            <p><strong>Quote Validity:</strong> This quote is valid for 30 days from the date of issue unless otherwise specified.</p>
            <p><strong>Service Delivery:</strong> Delivery times are estimates and may vary based on circumstances beyond our control.</p>
            <p><strong>Payment:</strong> Payment terms will be confirmed upon service booking and may require advance payment.</p>
            <p><strong>Liability:</strong> Our liability is limited to the declared value of goods or services as per our standard terms.</p>
            <p><strong>Changes:</strong> Any changes to requirements may affect the quoted price and delivery time.</p>
            @if(isset($siteSettings['quote_terms']))
                <p>{{ $siteSettings['quote_terms'] }}</p>
            @endif
        </div>
    </div>

    <!-- Next Steps -->
    <div class="section">
        <div class="section-title">Next Steps</div>
        <div class="info-box">
            <p style="margin: 0 0 10px 0; font-size: 11px;"><strong>To proceed with this quote:</strong></p>
            <ol style="margin: 0; padding-left: 20px; font-size: 11px;">
                <li>Review the quote details and terms carefully</li>
                <li>Contact us if you have any questions or need modifications</li>
                <li>Confirm your acceptance to proceed with booking</li>
                <li>We will schedule the service and provide tracking information</li>
            </ol>
            <p style="margin: 10px 0 0 0; font-size: 11px;">
                <strong>Contact:</strong> {{ $siteSettings['company_phone'] ?? 'Phone Number' }} | 
                {{ $siteSettings['company_email'] ?? '<EMAIL>' }}
            </p>
        </div>
    </div>
@endsection

@section('footer')
    <div style="text-align: center; margin-bottom: 10px;">
        <strong>Thank you for considering our services!</strong>
    </div>
    <div style="text-align: center; font-size: 10px;">
        Quote Reference: {{ $quote->quote_number }} | 
        For questions, contact: {{ $siteSettings['company_email'] ?? '<EMAIL>' }}
    </div>
@endsection
