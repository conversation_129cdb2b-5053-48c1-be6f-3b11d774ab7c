<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote #{{ $quote->quote_number }} - Atrix Logistics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .quote-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .status-badge {
            font-size: 1.1rem;
            padding: 8px 16px;
        }
        .product-item {
            border-left: 4px solid #667eea;
            background: #f8f9fa;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark header-gradient">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-shipping-fast me-2"></i>
                Atrix Logistics
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ route('quotes.show') }}">
                    <i class="fas fa-arrow-left me-2"></i>Back to Lookup
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row">
            <div class="col-12">
                <!-- Quote Header -->
                <div class="card quote-card mb-4">
                    <div class="card-header header-gradient text-white">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h3 class="mb-0">
                                    <i class="fas fa-quote-left me-2"></i>
                                    Quote #{{ $quote->quote_number }}
                                </h3>
                                <p class="mb-0 mt-2">
                                    {{ $quote->isProductQuote() ? 'Product Quote' : 'Shipping Quote' }} • 
                                    Requested {{ $quote->created_at->format('M d, Y') }}
                                </p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <span class="badge status-badge bg-{{ $quote->status_badge_color }}">
                                    {{ $quote->formatted_status }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">Customer Information</h6>
                                <p class="mb-1"><strong>Name:</strong> {{ $quote->customer_name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $quote->customer_email }}</p>
                                @if($quote->customer_phone)
                                <p class="mb-1"><strong>Phone:</strong> {{ $quote->customer_phone }}</p>
                                @endif
                                @if($quote->company_name)
                                <p class="mb-1"><strong>Company:</strong> {{ $quote->company_name }}</p>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">Quote Details</h6>
                                <p class="mb-1"><strong>Priority:</strong> 
                                    <span class="badge bg-{{ $quote->priority_badge_color }}">{{ $quote->formatted_priority }}</span>
                                </p>
                                @if($quote->quoted_at)
                                <p class="mb-1"><strong>Quoted:</strong> {{ $quote->quoted_at->format('M d, Y H:i') }}</p>
                                @endif
                                @if($quote->expires_at)
                                <p class="mb-1"><strong>Expires:</strong> {{ $quote->expires_at->format('M d, Y') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                @if($quote->isProductQuote())
                <!-- Product Quote Details -->
                <div class="card quote-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            Requested Products
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($quote->products)
                        <div class="row">
                            @foreach($quote->products as $item)
                            <div class="col-12 mb-3">
                                <div class="product-item p-3 rounded">
                                    <div class="row align-items-center">
                                        <div class="col-md-6">
                                            <h6 class="mb-1">{{ $item['product_name'] }}</h6>
                                            <small class="text-muted">SKU: {{ $item['product_sku'] }}</small>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <strong>Qty: {{ $item['quantity'] }}</strong>
                                        </div>
                                        <div class="col-md-2 text-center">
                                            <strong>${{ number_format($item['price_at_time'], 2) }}</strong>
                                            <br><small class="text-muted">each</small>
                                        </div>
                                        <div class="col-md-2 text-end">
                                            <strong class="text-primary">${{ number_format($item['total'], 2) }}</strong>
                                        </div>
                                    </div>
                                    @if($item['notes'])
                                    <div class="mt-2">
                                        <small class="text-muted"><strong>Notes:</strong> {{ $item['notes'] }}</small>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                        
                        <hr>
                        <div class="row">
                            <div class="col-md-8">
                                <strong>Total Products Value:</strong>
                            </div>
                            <div class="col-md-4 text-end">
                                <strong class="text-primary fs-5">${{ number_format($quote->products_total, 2) }}</strong>
                            </div>
                        </div>
                        @endif

                        @if($quote->product_requirements)
                        <div class="mt-4">
                            <h6 class="fw-bold">Special Requirements</h6>
                            <p class="text-muted">{{ $quote->product_requirements }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                @else
                <!-- Shipping Quote Details -->
                <div class="card quote-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-shipping-fast me-2"></i>
                            Shipping Details
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="fw-bold">Service Information</h6>
                                <p class="mb-1"><strong>Service Type:</strong> {{ $quote->formatted_service_type }}</p>
                                @if($quote->package_count)
                                <p class="mb-1"><strong>Packages:</strong> {{ $quote->package_count }}</p>
                                @endif
                                @if($quote->total_weight)
                                <p class="mb-1"><strong>Weight:</strong> {{ $quote->total_weight }} {{ $quote->weight_unit }}</p>
                                @endif
                                @if($quote->declared_value)
                                <p class="mb-1"><strong>Declared Value:</strong> ${{ number_format($quote->declared_value, 2) }}</p>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <h6 class="fw-bold">Route</h6>
                                <p class="mb-1"><strong>From:</strong> {{ $quote->origin_city }}, {{ $quote->origin_state }}</p>
                                <p class="mb-1"><strong>To:</strong> {{ $quote->destination_city }}, {{ $quote->destination_state }}</p>
                                @if($quote->preferred_pickup_date)
                                <p class="mb-1"><strong>Pickup Date:</strong> {{ $quote->preferred_pickup_date->format('M d, Y') }}</p>
                                @endif
                                @if($quote->required_delivery_date)
                                <p class="mb-1"><strong>Delivery Date:</strong> {{ $quote->required_delivery_date->format('M d, Y') }}</p>
                                @endif
                            </div>
                        </div>

                        @if($quote->description)
                        <div class="mt-3">
                            <h6 class="fw-bold">Description</h6>
                            <p class="text-muted">{{ $quote->description }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Pricing Information -->
                @if($quote->quoted_price)
                <div class="card quote-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-dollar-sign me-2"></i>
                            Pricing Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                @if($quote->pricing_breakdown)
                                <h6 class="fw-bold">Pricing Breakdown</h6>
                                <p class="text-muted">{{ $quote->pricing_breakdown }}</p>
                                @endif
                                
                                <div class="row">
                                    <div class="col-6">
                                        <strong>Quoted Price:</strong>
                                    </div>
                                    <div class="col-6 text-end">
                                        <strong>${{ number_format($quote->quoted_price, 2) }}</strong>
                                    </div>
                                </div>
                                
                                @if($quote->discount_amount > 0)
                                <div class="row">
                                    <div class="col-6">
                                        <span class="text-success">Discount:</span>
                                    </div>
                                    <div class="col-6 text-end">
                                        <span class="text-success">-${{ number_format($quote->discount_amount, 2) }}</span>
                                    </div>
                                </div>
                                @endif
                                
                                <hr>
                                <div class="row">
                                    <div class="col-6">
                                        <strong class="fs-5">Final Price:</strong>
                                    </div>
                                    <div class="col-6 text-end">
                                        <strong class="fs-4 text-primary">${{ number_format($quote->final_price ?? $quote->quoted_price, 2) }}</strong>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                @if($quote->canBeAccepted())
                                <div class="d-grid gap-2">
                                    <form method="POST" action="{{ route('quotes.accept', $quote) }}">
                                        @csrf
                                        <input type="hidden" name="customer_email" value="{{ $quote->customer_email }}">
                                        <button type="submit" class="btn btn-success btn-lg w-100">
                                            <i class="fas fa-check me-2"></i>Accept Quote
                                        </button>
                                    </form>
                                    
                                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
                                        <i class="fas fa-times me-2"></i>Reject Quote
                                    </button>
                                </div>
                                @elseif($quote->isAccepted())
                                <div class="alert alert-success text-center">
                                    <i class="fas fa-check-circle fa-2x mb-2"></i>
                                    <h6>Quote Accepted!</h6>
                                    <small>We will contact you to proceed with the order.</small>
                                </div>
                                @elseif($quote->status === 'rejected')
                                <div class="alert alert-danger text-center">
                                    <i class="fas fa-times-circle fa-2x mb-2"></i>
                                    <h6>Quote Rejected</h6>
                                </div>
                                @elseif($quote->isExpired())
                                <div class="alert alert-warning text-center">
                                    <i class="fas fa-clock fa-2x mb-2"></i>
                                    <h6>Quote Expired</h6>
                                    <small>Please request a new quote.</small>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Admin Notes -->
                @if($quote->admin_notes)
                <div class="card quote-card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            Additional Notes
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">{{ $quote->admin_notes }}</p>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Reject Modal -->
    <div class="modal fade" id="rejectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Quote</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" action="{{ route('quotes.reject', $quote) }}">
                    @csrf
                    <input type="hidden" name="customer_email" value="{{ $quote->customer_email }}">
                    <div class="modal-body">
                        <p>Are you sure you want to reject this quote?</p>
                        <div class="mb-3">
                            <label for="rejection_reason" class="form-label">Reason (Optional)</label>
                            <textarea class="form-control" id="rejection_reason" name="rejection_reason" rows="3" 
                                      placeholder="Let us know why you're rejecting this quote..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">Reject Quote</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
