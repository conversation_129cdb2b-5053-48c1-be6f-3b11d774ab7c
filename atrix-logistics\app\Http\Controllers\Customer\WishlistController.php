<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Wishlist;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class WishlistController extends Controller
{
    /**
     * Display the user's wishlist
     */
    public function index(): View
    {
        $user = Auth::user();

        $wishlistItems = $user->wishlist()
                             ->with(['product.category'])
                             ->latest()
                             ->paginate(20);

        return view('customer.wishlist', compact('wishlistItems'));
    }

    /**
     * Add product to wishlist
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'notes' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $product = Product::findOrFail($request->product_id);

        // Check if already in wishlist
        if (Wishlist::isInWishlist($user->id, $product->id)) {
            return response()->json([
                'success' => false,
                'message' => 'Product is already in your wishlist.',
            ], 409);
        }

        $wishlistItem = Wishlist::addToWishlist(
            $user->id,
            $product->id,
            $request->notes
        );

        return response()->json([
            'success' => true,
            'message' => 'Product added to wishlist successfully.',
            'wishlist_count' => $user->wishlist()->count(),
        ]);
    }

    /**
     * Remove product from wishlist
     */
    public function destroy(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $user = Auth::user();
        $removed = Wishlist::removeFromWishlist($user->id, $request->product_id);

        if ($removed) {
            return response()->json([
                'success' => true,
                'message' => 'Product removed from wishlist.',
                'wishlist_count' => $user->wishlist()->count(),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Product not found in wishlist.',
        ], 404);
    }

    /**
     * Remove wishlist item by ID
     */
    public function remove(Wishlist $wishlist): RedirectResponse
    {
        // Ensure the wishlist item belongs to the authenticated user
        if ($wishlist->user_id !== Auth::id()) {
            abort(404);
        }

        $wishlist->delete();

        return redirect()->route('customer.wishlist')
                        ->with('success', 'Product removed from wishlist.');
    }

    /**
     * Update wishlist item notes
     */
    public function updateNotes(Request $request, Wishlist $wishlist): JsonResponse
    {
        $request->validate([
            'notes' => 'nullable|string|max:500',
        ]);

        // Ensure the wishlist item belongs to the authenticated user
        if ($wishlist->user_id !== Auth::id()) {
            abort(404);
        }

        $wishlist->update(['notes' => $request->notes]);

        return response()->json([
            'success' => true,
            'message' => 'Notes updated successfully.',
        ]);
    }

    /**
     * Get wishlist count for AJAX requests
     */
    public function count(): JsonResponse
    {
        $user = Auth::user();

        return response()->json([
            'count' => $user->wishlist()->count(),
        ]);
    }

    /**
     * Check if product is in wishlist
     */
    public function check(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
        ]);

        $user = Auth::user();
        $inWishlist = Wishlist::isInWishlist($user->id, $request->product_id);

        return response()->json([
            'in_wishlist' => $inWishlist,
        ]);
    }

    /**
     * Clear entire wishlist
     */
    public function clear(): RedirectResponse
    {
        $user = Auth::user();
        $user->wishlist()->delete();

        return redirect()->route('customer.wishlist')
                        ->with('success', 'Wishlist cleared successfully.');
    }
}
