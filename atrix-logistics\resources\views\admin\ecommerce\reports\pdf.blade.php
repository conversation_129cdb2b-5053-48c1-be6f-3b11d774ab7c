<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>E-commerce Analytics Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .section h2 {
            color: #007bff;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .stats-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .stats-row {
            display: table-row;
        }
        
        .stats-cell {
            display: table-cell;
            width: 50%;
            padding: 10px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        
        .stat-item {
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-weight: bold;
            color: #555;
        }
        
        .stat-value {
            font-size: 16px;
            color: #007bff;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .badge-success {
            background-color: #28a745;
            color: white;
        }
        
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .badge-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .badge-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>E-commerce Analytics Report</h1>
        <p>Generated on: {{ $generated_at }}</p>
        <p>Period: Last {{ $period }} days</p>
        <p>Currency: {{ $currencySettings['code'] ?? 'USD' }}</p>
    </div>

    <!-- Overview Statistics -->
    <div class="section">
        <h2>Overview Statistics</h2>
        <div class="stats-grid">
            <div class="stats-row">
                <div class="stats-cell">
                    <div class="stat-item">
                        <div class="stat-label">Total Products</div>
                        <div class="stat-value">{{ number_format($overviewStats['total_products']) }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Active Products</div>
                        <div class="stat-value">{{ number_format($overviewStats['active_products']) }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Total Categories</div>
                        <div class="stat-value">{{ number_format($overviewStats['total_categories']) }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Active Categories</div>
                        <div class="stat-value">{{ number_format($overviewStats['active_categories']) }}</div>
                    </div>
                </div>
                <div class="stats-cell">
                    <div class="stat-item">
                        <div class="stat-label">Total Orders</div>
                        <div class="stat-value">{{ number_format($overviewStats['total_orders']) }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Paid Orders</div>
                        <div class="stat-value">{{ number_format($overviewStats['paid_orders']) }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Total Revenue</div>
                        <div class="stat-value">@currency($overviewStats['total_revenue'])</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Stock Alerts</div>
                        <div class="stat-value">{{ $overviewStats['low_stock_alerts'] + $overviewStats['out_of_stock_alerts'] }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Products -->
    <div class="section">
        <h2>Top Products</h2>
        <table>
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th>SKU</th>
                    <th>Price</th>
                    <th>Stock</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($productAnalytics['top_products'] as $product)
                <tr>
                    <td>{{ $product->name }}</td>
                    <td>{{ $product->sku }}</td>
                    <td>@currency($product->price)</td>
                    <td>{{ $product->manage_stock ? $product->stock_quantity : 'N/A' }}</td>
                    <td>
                        <span class="badge badge-{{ $product->is_active ? 'success' : 'secondary' }}">
                            {{ $product->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Category Performance -->
    <div class="section">
        <h2>Category Performance</h2>
        <table>
            <thead>
                <tr>
                    <th>Category Name</th>
                    <th>Products Count</th>
                    <th>Average Price</th>
                </tr>
            </thead>
            <tbody>
                @foreach($productAnalytics['category_performance'] as $category)
                <tr>
                    <td>{{ $category->name }}</td>
                    <td class="text-center">{{ $category->products_count }}</td>
                    <td>@currency($category->products()->avg('price') ?? 0)</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Recent High-Value Orders -->
    <div class="section">
        <h2>Recent High-Value Orders</h2>
        <table>
            <thead>
                <tr>
                    <th>Order Number</th>
                    <th>Customer</th>
                    <th>Total Amount</th>
                    <th>Status</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach($salesAnalytics['recent_orders'] as $order)
                <tr>
                    <td>{{ $order->order_number }}</td>
                    <td>{{ $order->customer->name ?? 'Guest' }}</td>
                    <td>@currency($order->total_amount)</td>
                    <td>
                        <span class="badge badge-{{ $order->payment_status === 'paid' ? 'success' : 'warning' }}">
                            {{ ucfirst($order->payment_status) }}
                        </span>
                    </td>
                    <td>{{ $order->created_at->format('Y-m-d') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Top Customers -->
    <div class="section">
        <h2>Top Customers</h2>
        <table>
            <thead>
                <tr>
                    <th>Customer Name</th>
                    <th>Email</th>
                    <th>Orders</th>
                    <th>Total Spent</th>
                </tr>
            </thead>
            <tbody>
                @foreach($salesAnalytics['top_customers'] as $customer)
                <tr>
                    <td>{{ $customer->name }}</td>
                    <td>{{ $customer->email }}</td>
                    <td class="text-center">{{ $customer->orders_count }}</td>
                    <td>@currency($customer->total_spent ?? 0)</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Inventory Alerts -->
    <div class="section">
        <h2>Inventory Alerts</h2>
        
        @if($inventoryAnalytics['low_stock_products']->count() > 0)
        <h3>Low Stock Products</h3>
        <table>
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th>Current Stock</th>
                    <th>Min Level</th>
                    <th>Price</th>
                </tr>
            </thead>
            <tbody>
                @foreach($inventoryAnalytics['low_stock_products'] as $product)
                <tr>
                    <td>{{ $product->name }}</td>
                    <td class="text-center">
                        <span class="badge badge-warning">{{ $product->stock_quantity }}</span>
                    </td>
                    <td class="text-center">{{ $product->min_stock_level }}</td>
                    <td>@currency($product->price)</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @endif

        @if($inventoryAnalytics['out_of_stock_products']->count() > 0)
        <h3>Out of Stock Products</h3>
        <table>
            <thead>
                <tr>
                    <th>Product Name</th>
                    <th>Price</th>
                    <th>Last Updated</th>
                </tr>
            </thead>
            <tbody>
                @foreach($inventoryAnalytics['out_of_stock_products'] as $product)
                <tr>
                    <td>{{ $product->name }}</td>
                    <td>@currency($product->price)</td>
                    <td>{{ $product->updated_at->format('Y-m-d H:i') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
        @endif
    </div>

    <div class="footer">
        <p>This report was generated automatically by Atrix Logistics E-commerce Analytics System</p>
        <p>For questions or support, please contact your system administrator</p>
    </div>
</body>
</html>
