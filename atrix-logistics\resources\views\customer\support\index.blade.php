@extends('layouts.customer')

@section('title', 'Support Tickets')
@section('page-title', 'Support Tickets')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <a href="{{ route('customer.support.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> New Ticket
        </a>
    </div>
@endsection

@section('content')
    <!-- Support Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card">
                <div class="d-flex justify-content-between text-dark align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $tickets->total() }}</h3>
                        <p class="mb-0">Total Tickets</p>
                    </div>
                    <div>
                        <i class="fas fa-ticket-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $tickets->whereIn('status', ['open', 'in_progress'])->count() }}</h3>
                        <p class="mb-0">Active Tickets</p>
                    </div>
                    <div>
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $tickets->where('status', 'resolved')->count() }}</h3>
                        <p class="mb-0">Resolved</p>
                    </div>
                    <div>
                        <i class="fas fa-check-circle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="mb-1">{{ $tickets->where('priority', 'high')->count() + $tickets->where('priority', 'urgent')->count() }}</h3>
                        <p class="mb-0">High Priority</p>
                    </div>
                    <div>
                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="{{ route('customer.support.index') }}" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="Ticket number, subject...">
                </div>
                
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ request('status') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="priority" class="form-label">Priority</label>
                    <select class="form-select" id="priority" name="priority">
                        <option value="">All Priorities</option>
                        @foreach($priorities as $value => $label)
                            <option value="{{ $value }}" {{ request('priority') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        @foreach($categories as $value => $label)
                            <option value="{{ $value }}" {{ request('category') === $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <div class="col-md-1">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="{{ route('customer.support.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Support Tickets -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-headset me-2"></i>
                Your Support Tickets ({{ $tickets->total() }})
            </h5>
        </div>
        
        <div class="card-body">
            @if($tickets->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Subject</th>
                                <th>Category</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($tickets as $ticket)
                                <tr>
                                    <td>
                                        <strong>{{ $ticket->ticket_number }}</strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ Str::limit($ticket->subject, 40) }}</strong>
                                            @if($ticket->assignedTo)
                                                <br><small class="text-muted">
                                                    Assigned to: {{ $ticket->assignedTo->name }}
                                                </small>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ ucwords($ticket->category) }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $ticket->priority_badge_color }}">
                                            {{ $ticket->formatted_priority }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ $ticket->status_badge_color }}">
                                            {{ $ticket->formatted_status }}
                                        </span>
                                    </td>
                                    <td>
                                        {{ $ticket->created_at->format('M d, Y') }}
                                        <br><small class="text-muted">{{ $ticket->created_at->diffForHumans() }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('customer.support.show', $ticket) }}" 
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if(in_array($ticket->status, ['open', 'waiting_customer']))
                                                <a href="{{ route('customer.support.edit', $ticket) }}" 
                                                   class="btn btn-sm btn-outline-secondary" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            @endif
                                            @if($ticket->status === 'resolved')
                                                <form method="POST" action="{{ route('customer.support.close', $ticket) }}" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-success" 
                                                            title="Close Ticket" onclick="return confirm('Close this ticket?')">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            @endif
                                            @if($ticket->status === 'closed')
                                                <form method="POST" action="{{ route('customer.support.reopen', $ticket) }}" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-outline-warning" 
                                                            title="Reopen Ticket" onclick="return confirm('Reopen this ticket?')">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        Showing {{ $tickets->firstItem() }} to {{ $tickets->lastItem() }} of {{ $tickets->total() }} tickets
                    </div>
                    {{ $tickets->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-headset fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No support tickets found</h5>
                    <p class="text-muted">You haven't created any support tickets yet. Need help? Create your first ticket.</p>
                    <a href="{{ route('customer.support.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i> Create Support Ticket
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Quick Help -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-lightbulb me-2"></i>
                        Quick Help
                    </h6>
                    <div class="row">
                        <div class="col-md-3">
                            <h6>General Questions</h6>
                            <p class="small text-muted">For general inquiries about our services, shipping rates, or account questions.</p>
                        </div>
                        <div class="col-md-3">
                            <h6>Technical Issues</h6>
                            <p class="small text-muted">Having trouble with our website, mobile app, or tracking system? Let us know.</p>
                        </div>
                        <div class="col-md-3">
                            <h6>Billing Support</h6>
                            <p class="small text-muted">Questions about invoices, payments, or billing discrepancies.</p>
                        </div>
                        <div class="col-md-3">
                            <h6>Shipping Issues</h6>
                            <p class="small text-muted">Problems with deliveries, damaged packages, or tracking information.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .stats-card {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
        transition: transform 0.2s;
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
    }
    
    .stats-card.success {
        border-left-color: #28a745;
    }
    
    .stats-card.warning {
        border-left-color: #ffc107;
    }
    
    .stats-card.info {
        border-left-color: #17a2b8;
    }
    
    .stats-card.danger {
        border-left-color: #dc3545;
    }
</style>
@endpush
