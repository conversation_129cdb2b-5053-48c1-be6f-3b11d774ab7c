<?php

namespace Tests\Feature;

use App\Models\Cart;
use App\Models\Category;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\UserAddress;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CheckoutTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Product $product;
    protected Category $category;
    protected UserAddress $shippingAddress;
    protected UserAddress $billingAddress;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test user
        $this->user = User::factory()->create([
            'role' => 'customer',
            'email_verified_at' => now(),
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
        ]);

        // Create test category
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::factory()->create([
            'category_id' => $this->category->id,
            'name' => 'Test Product',
            'price' => 99.99,
            'sale_price' => null, // Ensure no sale price
            'stock_quantity' => 10,
            'manage_stock' => true,
            'is_active' => true,
        ]);

        // Create test addresses
        $this->shippingAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'shipping',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address_line_1' => '123 Main St',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10001',
            'country' => 'USA',
            'is_default' => true,
        ]);

        $this->billingAddress = UserAddress::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'billing',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'address_line_1' => '456 Oak Ave',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10002',
            'country' => 'USA',
            'is_default' => true,
        ]);
    }

    /** @test */
    public function user_can_view_checkout_page_with_items_in_cart()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        $response = $this->get('/checkout');

        $response->assertStatus(200)
            ->assertViewIs('frontend.checkout.index')
            ->assertViewHas(['cart', 'addresses', 'defaultShipping', 'defaultBilling'])
            ->assertSee($this->product->name)
            ->assertSee('$199.98'); // Total for 2 items
    }

    /** @test */
    public function user_cannot_access_checkout_with_empty_cart()
    {
        $this->actingAs($this->user);

        $response = $this->get('/checkout');

        $response->assertRedirect('/cart')
            ->assertSessionHas('error', 'Your cart is empty.');
    }

    /** @test */
    public function user_cannot_access_checkout_without_authentication()
    {
        $response = $this->get('/checkout');

        $response->assertRedirect('/customer/login');
    }

    /** @test */
    public function user_can_place_order_with_manual_payment()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 2);

        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual',
            'notes' => 'Please handle with care',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully.',
            ]);

        // Verify order was created
        $order = Order::where('customer_id', $this->user->id)->first();
        $this->assertNotNull($order);
        $this->assertEquals('pending', $order->status);
        $this->assertEquals('pending', $order->payment_status);
        $this->assertEquals('manual', $order->payment_method);
        $this->assertEquals(199.98, $order->total_amount);
        $this->assertEquals('Please handle with care', $order->notes);

        // Verify order items
        $this->assertEquals(1, $order->items()->count());
        $orderItem = $order->items()->first();
        $this->assertEquals($this->product->id, $orderItem->product_id);
        $this->assertEquals(2, $orderItem->quantity);
        $this->assertEquals(99.99, $orderItem->unit_price);

        // Verify cart was cleared
        $cart->refresh();
        $this->assertTrue($cart->isEmpty());

        // Verify stock was decremented
        $this->product->refresh();
        $this->assertEquals(8, $this->product->stock_quantity); // 10 - 2
    }

    /** @test */
    public function user_can_place_order_with_paypal_payment()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 1);

        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'paypal',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully.',
            ]);

        // Verify payment response includes PayPal redirect
        $responseData = $response->json();
        $this->assertEquals('paypal', $responseData['payment_response']['type']);
        $this->assertStringContainsString('orders', $responseData['payment_response']['redirect']);

        // Verify order was created with correct payment method
        $order = Order::where('customer_id', $this->user->id)->first();
        $this->assertEquals('paypal', $order->payment_method);
    }

    /** @test */
    public function user_can_place_order_with_stripe_payment()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 1);

        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'stripe',
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Order created successfully.',
            ]);

        // Verify payment response includes Stripe redirect
        $responseData = $response->json();
        $this->assertEquals('stripe', $responseData['payment_response']['type']);
        $this->assertStringContainsString('orders', $responseData['payment_response']['redirect']);

        // Verify order was created with correct payment method
        $order = Order::where('customer_id', $this->user->id)->first();
        $this->assertEquals('stripe', $order->payment_method);
    }

    /** @test */
    public function checkout_fails_with_invalid_address()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 1);

        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => 999, // Non-existent address
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed.',
            ]);
    }

    /** @test */
    public function checkout_fails_with_invalid_payment_method()
    {
        $this->actingAs($this->user);

        // Add item to cart
        $cart = Cart::getCurrent();
        $cart->addProduct($this->product, 1);

        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'invalid_method',
        ]);

        $response->assertStatus(422)
            ->assertJson([
                'success' => false,
                'message' => 'Validation failed.',
            ]);
    }

    /** @test */
    public function checkout_fails_when_user_not_authenticated()
    {
        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual',
        ]);

        $response->assertStatus(401)
            ->assertJson([
                'success' => false,
                'message' => 'Please log in to place an order.',
            ]);
    }

    /** @test */
    public function checkout_fails_with_empty_cart()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/checkout/process', [
            'shipping_address_id' => $this->shippingAddress->id,
            'billing_address_id' => $this->billingAddress->id,
            'payment_method' => 'manual',
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Your cart is empty.',
            ]);
    }

    /** @test */
    public function user_can_view_order_confirmation()
    {
        $this->actingAs($this->user);

        // Create an order
        $order = Order::factory()->create([
            'customer_id' => $this->user->id,
            'order_number' => 'ORD20241201001',
            'total_amount' => 99.99,
            'payment_method' => 'manual',
        ]);

        $response = $this->get("/checkout/confirmation/{$order->id}");

        $response->assertStatus(200)
            ->assertViewIs('frontend.checkout.confirmation')
            ->assertViewHas('order')
            ->assertSee($order->order_number)
            ->assertSee('$99.99');
    }

    /** @test */
    public function user_cannot_view_other_users_order_confirmation()
    {
        $this->actingAs($this->user);

        // Create order for different user
        $otherUser = User::factory()->create(['role' => 'customer']);
        $order = Order::factory()->create([
            'customer_id' => $otherUser->id,
        ]);

        $response = $this->get("/checkout/confirmation/{$order->id}");

        $response->assertStatus(404);
    }
}
