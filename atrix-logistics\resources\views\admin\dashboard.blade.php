@extends('layouts.admin')

@section('title', 'Admin Dashboard')
@section('page-title', 'Dashboard')

@push('styles')
<style>
    .stats-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .card {
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        transition: transform 0.2s ease-in-out;
    }

    .card:hover {
        transform: translateY(-1px);
    }

    .bg-gradient-primary {
        background: linear-gradient(45deg, #4e73df, #224abe);
    }

    .bg-gradient-success {
        background: linear-gradient(45deg, #1cc88a, #13855c);
    }

    .bg-gradient-info {
        background: linear-gradient(45deg, #36b9cc, #258391);
    }

    .bg-gradient-warning {
        background: linear-gradient(45deg, #f6c23e, #dda20a);
    }

    .progress {
        border-radius: 10px;
    }

    .progress-bar {
        border-radius: 10px;
    }

    .badge {
        font-size: 0.75rem;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #5a5c69;
        font-size: 0.85rem;
    }

    .table td {
        font-size: 0.85rem;
        vertical-align: middle;
    }

    .stat-card {
        border-left: 4px solid #4e73df;
    }

    .stat-card-success {
        border-left: 4px solid #1cc88a;
    }

    .stat-card-danger {
        border-left: 4px solid #e74a3b;
    }

    .text-xs {
        font-size: 0.7rem;
    }

    .font-weight-bold {
        font-weight: 700;
    }

    .opacity-75 {
        opacity: 0.75;
    }
</style>
@endpush

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.parcels.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> New Parcel
        </a>
        <a href="{{ route('admin.products.create') }}" class="btn btn-success">
            <i class="fas fa-box me-1"></i> Add Product
        </a>
        <a href="{{ route('tracking.index') }}" class="btn btn-outline-secondary" target="_blank">
            <i class="fas fa-external-link-alt me-1"></i> View Website
        </a>

        <!-- Notifications Dropdown -->
        @if($ecommerceStats['low_stock'] + $ecommerceStats['out_of_stock'] > 0)
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-warning dropdown-toggle position-relative" data-bs-toggle="dropdown">
                    <i class="fas fa-bell me-1"></i> Alerts
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        {{ $ecommerceStats['low_stock'] + $ecommerceStats['out_of_stock'] }}
                    </span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" style="min-width: 300px;">
                    <li><h6 class="dropdown-header">Inventory Alerts</h6></li>
                    @if($ecommerceStats['out_of_stock'] > 0)
                        <li>
                            <a class="dropdown-item text-danger" href="{{ route('admin.products.index', ['stock_status' => 'out_of_stock']) }}">
                                <i class="fas fa-times-circle me-2"></i>
                                {{ $ecommerceStats['out_of_stock'] }} products out of stock
                            </a>
                        </li>
                    @endif
                    @if($ecommerceStats['low_stock'] > 0)
                        <li>
                            <a class="dropdown-item text-warning" href="{{ route('admin.products.index', ['stock_status' => 'low_stock']) }}">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                {{ $ecommerceStats['low_stock'] }} products low in stock
                            </a>
                        </li>
                    @endif
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{{ route('admin.products.index') }}">
                            <i class="fas fa-list me-2"></i>
                            View All Products
                        </a>
                    </li>
                </ul>
            </div>
        @endif
    </div>
@endsection

@section('content')
    <!-- Order Management Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-receipt me-2"></i>
                        Order Management Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Total Orders -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($orderStats['total_orders']) }}</h4>
                                            <p class="mb-0">Total Orders</p>
                                            <small class="opacity-75">{{ $orderStats['orders_today'] }} today</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-receipt fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pending Orders -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($orderStats['pending_orders']) }}</h4>
                                            <p class="mb-0">Pending Orders</p>
                                            <small class="opacity-75">Need attention</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-clock fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Total Revenue -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">@currency($orderStats['total_revenue'])</h4>
                                            <p class="mb-0">Total Revenue</p>
                                            <small class="opacity-75">Paid orders</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Average Order Value -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">@currency($orderStats['average_order_value'])</h4>
                                            <p class="mb-0">Avg Order Value</p>
                                            <small class="opacity-75">Per order</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- E-commerce Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        E-commerce Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Products Stats -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($ecommerceStats['total_products']) }}</h4>
                                            <p class="mb-0">Total Products</p>
                                            <small class="opacity-75">{{ $ecommerceStats['active_products'] }} active</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-box fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Categories Stats -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ number_format($ecommerceStats['total_categories']) }}</h4>
                                            <p class="mb-0">Categories</p>
                                            <small class="opacity-75">{{ $ecommerceStats['active_categories'] }} active</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-folder fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Inventory Value -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">@currency($ecommerceStats['total_inventory_value'])</h4>
                                            <p class="mb-0">Inventory Value</p>
                                            <small class="opacity-75">Cost basis</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-warehouse fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Stock Alerts -->
                        <div class="col-xl-3 col-md-6 mb-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h4 class="mb-0">{{ $ecommerceStats['low_stock'] + $ecommerceStats['out_of_stock'] }}</h4>
                                            <p class="mb-0">Stock Alerts</p>
                                            <small class="opacity-75">{{ $ecommerceStats['out_of_stock'] }} out of stock</small>
                                        </div>
                                        <div>
                                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logistics Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Total Parcels</div>
                            <div class="h5 mb-0 font-weight-bold">{{ number_format($logisticsStats['total_parcels']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Pending</div>
                            <div class="h5 mb-0 font-weight-bold">{{ number_format($logisticsStats['pending_parcels']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">In Transit</div>
                            <div class="h5 mb-0 font-weight-bold">{{ number_format($logisticsStats['in_transit_parcels']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card stat-card-danger">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1">Delivered</div>
                            <div class="h5 mb-0 font-weight-bold">{{ number_format($logisticsStats['delivered_parcels']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats Row -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 text-primary">Total Customers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($logisticsStats['total_customers']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 text-info">Active Carriers</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($logisticsStats['total_carriers']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 text-warning">Pending Quotes</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ number_format($logisticsStats['pending_quotes']) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-quote-left fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-uppercase mb-1 text-success">Monthly Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@currency($logisticsStats['revenue_this_month'])</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Parcel Analytics Quick View -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Parcel Analytics (Last 30 Days)
                    </h5>
                    <a href="{{ route('admin.analytics.parcels') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-chart-bar me-1"></i> View Full Analytics
                    </a>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ number_format($logisticsStats['delivery_rate'] ?? 0, 1) }}%</h4>
                                <p class="text-muted mb-0">Delivery Rate</p>
                                <small class="text-muted">Success rate</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border-end">
                                <h4 class="text-success mb-1">@currency($logisticsStats['avg_parcel_value'] ?? 0)</h4>
                                <p class="text-muted mb-0">Avg Parcel Value</p>
                                <small class="text-muted">Per parcel</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <div class="border-end">
                                <h4 class="text-info mb-1">{{ number_format($logisticsStats['avg_delivery_days'] ?? 0, 1) }}</h4>
                                <p class="text-muted mb-0">Avg Delivery Time</p>
                                <small class="text-muted">Days</small>
                            </div>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning mb-1">{{ number_format($logisticsStats['exception_rate'] ?? 0, 1) }}%</h4>
                            <p class="text-muted mb-0">Exception Rate</p>
                            <small class="text-muted">Issues</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Parcels -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Parcels</h6>
                    <a href="{{ route('admin.parcels.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($recent_parcels->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Tracking #</th>
                                        <th>Customer</th>
                                        <th>Status</th>
                                        <th>Carrier</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recent_parcels as $parcel)
                                        <tr>
                                            <td>
                                                <strong>{{ $parcel->tracking_number }}</strong><br>
                                                <small class="text-muted">{{ Str::limit($parcel->description, 30) }}</small>
                                            </td>
                                            <td>
                                                {{ $parcel->recipient_name }}<br>
                                                <small class="text-muted">{{ $parcel->recipient_city }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $parcel->getStatusBadgeColor() }}">
                                                    {{ $parcel->getFormattedStatus() }}
                                                </span>
                                            </td>
                                            <td>{{ $parcel->carrier->name }}</td>
                                            <td>{{ $parcel->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <a href="{{ route('admin.parcels.show', $parcel) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No parcels found</p>
                            <a href="{{ route('admin.parcels.create') }}" class="btn btn-primary">Create First Parcel</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Status Distribution Chart -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Parcel Status Distribution</h6>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="100" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-receipt me-2"></i>
                        Recent Orders
                    </h6>
                    <a href="{{ route('admin.orders.index') }}" class="btn btn-sm btn-outline-primary">View All Orders</a>
                </div>
                <div class="card-body">
                    @php
                        $recentOrders = \App\Models\Order::with(['customer'])
                                                        ->orderBy('created_at', 'desc')
                                                        ->limit(5)
                                                        ->get();
                    @endphp

                    @if($recentOrders->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Order #</th>
                                        <th>Customer</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentOrders as $order)
                                        <tr>
                                            <td>
                                                <strong>{{ $order->order_number }}</strong>
                                            </td>
                                            <td>
                                                {{ $order->customer_name }}
                                                <br><small class="text-muted">{{ $order->customer_email }}</small>
                                            </td>
                                            <td>
                                                <strong>@currency($order->total_amount)</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $order->status_badge_color }}">
                                                    {{ $order->formatted_status }}
                                                </span>
                                            </td>
                                            <td>
                                                {{ $order->created_at->format('M d, Y') }}
                                                <br><small class="text-muted">{{ $order->created_at->diffForHumans() }}</small>
                                            </td>
                                            <td>
                                                <a href="{{ route('admin.orders.show', $order) }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No orders found</p>
                            <a href="{{ route('admin.orders.create') }}" class="btn btn-primary">Create First Order</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Order Status Distribution -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        Order Status Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="orderStatusChart" width="100" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Customers -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Customers</h6>
                </div>
                <div class="card-body">
                    @if($recent_customers->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>Location</th>
                                        <th>Joined</th>
                                        <th>Parcels</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recent_customers as $customer)
                                        <tr>
                                            <td>{{ $customer->name }}</td>
                                            <td>{{ $customer->email }}</td>
                                            <td>{{ $customer->phone ?? 'N/A' }}</td>
                                            <td>{{ $customer->city }}, {{ $customer->state }}</td>
                                            <td>{{ $customer->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <span class="badge bg-info">{{ $customer->parcels()->count() }}</span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No customers found</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- E-commerce Analytics Section -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-chart-line me-2"></i>
                E-commerce Analytics
            </h4>
        </div>
    </div>

    <!-- Product Performance & Inventory Alerts -->
    <div class="row mb-4">
        <!-- Top Products -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-star me-2"></i>
                        Recent Products
                    </h6>
                    <a href="{{ route('admin.products.index') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    @if($ecommerceAnalytics['recent_products']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Added</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ecommerceAnalytics['recent_products'] as $product)
                                        <tr>
                                            <td>
                                                <strong>{{ Str::limit($product->name, 25) }}</strong>
                                            </td>
                                            <td>@currency($product->price)</td>
                                            <td>
                                                @if($product->is_active)
                                                    <span class="badge bg-success">Active</span>
                                                @else
                                                    <span class="badge bg-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>{{ $product->created_at->format('M d') }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No products found</p>
                            <a href="{{ route('admin.products.create') }}" class="btn btn-primary">Add First Product</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Inventory Alerts -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Inventory Alerts
                    </h6>
                </div>
                <div class="card-body">
                    @if($ecommerceAnalytics['inventory_alerts']['low_stock_products']->count() > 0 || $ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->count() > 0)
                        <!-- Low Stock Products -->
                        @if($ecommerceAnalytics['inventory_alerts']['low_stock_products']->count() > 0)
                            <h6 class="text-warning mb-2">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                Low Stock ({{ $ecommerceAnalytics['inventory_alerts']['low_stock_products']->count() }})
                            </h6>
                            <div class="mb-3">
                                @foreach($ecommerceAnalytics['inventory_alerts']['low_stock_products']->take(3) as $product)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>{{ Str::limit($product->name, 20) }}</strong>
                                            @if($product->category)
                                                <br><small class="text-muted">{{ $product->category->name }}</small>
                                            @endif
                                        </div>
                                        <span class="badge bg-warning">{{ $product->stock_quantity }} left</span>
                                    </div>
                                @endforeach
                                @if($ecommerceAnalytics['inventory_alerts']['low_stock_products']->count() > 3)
                                    <small class="text-muted">
                                        +{{ $ecommerceAnalytics['inventory_alerts']['low_stock_products']->count() - 3 }} more
                                    </small>
                                @endif
                            </div>
                        @endif

                        <!-- Out of Stock Products -->
                        @if($ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->count() > 0)
                            <h6 class="text-danger mb-2">
                                <i class="fas fa-times-circle me-1"></i>
                                Out of Stock ({{ $ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->count() }})
                            </h6>
                            <div>
                                @foreach($ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->take(3) as $product)
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong>{{ Str::limit($product->name, 20) }}</strong>
                                            @if($product->category)
                                                <br><small class="text-muted">{{ $product->category->name }}</small>
                                            @endif
                                        </div>
                                        <span class="badge bg-danger">Out of Stock</span>
                                    </div>
                                @endforeach
                                @if($ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->count() > 3)
                                    <small class="text-muted">
                                        +{{ $ecommerceAnalytics['inventory_alerts']['out_of_stock_products']->count() - 3 }} more
                                    </small>
                                @endif
                            </div>
                        @endif
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-success">All products are well stocked!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Interactive Charts -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        Product Status Distribution
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="productStatusChart" width="100" height="100"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-bar me-2"></i>
                        Inventory Overview
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="inventoryChart" width="100" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Performance -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-folder me-2"></i>
                        Category Performance
                    </h6>
                    <a href="{{ route('admin.categories.index') }}" class="btn btn-sm btn-outline-primary">Manage Categories</a>
                </div>
                <div class="card-body">
                    @if($ecommerceAnalytics['category_stats']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Products</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($ecommerceAnalytics['category_stats'] as $category)
                                        <tr>
                                            <td>
                                                <strong>{{ $category->name }}</strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $category->products_count }} products</span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    @php
                                                        $maxProducts = $ecommerceAnalytics['category_stats']->max('products_count');
                                                        $percentage = $maxProducts > 0 ? ($category->products_count / $maxProducts) * 100 : 0;
                                                    @endphp
                                                    <div class="progress-bar bg-primary"
                                                         role="progressbar"
                                                         style="width: {{ $percentage }}%"
                                                         aria-valuenow="{{ $percentage }}"
                                                         aria-valuemin="0"
                                                         aria-valuemax="100">
                                                        {{ number_format($percentage, 1) }}%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No categories found</p>
                            <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">Create First Category</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Communications Overview -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-envelope me-2"></i>
                        Communications
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-0 font-weight-bold text-warning">{{ $communicationStats['new_contacts'] }}</div>
                                <div class="text-xs text-uppercase">New Messages</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-0 font-weight-bold text-success">{{ $communicationStats['active_subscribers'] }}</div>
                                <div class="text-xs text-uppercase">Subscribers</div>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.communications.contacts.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-inbox me-2"></i>
                            View Messages
                            @if($communicationStats['new_contacts'] > 0)
                                <span class="badge bg-danger ms-1">{{ $communicationStats['new_contacts'] }}</span>
                            @endif
                        </a>
                        <a href="{{ route('admin.communications.newsletter.index') }}" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-newspaper me-2"></i>
                            Manage Newsletter
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.products.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Add New Product
                        </a>
                        <a href="{{ route('admin.categories.create') }}" class="btn btn-outline-primary">
                            <i class="fas fa-folder-plus me-2"></i>
                            Add New Category
                        </a>
                        <a href="{{ route('admin.products.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i>
                            Manage Products
                        </a>
                        <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-sitemap me-2"></i>
                            Manage Categories
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    // Status Distribution Chart
    const ctx = document.getElementById('statusChart').getContext('2d');
    const statusData = @json($status_distribution);

    const chart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(statusData).map(status => status.replace('_', ' ').toUpperCase()),
            datasets: [{
                data: Object.values(statusData),
                backgroundColor: [
                    '#6c757d', // pending
                    '#17a2b8', // picked_up
                    '#007bff', // in_transit
                    '#ffc107', // out_for_delivery
                    '#28a745', // delivered
                    '#dc3545', // exception
                    '#6f42c1'  // returned
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Order Status Distribution Chart
    const orderStatusCtx = document.getElementById('orderStatusChart').getContext('2d');
    const orderStatusChart = new Chart(orderStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
            datasets: [{
                data: [
                    {{ $orderStats['pending_orders'] }},
                    {{ $orderStats['processing_orders'] }},
                    {{ $orderStats['shipped_orders'] }},
                    {{ $orderStats['delivered_orders'] }},
                    {{ $orderStats['cancelled_orders'] }}
                ],
                backgroundColor: [
                    '#ffc107', // Pending - Yellow
                    '#17a2b8', // Processing - Cyan
                    '#007bff', // Shipped - Blue
                    '#28a745', // Delivered - Green
                    '#dc3545'  // Cancelled - Red
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Product Status Distribution Chart
    const productStatusCtx = document.getElementById('productStatusChart').getContext('2d');
    const productStatusChart = new Chart(productStatusCtx, {
        type: 'doughnut',
        data: {
            labels: ['Active Products', 'Inactive Products', 'Featured Products'],
            datasets: [{
                data: [
                    {{ $ecommerceStats['active_products'] }},
                    {{ $ecommerceStats['total_products'] - $ecommerceStats['active_products'] }},
                    {{ $ecommerceStats['featured_products'] }}
                ],
                backgroundColor: [
                    '#28a745', // Active - Green
                    '#6c757d', // Inactive - Gray
                    '#ffc107'  // Featured - Yellow
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Inventory Overview Chart
    const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
    const inventoryChart = new Chart(inventoryCtx, {
        type: 'bar',
        data: {
            labels: ['In Stock', 'Low Stock', 'Out of Stock'],
            datasets: [{
                label: 'Products',
                data: [
                    {{ $ecommerceStats['active_products'] - $ecommerceStats['low_stock'] - $ecommerceStats['out_of_stock'] }},
                    {{ $ecommerceStats['low_stock'] }},
                    {{ $ecommerceStats['out_of_stock'] }}
                ],
                backgroundColor: [
                    '#28a745', // In Stock - Green
                    '#ffc107', // Low Stock - Yellow
                    '#dc3545'  // Out of Stock - Red
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Auto-refresh dashboard data every 5 minutes
    setInterval(function() {
        refreshDashboardData();
    }, 300000); // 5 minutes

    // Function to refresh dashboard data
    function refreshDashboardData() {
        fetch('{{ route("admin.analytics-data") }}')
            .then(response => response.json())
            .then(data => {
                // Update product status chart
                productStatusChart.data.datasets[0].data = [
                    data.ecommerce_stats.active_products,
                    data.ecommerce_stats.total_products - data.ecommerce_stats.active_products,
                    data.ecommerce_stats.featured_products
                ];
                productStatusChart.update();

                // Update inventory chart
                inventoryChart.data.datasets[0].data = [
                    data.ecommerce_stats.active_products - data.low_stock_count - data.out_of_stock_count,
                    data.low_stock_count,
                    data.out_of_stock_count
                ];
                inventoryChart.update();

                console.log('Dashboard data refreshed at:', data.timestamp);
            })
            .catch(error => {
                console.error('Error refreshing dashboard data:', error);
            });
    }

    // Add tooltips to all elements with data-bs-toggle="tooltip"
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add smooth animations to cards on hover
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.2s ease-in-out';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
@endpush
