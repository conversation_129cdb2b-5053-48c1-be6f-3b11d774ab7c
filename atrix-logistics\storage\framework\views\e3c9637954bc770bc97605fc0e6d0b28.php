<!-- Footer -->
<footer class="bg-gray-900 text-white">
    <!-- Main Footer Content -->
    <div class="container mx-auto px-4 py-16">
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
            <!-- Company Info -->
            <div class="space-y-6">
                <div class="flex items-center">
                    <?php if(isset($siteSettings['site_logo']) && $siteSettings['site_logo']): ?>
                        <img src="<?php echo e(Storage::url($siteSettings['site_logo'])); ?>" alt="<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>" class="h-10 w-auto filter brightness-0 invert">
                    <?php else: ?>
                        <div class="flex items-center">
                            <div class="bg-green-600 text-white p-2 rounded-lg mr-3">
                                <i class="fas fa-shipping-fast text-lg"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold"><?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?></h3>
                                <?php if(isset($siteSettings['site_tagline'])): ?>
                                <p class="text-xs text-gray-400"><?php echo e($siteSettings['site_tagline']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <p class="text-gray-300 leading-relaxed">
                    <?php echo e($siteSettings['company_description'] ?? 'Professional logistics and freight solutions provider specializing in global shipping, warehousing, and supply chain management.'); ?>

                </p>
                
                <!-- Contact Info -->
                <div class="space-y-3">
                    <?php if(isset($siteSettings['contact_address'])): ?>
                    <div class="flex items-start">
                        <i class="fas fa-map-marker-alt text-green-500 mt-1 mr-3"></i>
                        <span class="text-gray-300"><?php echo e($siteSettings['contact_address']); ?></span>
                    </div>
                    <?php endif; ?>

                    <?php if(isset($siteSettings['contact_phone'])): ?>
                    <div class="flex items-center">
                        <i class="fas fa-phone text-green-500 mr-3"></i>
                        <a href="tel:<?php echo e($siteSettings['contact_phone']); ?>" class="text-gray-300 hover:text-green-400 transition-colors">
                            <?php echo e($siteSettings['contact_phone']); ?>

                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if(isset($siteSettings['contact_email'])): ?>
                    <div class="flex items-center">
                        <i class="fas fa-envelope text-green-500 mr-3"></i>
                        <a href="mailto:<?php echo e($siteSettings['contact_email']); ?>" class="text-gray-300 hover:text-green-400 transition-colors">
                            <?php echo e($siteSettings['contact_email']); ?>

                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-white">Quick Links</h3>
                <ul class="space-y-3">
                    <li><a href="<?php echo e(route('home')); ?>" class="text-gray-300 hover:text-green-400 transition-colors">Home</a></li>
                    <li><a href="<?php echo e(route('about')); ?>" class="text-gray-300 hover:text-green-400 transition-colors">About Us</a></li>
                    <li><a href="<?php echo e(route('products.index')); ?>" class="text-gray-300 hover:text-green-400 transition-colors">Products</a></li>
                    <li><a href="<?php echo e(route('tracking.index')); ?>" class="text-gray-300 hover:text-green-400 transition-colors">Track Shipment</a></li>
                    <li><a href="<?php echo e(route('blog.index')); ?>" class="text-gray-300 hover:text-green-400 transition-colors">Blog</a></li>
                    <li><a href="<?php echo e(route('contact')); ?>" class="text-gray-300 hover:text-green-400 transition-colors">Contact</a></li>
                </ul>
            </div>

            <!-- Services -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-white">Our Services</h3>
                <ul class="space-y-3">
                    <li>
                        <a href="<?php echo e(route('services')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-truck text-green-500 mr-3"></i>
                            Freight & Logistics
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('services')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-warehouse text-green-500 mr-3"></i>
                            Warehousing
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('services')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-globe text-green-500 mr-3"></i>
                            Global Shipping
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('services')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-shield-alt text-green-500 mr-3"></i>
                            Supply Chain
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('services')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-plane text-green-500 mr-3"></i>
                            Air Freight
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo e(route('services')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                            <i class="fas fa-bolt text-green-500 mr-3"></i>
                            Express Delivery
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Products Categories -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-white">Products for Sale</h3>
                <ul class="space-y-3">
                    <?php
                        $footerCategories = \App\Models\Category::footerFeatured()
                            ->orderBy('sort_order')
                            ->orderBy('name')
                            ->limit(6)
                            ->get();
                    ?>

                    <?php $__empty_1 = true; $__currentLoopData = $footerCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <li>
                            <a href="<?php echo e(route('categories.show', $category->slug)); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <?php if($category->footer_icon): ?>
                                    <i class="<?php echo e($category->footer_icon); ?> mr-3"></i>
                                <?php elseif($category->icon): ?>
                                    <i class="<?php echo e($category->icon); ?> mr-3"></i>
                                <?php else: ?>
                                    <i class="fas fa-cube text-blue-500 mr-3"></i>
                                <?php endif; ?>
                                <?php echo e($category->footer_text ?: $category->name); ?>

                            </a>
                        </li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <!-- Fallback to hardcoded categories if no footer featured categories exist -->
                        <li>
                            <a href="<?php echo e(route('categories.show', 'shipping-containers')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-cube text-blue-500 mr-3"></i>
                                Shipping Containers
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('categories.show', 'cardboard-boxes')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-box text-orange-500 mr-3"></i>
                                CardBoxes
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('categories.show', 'steel-products')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-industry text-gray-500 mr-3"></i>
                                Steel Products
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('categories.show', 'spare-parts')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-cog text-purple-500 mr-3"></i>
                                Spare Parts
                            </a>
                        </li>
                        <li>
                            <a href="<?php echo e(route('categories.show', 'packaging-materials')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors">
                                <i class="fas fa-tape text-yellow-500 mr-3"></i>
                                Packaging Materials
                            </a>
                        </li>
                    <?php endif; ?>

                    <li>
                        <a href="<?php echo e(route('products.index')); ?>" class="flex items-center text-gray-300 hover:text-green-400 transition-colors font-semibold">
                            <i class="fas fa-th-large text-green-500 mr-3"></i>
                            View All Products
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Newsletter & Social -->
            <div class="space-y-6">
                <h3 class="text-lg font-semibold text-white">Stay Connected</h3>
                <p class="text-secondary-300">Subscribe to our newsletter for updates and industry insights.</p>
                
                <!-- Newsletter Form -->
                <form action="<?php echo e(route('newsletter.subscribe')); ?>" method="POST" class="space-y-3" id="newsletter-form">
                    <?php echo csrf_field(); ?>
                    <div class="flex">
                        <input type="email" name="email" placeholder="Your email address" required
                               class="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-l-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500 transition-colors">
                        <button type="submit" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-r-lg transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div id="newsletter-message" class="text-sm hidden"></div>
                </form>
                
                <!-- Social Media Links -->
                <div class="space-y-4">
                    <h4 class="font-medium text-white">Follow Us</h4>
                    <div class="flex space-x-4">
                        <?php if(isset($siteSettings['social_facebook'])): ?>
                        <a href="<?php echo e($siteSettings['social_facebook']); ?>" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <?php endif; ?>

                        <?php if(isset($siteSettings['social_twitter'])): ?>
                        <a href="<?php echo e($siteSettings['social_twitter']); ?>" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <?php endif; ?>

                        <?php if(isset($siteSettings['social_linkedin'])): ?>
                        <a href="<?php echo e($siteSettings['social_linkedin']); ?>" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <?php endif; ?>

                        <?php if(isset($siteSettings['social_instagram'])): ?>
                        <a href="<?php echo e($siteSettings['social_instagram']); ?>" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <?php endif; ?>

                        <?php if(isset($siteSettings['social_youtube'])): ?>
                        <a href="<?php echo e($siteSettings['social_youtube']); ?>" target="_blank"
                           class="bg-gray-800 hover:bg-green-600 p-3 rounded-lg transition-colors">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Footer -->
    <div class="border-t border-gray-800">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-gray-400 text-sm">
                    © <?php echo e(date('Y')); ?> <?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>. All rights reserved.
                </div>

                <div class="flex items-center space-x-6 text-sm">
                    <a href="#" class="text-gray-400 hover:text-green-400 transition-colors">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-green-400 transition-colors">Terms of Service</a>
                    <a href="#" class="text-gray-400 hover:text-green-400 transition-colors">Cookie Policy</a>
                </div>

                <!-- Certifications/Trust Badges -->
                <div class="flex items-center space-x-4">
                    <?php if(isset($siteSettings['ssl_badge']) && $siteSettings['ssl_badge']): ?>
                    <div class="flex items-center text-xs text-gray-400">
                        <i class="fas fa-lock mr-1 text-green-500"></i>
                        SSL Secured
                    </div>
                    <?php endif; ?>

                    <?php if(isset($siteSettings['iso_certified']) && $siteSettings['iso_certified']): ?>
                    <div class="flex items-center text-xs text-gray-400">
                        <i class="fas fa-certificate mr-1 text-green-500"></i>
                        ISO Certified
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Quote Modal -->
<div id="quote-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-800">Get a Quote</h3>
                <button onclick="closeQuoteModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form action="#" method="POST" class="space-y-4">
                <?php echo csrf_field(); ?>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input type="text" name="first_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition-colors">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input type="text" name="last_name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition-colors">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" name="email" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition-colors">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input type="tel" name="phone"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition-colors">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Service Type</label>
                    <select name="service_type" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition-colors">
                        <option value="">Select a service</option>
                        <option value="automotive_parts">Automotive Parts</option>
                        <option value="shipping_containers">Shipping Containers</option>
                        <option value="steel_products">Steel Products</option>
                        <option value="freight_logistics">Freight & Logistics</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                    <textarea name="message" rows="4" required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-green-500 transition-colors"
                              placeholder="Please describe your requirements..."></textarea>
                </div>

                <button type="submit" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium transition-colors">
                    Send Quote Request
                </button>
            </form>
        </div>
    </div>
</div>

<script>
function openQuoteModal() {
    document.getElementById('quote-modal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeQuoteModal() {
    document.getElementById('quote-modal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}

// Close modal when clicking outside
document.getElementById('quote-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuoteModal();
    }
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeQuoteModal();
    }
});

// Newsletter subscription
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const messageDiv = document.getElementById('newsletter-message');
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnContent = submitBtn.innerHTML;

    // Show loading state
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    submitBtn.disabled = true;
    messageDiv.classList.add('hidden');

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageDiv.textContent = data.message;
            messageDiv.className = 'text-sm text-green-400 mt-2';
            form.reset();
        } else {
            messageDiv.textContent = data.message || 'An error occurred. Please try again.';
            messageDiv.className = 'text-sm text-red-400 mt-2';
        }
        messageDiv.classList.remove('hidden');
    })
    .catch(error => {
        messageDiv.textContent = 'An error occurred. Please try again.';
        messageDiv.className = 'text-sm text-red-400 mt-2';
        messageDiv.classList.remove('hidden');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalBtnContent;
        submitBtn.disabled = false;
    });
});
</script>
<?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/layouts/partials/frontend/footer.blade.php ENDPATH**/ ?>