<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ $siteSettings['site_title'] ?? config('app.name', 'Atrix Logistics') }}</title>

        <!-- Favicon -->
        @if(!empty($siteSettings['site_favicon']) && str_starts_with($siteSettings['site_favicon'], 'uploads/'))
            <link rel="icon" href="{{ Storage::url($siteSettings['site_favicon']) }}" type="image/x-icon">
            <link rel="shortcut icon" href="{{ Storage::url($siteSettings['site_favicon']) }}" type="image/x-icon">
        @else
            <link rel="icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
            <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
        @endif

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            @include('layouts.navigation')

            <!-- Page Heading -->
            @isset($header)
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endisset

            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>
        </div>
    </body>
</html>
