<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class SupportTicket extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'ticket_number',
        'user_id',
        'assigned_to',
        'subject',
        'description',
        'priority',
        'status',
        'category',
        'customer_name',
        'customer_email',
        'customer_phone',
        'order_number',
        'parcel_tracking',
        'attachments',
        'resolution',
        'resolved_at',
        'closed_at',
        'admin_notes',
        'metadata',
    ];

    protected $casts = [
        'attachments' => 'array',
        'metadata' => 'array',
        'resolved_at' => 'datetime',
        'closed_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Scopes
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'open');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', ['high', 'urgent']);
    }

    /**
     * Accessors & Mutators
     */
    public function getFormattedStatusAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->status));
    }

    public function getFormattedPriorityAttribute(): string
    {
        return ucwords($this->priority);
    }

    public function getStatusBadgeColorAttribute(): string
    {
        return match($this->status) {
            'open' => 'danger',
            'in_progress' => 'warning',
            'waiting_customer' => 'info',
            'waiting_admin' => 'primary',
            'resolved' => 'success',
            'closed' => 'secondary',
            default => 'secondary'
        };
    }

    public function getPriorityBadgeColorAttribute(): string
    {
        return match($this->priority) {
            'low' => 'success',
            'medium' => 'info',
            'high' => 'warning',
            'urgent' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Helper Methods
     */
    public function isOpen(): bool
    {
        return $this->status === 'open';
    }

    public function isInProgress(): bool
    {
        return $this->status === 'in_progress';
    }

    public function isResolved(): bool
    {
        return $this->status === 'resolved';
    }

    public function isClosed(): bool
    {
        return $this->status === 'closed';
    }

    public function isHighPriority(): bool
    {
        return in_array($this->priority, ['high', 'urgent']);
    }

    public function canBeResolved(): bool
    {
        return in_array($this->status, ['open', 'in_progress', 'waiting_customer', 'waiting_admin']);
    }

    public function canBeClosed(): bool
    {
        return in_array($this->status, ['open', 'in_progress', 'waiting_customer', 'waiting_admin', 'resolved']);
    }

    public function canBeReopened(): bool
    {
        return $this->status === 'closed';
    }

    public function canBeEdited(): bool
    {
        return in_array($this->status, ['open', 'in_progress', 'waiting_customer']);
    }

    public function getFormattedCategoryAttribute(): string
    {
        return ucwords(str_replace('_', ' ', $this->category));
    }

    public function getDaysOpen(): int
    {
        return $this->created_at->diffInDays(now());
    }

    public function getResponseTime(): ?int
    {
        if ($this->resolved_at) {
            return $this->created_at->diffInHours($this->resolved_at);
        }
        return null;
    }

    /**
     * Static Methods
     */
    public static function generateTicketNumber(): string
    {
        $prefix = 'TKT';
        $timestamp = now()->format('Ymd');
        $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);

        $ticketNumber = $prefix . $timestamp . $random;

        // Ensure uniqueness
        $counter = 1;
        $originalTicketNumber = $ticketNumber;
        while (static::where('ticket_number', $ticketNumber)->exists()) {
            $ticketNumber = $originalTicketNumber . $counter;
            $counter++;
        }

        return $ticketNumber;
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($ticket) {
            if (empty($ticket->ticket_number)) {
                $ticket->ticket_number = static::generateTicketNumber();
            }
        });
    }
}
