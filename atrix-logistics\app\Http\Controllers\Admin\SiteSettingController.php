<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use App\Services\ImageProcessingService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class SiteSettingController extends Controller
{
    protected ImageProcessingService $imageService;

    public function __construct(ImageProcessingService $imageService)
    {
        $this->imageService = $imageService;
    }

    /**
     * Display the site settings management interface
     */
    public function index(): View
    {
        $settings = SiteSetting::orderBy('group_name')->orderBy('key_name')->get();

        $groupedSettings = $settings->groupBy('group_name');

        return view('admin.settings.index', compact('groupedSettings'));
    }

    /**
     * Update site settings
     */
    public function update(Request $request): RedirectResponse
    {
        // Log the incoming request for debugging
        \Log::info('Site settings update request received', [
            'settings_keys' => array_keys($request->input('settings', [])),
            'files_keys' => array_keys($request->file('files', [])),
            'has_files' => $request->hasFile('files'),
        ]);

        $validated = $request->validate([
            'settings' => 'required|array',
            'settings.*' => 'nullable|string|max:5000',
            'files' => 'nullable|array',
            'files.*' => 'nullable|file|image|max:5120', // 5MB max, images only
        ]);

        foreach ($validated['settings'] as $key => $value) {
            $setting = SiteSetting::where('key_name', $key)->first();

            if ($setting) {
                \Log::info("Processing setting: {$key}", [
                    'type' => $setting->type,
                    'current_value' => $setting->value,
                    'new_value' => $value,
                    'has_file' => $request->hasFile("files.{$key}")
                ]);

                // Handle file uploads for image type settings
                if ($setting->type === 'image') {
                    // Check if a file was uploaded for this setting
                    if ($request->hasFile("files.{$key}")) {
                        $file = $request->file("files.{$key}");

                        \Log::info("File upload attempt for {$key}", [
                            'original_name' => $file->getClientOriginalName(),
                            'size' => $file->getSize(),
                            'mime_type' => $file->getMimeType(),
                            'is_valid' => $file->isValid(),
                            'error' => $file->getError()
                        ]);

                        // Validate file
                        if ($file->isValid()) {
                            try {
                                // Delete old file if exists (only if it's in uploads directory)
                                if ($setting->value && str_starts_with($setting->value, 'uploads/')) {
                                    $this->imageService->deleteOldImage($setting->value);
                                    \Log::info("Deleted old file: {$setting->value}");
                                }

                                // Process and convert image using the service
                                $path = $this->imageService->processImage($file, 'uploads/settings', $key);
                                $value = $path;

                                // Log successful upload for debugging
                                \Log::info("File processed and uploaded successfully for {$key}: {$path}");
                            } catch (\Exception $e) {
                                // Log processing error
                                \Log::error("Image processing failed for {$key}", [
                                    'error' => $e->getMessage(),
                                    'file' => $file->getClientOriginalName()
                                ]);
                                $value = $setting->value; // Keep existing value
                            }
                        } else {
                            // Log upload error
                            \Log::error("File upload failed for {$key}", [
                                'error_code' => $file->getError(),
                                'error_message' => $file->getErrorMessage(),
                                'max_file_size' => ini_get('upload_max_filesize'),
                                'post_max_size' => ini_get('post_max_size')
                            ]);
                            $value = $setting->value; // Keep existing value
                        }
                    } else {
                        // No new file uploaded, keep existing value
                        $value = $setting->value;
                        \Log::info("No file uploaded for {$key}, keeping existing value: {$value}");
                    }
                }

                $setting->update(['value' => $value]);
                \Log::info("Updated setting {$key} with value: {$value}");
            }
        }

        // Clear settings cache
        SiteSetting::clearCache();

        return redirect()->route('admin.cms.settings.index')
                        ->with('success', 'Site settings updated successfully.');
    }

    /**
     * Create a new setting
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'key_name' => 'required|string|max:255|unique:site_settings,key_name',
            'value' => 'nullable|string|max:5000',
            'type' => 'required|string|in:string,text,boolean,integer,json,image',
            'group_name' => 'required|string|max:100',
            'label' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'is_public' => 'boolean',
        ]);

        $validated['is_public'] = $request->has('is_public');

        SiteSetting::create($validated);

        return redirect()->route('admin.cms.settings.index')
                        ->with('success', 'Setting created successfully.');
    }

    /**
     * Delete a setting
     */
    public function destroy(SiteSetting $siteSetting): RedirectResponse
    {
        // Delete associated file if it's an image setting
        if ($siteSetting->type === 'image' && $siteSetting->value) {
            if (Storage::disk('public')->exists($siteSetting->value)) {
                Storage::disk('public')->delete($siteSetting->value);
            }
        }

        $siteSetting->delete();

        // Clear settings cache
        SiteSetting::clearCache();

        return redirect()->route('admin.cms.settings.index')
                        ->with('success', 'Setting deleted successfully.');
    }

    /**
     * Reset settings to default values
     */
    public function reset(Request $request): RedirectResponse
    {
        $group = $request->get('group');

        if ($group) {
            $settings = SiteSetting::where('group_name', $group)->get();
            $message = "Settings for group '{$group}' reset to default values.";
        } else {
            $settings = SiteSetting::all();
            $message = "All settings reset to default values.";
        }

        foreach ($settings as $setting) {
            // Delete image files
            if ($setting->type === 'image' && $setting->value) {
                if (Storage::disk('public')->exists($setting->value)) {
                    Storage::disk('public')->delete($setting->value);
                }
            }

            // Reset to default value based on type
            $defaultValue = match($setting->type) {
                'boolean' => '0',
                'integer' => '0',
                'json' => '{}',
                'image' => '',
                default => '',
            };

            $setting->update(['value' => $defaultValue]);
        }

        // Clear settings cache
        SiteSetting::clearCache();

        return redirect()->route('admin.cms.settings.index')
                        ->with('success', $message);
    }
}
