<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        $name = fake()->words(3, true);
        $price = fake()->randomFloat(2, 5, 200);
        $salePrice = fake()->boolean(30) ? fake()->randomFloat(2, 3, $price - 1) : null;
        
        return [
            'category_id' => Category::factory(),
            'name' => ucwords($name),
            'slug' => \Illuminate\Support\Str::slug($name),
            'sku' => strtoupper(fake()->bothify('??###')),
            'short_description' => fake()->sentence(),
            'description' => fake()->paragraphs(3, true),
            'price' => $price,
            'sale_price' => $salePrice,
            'cost_price' => fake()->randomFloat(2, 1, $price * 0.7),
            'stock_quantity' => fake()->numberBetween(0, 100),
            'min_stock_level' => fake()->numberBetween(5, 20),
            'weight' => fake()->randomFloat(2, 0.1, 10),
            'dimensions' => [
                'length' => fake()->randomFloat(1, 5, 50),
                'width' => fake()->randomFloat(1, 5, 50),
                'height' => fake()->randomFloat(1, 5, 50),
                'unit' => 'cm'
            ],
            'is_active' => true,
            'is_featured' => fake()->boolean(20),
            'sort_order' => fake()->numberBetween(1, 100),
            'meta_title' => ucwords($name),
            'meta_description' => fake()->sentence(),
            'images' => [
                'https://via.placeholder.com/400x400?text=' . urlencode($name),
                'https://via.placeholder.com/400x400?text=' . urlencode($name . ' 2')
            ],
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    public function onSale(): static
    {
        return $this->state(function (array $attributes) {
            $price = $attributes['price'] ?? fake()->randomFloat(2, 10, 200);
            return [
                'sale_price' => fake()->randomFloat(2, 5, $price - 1),
            ];
        });
    }

    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => 0,
        ]);
    }

    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => fake()->numberBetween(1, 5),
            'min_stock_level' => 10,
        ]);
    }
}
