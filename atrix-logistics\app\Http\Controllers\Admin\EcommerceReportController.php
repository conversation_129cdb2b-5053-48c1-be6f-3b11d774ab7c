<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Category;
use App\Models\Order;
use App\Models\User;
use App\Helpers\CurrencyHelper;
use App\Exports\ProductsExport;
use App\Exports\OrdersExport;
use App\Exports\EcommerceAnalyticsExport;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class EcommerceReportController extends Controller
{
    /**
     * Display e-commerce reports dashboard
     */
    public function index(): View
    {
        // Get overview statistics
        $overviewStats = $this->getOverviewStats();

        // Get recent activity
        $recentActivity = $this->getRecentActivity();

        // Get currency settings
        $currencySettings = CurrencyHelper::getSettings();

        return view('admin.ecommerce.reports.index', compact('overviewStats', 'recentActivity', 'currencySettings'));
    }

    /**
     * Get product analytics data
     */
    public function productAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        // Product creation trend
        $productTrend = Product::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                              ->where('created_at', '>=', $startDate)
                              ->groupBy('date')
                              ->orderBy('date')
                              ->get()
                              ->pluck('count', 'date');

        // Product status distribution
        $statusDistribution = [
            'active' => Product::where('is_active', true)->count(),
            'inactive' => Product::where('is_active', false)->count(),
            'featured' => Product::where('is_featured', true)->count(),
        ];

        // Stock status distribution
        $stockDistribution = [
            'in_stock' => Product::where('manage_stock', true)
                                ->where('stock_quantity', '>', 0)
                                ->whereColumn('stock_quantity', '>', 'min_stock_level')
                                ->count(),
            'low_stock' => Product::where('manage_stock', true)
                                 ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                 ->where('stock_quantity', '>', 0)
                                 ->count(),
            'out_of_stock' => Product::where('manage_stock', true)
                                    ->where('stock_quantity', 0)
                                    ->count(),
        ];

        // Category performance
        $categoryPerformance = Category::withCount('products')
                                     ->where('is_active', true)
                                     ->orderBy('products_count', 'desc')
                                     ->limit(10)
                                     ->get()
                                     ->map(function($category) {
                                         return [
                                             'name' => $category->name,
                                             'products_count' => $category->products_count,
                                             'avg_price' => $category->products()->avg('price') ?? 0,
                                         ];
                                     });

        // Top products by stock value
        $topProductsByValue = Product::where('is_active', true)
                                   ->where('manage_stock', true)
                                   ->selectRaw('name, stock_quantity, price, (stock_quantity * price) as stock_value')
                                   ->orderBy('stock_value', 'desc')
                                   ->limit(10)
                                   ->get();

        return response()->json([
            'product_trend' => $productTrend,
            'status_distribution' => $statusDistribution,
            'stock_distribution' => $stockDistribution,
            'category_performance' => $categoryPerformance,
            'top_products_by_value' => $topProductsByValue,
            'period' => $period,
        ]);
    }

    /**
     * Get sales analytics data
     */
    public function salesAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        // Daily sales trend
        $salesTrend = Order::selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(total_amount) as revenue')
                          ->where('created_at', '>=', $startDate)
                          ->where('payment_status', 'paid')
                          ->groupBy('date')
                          ->orderBy('date')
                          ->get()
                          ->mapWithKeys(function($item) {
                              return [$item->date => [
                                  'orders' => $item->orders,
                                  'revenue' => $item->revenue
                              ]];
                          });

        // Order status distribution
        $orderStatusDistribution = Order::selectRaw('status, COUNT(*) as count')
                                       ->groupBy('status')
                                       ->pluck('count', 'status');

        // Payment status distribution
        $paymentStatusDistribution = Order::selectRaw('payment_status, COUNT(*) as count')
                                         ->groupBy('payment_status')
                                         ->pluck('count', 'payment_status');

        // Top customers by orders
        $topCustomers = User::where('role', 'customer')
                           ->withCount(['orders' => function($query) {
                               $query->where('payment_status', 'paid');
                           }])
                           ->withSum(['orders as total_spent' => function($query) {
                               $query->where('payment_status', 'paid');
                           }], 'total_amount')
                           ->orderBy('orders_count', 'desc')
                           ->limit(10)
                           ->get()
                           ->map(function($customer) {
                               return [
                                   'name' => $customer->name,
                                   'email' => $customer->email,
                                   'orders_count' => $customer->orders_count,
                                   'total_spent' => $customer->total_spent ?? 0,
                               ];
                           });

        return response()->json([
            'sales_trend' => $salesTrend,
            'order_status_distribution' => $orderStatusDistribution,
            'payment_status_distribution' => $paymentStatusDistribution,
            'top_customers' => $topCustomers,
            'period' => $period,
        ]);
    }

    /**
     * Get inventory analytics data
     */
    public function inventoryAnalytics(): JsonResponse
    {
        // Low stock alerts
        $lowStockProducts = Product::where('manage_stock', true)
                                  ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                  ->where('stock_quantity', '>', 0)
                                  ->orderBy('stock_quantity', 'asc')
                                  ->limit(20)
                                  ->get(['name', 'stock_quantity', 'min_stock_level', 'price']);

        // Out of stock products
        $outOfStockProducts = Product::where('manage_stock', true)
                                    ->where('stock_quantity', 0)
                                    ->orderBy('updated_at', 'desc')
                                    ->limit(20)
                                    ->get(['name', 'price', 'updated_at']);

        // Inventory value by category
        $inventoryByCategory = Category::with(['products' => function($query) {
                                         $query->where('is_active', true)
                                               ->where('manage_stock', true);
                                     }])
                                     ->get()
                                     ->map(function($category) {
                                         $totalValue = $category->products->sum(function($product) {
                                             return $product->stock_quantity * $product->price;
                                         });
                                         return [
                                             'name' => $category->name,
                                             'total_value' => $totalValue,
                                             'products_count' => $category->products->count(),
                                         ];
                                     })
                                     ->sortByDesc('total_value')
                                     ->values();

        // Total inventory statistics
        $inventoryStats = [
            'total_products' => Product::where('is_active', true)->count(),
            'total_stock_value' => Product::where('is_active', true)
                                         ->where('manage_stock', true)
                                         ->selectRaw('SUM(stock_quantity * price) as total')
                                         ->value('total') ?? 0,
            'low_stock_count' => $lowStockProducts->count(),
            'out_of_stock_count' => $outOfStockProducts->count(),
        ];

        return response()->json([
            'low_stock_products' => $lowStockProducts,
            'out_of_stock_products' => $outOfStockProducts,
            'inventory_by_category' => $inventoryByCategory,
            'inventory_stats' => $inventoryStats,
        ]);
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        return [
            'total_products' => Product::count(),
            'active_products' => Product::where('is_active', true)->count(),
            'total_categories' => Category::count(),
            'active_categories' => Category::where('is_active', true)->count(),
            'total_orders' => Order::count(),
            'paid_orders' => Order::where('payment_status', 'paid')->count(),
            'total_revenue' => Order::where('payment_status', 'paid')->sum('total_amount'),
            'low_stock_alerts' => Product::where('manage_stock', true)
                                        ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                        ->where('stock_quantity', '>', 0)
                                        ->count(),
            'out_of_stock_alerts' => Product::where('manage_stock', true)
                                           ->where('stock_quantity', 0)
                                           ->count(),
        ];
    }

    /**
     * Get recent activity
     */
    private function getRecentActivity(): array
    {
        return [
            'recent_products' => Product::orderBy('created_at', 'desc')->limit(5)->get(),
            'recent_orders' => Order::with('customer')->orderBy('created_at', 'desc')->limit(5)->get(),
            'recent_categories' => Category::orderBy('created_at', 'desc')->limit(3)->get(),
        ];
    }

    /**
     * Export products data
     */
    public function exportProducts(Request $request)
    {
        $format = $request->get('format', 'xlsx');
        $filename = 'products_export_' . date('Y-m-d_H-i-s');

        return Excel::download(new ProductsExport($request), $filename . '.' . $format);
    }

    /**
     * Export orders data
     */
    public function exportOrders(Request $request)
    {
        $format = $request->get('format', 'xlsx');
        $filename = 'orders_export_' . date('Y-m-d_H-i-s');

        return Excel::download(new OrdersExport($request), $filename . '.' . $format);
    }

    /**
     * Export comprehensive analytics data
     */
    public function exportAnalytics(Request $request)
    {
        $format = $request->get('format', 'xlsx');
        $filename = 'ecommerce_analytics_' . date('Y-m-d_H-i-s');

        return Excel::download(new EcommerceAnalyticsExport($request), $filename . '.' . $format);
    }

    /**
     * Export analytics as PDF
     */
    public function exportAnalyticsPdf(Request $request)
    {
        $period = $request->get('period', 30);
        $overviewStats = $this->getOverviewStats();
        $currencySettings = CurrencyHelper::getSettings();

        // Get analytics data for PDF
        $productAnalytics = $this->getProductAnalyticsForPdf($period);
        $salesAnalytics = $this->getSalesAnalyticsForPdf($period);
        $inventoryAnalytics = $this->getInventoryAnalyticsForPdf();

        $data = [
            'overviewStats' => $overviewStats,
            'productAnalytics' => $productAnalytics,
            'salesAnalytics' => $salesAnalytics,
            'inventoryAnalytics' => $inventoryAnalytics,
            'currencySettings' => $currencySettings,
            'period' => $period,
            'generated_at' => now()->format('Y-m-d H:i:s'),
        ];

        $pdf = Pdf::loadView('admin.ecommerce.reports.pdf', $data);

        return $pdf->download('ecommerce_analytics_' . date('Y-m-d_H-i-s') . '.pdf');
    }

    /**
     * Get product analytics data for PDF
     */
    private function getProductAnalyticsForPdf($period): array
    {
        $startDate = Carbon::now()->subDays($period);

        return [
            'top_products' => Product::where('is_active', true)
                                   ->orderBy('created_at', 'desc')
                                   ->limit(10)
                                   ->get(),
            'category_performance' => Category::withCount('products')
                                            ->where('is_active', true)
                                            ->orderBy('products_count', 'desc')
                                            ->limit(10)
                                            ->get(),
            'stock_alerts' => Product::where('manage_stock', true)
                                   ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                   ->limit(10)
                                   ->get(),
        ];
    }

    /**
     * Get sales analytics data for PDF
     */
    private function getSalesAnalyticsForPdf($period): array
    {
        $startDate = Carbon::now()->subDays($period);

        return [
            'recent_orders' => Order::with('customer')
                                  ->where('created_at', '>=', $startDate)
                                  ->orderBy('total_amount', 'desc')
                                  ->limit(10)
                                  ->get(),
            'top_customers' => User::where('role', 'customer')
                                 ->withCount(['orders' => function($query) {
                                     $query->where('payment_status', 'paid');
                                 }])
                                 ->withSum(['orders as total_spent' => function($query) {
                                     $query->where('payment_status', 'paid');
                                 }], 'total_amount')
                                 ->orderBy('total_spent', 'desc')
                                 ->limit(10)
                                 ->get(),
        ];
    }

    /**
     * Get inventory analytics data for PDF
     */
    private function getInventoryAnalyticsForPdf(): array
    {
        return [
            'low_stock_products' => Product::where('manage_stock', true)
                                          ->whereColumn('stock_quantity', '<=', 'min_stock_level')
                                          ->where('stock_quantity', '>', 0)
                                          ->orderBy('stock_quantity', 'asc')
                                          ->limit(10)
                                          ->get(),
            'out_of_stock_products' => Product::where('manage_stock', true)
                                             ->where('stock_quantity', 0)
                                             ->orderBy('updated_at', 'desc')
                                             ->limit(10)
                                             ->get(),
        ];
    }
}
