<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Product;
use App\Models\SiteSetting;
use App\Services\SeoLocalizationService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;

class SitemapController extends Controller
{
    protected $seoService;

    public function __construct(SeoLocalizationService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Generate the main sitemap index
     */
    public function index()
    {
        $sitemaps = [];
        
        // Add main sitemap
        $sitemaps[] = [
            'loc' => route('sitemap.main'),
            'lastmod' => now()->toISOString(),
        ];

        // Add localized sitemaps if enabled
        if (SiteSetting::getValue('generate_localized_sitemaps', true)) {
            $supportedLocales = $this->seoService->getSupportedLocales();
            
            foreach ($supportedLocales as $locale => $config) {
                if ($config['enabled']) {
                    $sitemaps[] = [
                        'loc' => route('sitemap.locale', ['locale' => $locale]),
                        'lastmod' => now()->toISOString(),
                    ];
                }
            }
        }

        $xml = view('sitemaps.index', compact('sitemaps'))->render();

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate the main sitemap
     */
    public function main()
    {
        $urls = Cache::remember('sitemap_main', 3600, function () {
            return $this->generateMainSitemapUrls();
        });

        $xml = view('sitemaps.main', compact('urls'))->render();

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate localized sitemap
     */
    public function locale($locale)
    {
        if (!$this->seoService->isLocaleSupported($locale)) {
            abort(404);
        }

        $urls = Cache::remember("sitemap_locale_{$locale}", 3600, function () use ($locale) {
            return $this->generateLocalizedSitemapUrls($locale);
        });

        $xml = view('sitemaps.locale', compact('urls', 'locale'))->render();

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600',
        ]);
    }

    /**
     * Generate main sitemap URLs
     */
    protected function generateMainSitemapUrls()
    {
        $urls = [];
        $includeAlternates = SiteSetting::getValue('sitemap_include_alternates', true);

        // Homepage
        $urls[] = [
            'loc' => route('home'),
            'lastmod' => now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '1.0',
            'alternates' => $includeAlternates ? $this->getAlternateUrls(route('home')) : [],
        ];

        // Static pages
        $staticPages = [
            ['route' => 'about', 'priority' => '0.8', 'changefreq' => 'monthly'],
            ['route' => 'services', 'priority' => '0.9', 'changefreq' => 'weekly'],
            ['route' => 'products', 'priority' => '0.9', 'changefreq' => 'daily'],
            ['route' => 'contact', 'priority' => '0.7', 'changefreq' => 'monthly'],
            ['route' => 'track', 'priority' => '0.6', 'changefreq' => 'monthly'],
        ];

        foreach ($staticPages as $page) {
            if (\Route::has($page['route'])) {
                $url = route($page['route']);
                $urls[] = [
                    'loc' => $url,
                    'lastmod' => now()->toISOString(),
                    'changefreq' => $page['changefreq'],
                    'priority' => $page['priority'],
                    'alternates' => $includeAlternates ? $this->getAlternateUrls($url) : [],
                ];
            }
        }

        // Categories
        $categories = Category::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        foreach ($categories as $category) {
            $url = route('categories.show', $category->slug);
            $urls[] = [
                'loc' => $url,
                'lastmod' => $category->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8',
                'alternates' => $includeAlternates ? $this->getAlternateUrls($url) : [],
            ];
        }

        // Products
        $products = Product::where('is_active', true)
            ->orderBy('updated_at', 'desc')
            ->get();

        foreach ($products as $product) {
            $url = route('products.show', $product->slug);
            $urls[] = [
                'loc' => $url,
                'lastmod' => $product->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.7',
                'alternates' => $includeAlternates ? $this->getAlternateUrls($url) : [],
            ];
        }

        return $urls;
    }

    /**
     * Generate localized sitemap URLs
     */
    protected function generateLocalizedSitemapUrls($locale)
    {
        $urls = [];
        $baseUrls = $this->generateMainSitemapUrls();

        foreach ($baseUrls as $baseUrl) {
            $localizedUrl = $this->getLocalizedUrl($baseUrl['loc'], $locale);
            
            $urls[] = [
                'loc' => $localizedUrl,
                'lastmod' => $baseUrl['lastmod'],
                'changefreq' => $baseUrl['changefreq'],
                'priority' => $baseUrl['priority'],
            ];
        }

        return $urls;
    }

    /**
     * Get alternate URLs for hreflang
     */
    protected function getAlternateUrls($url)
    {
        $alternates = [];
        $supportedLocales = $this->seoService->getSupportedLocales();

        foreach ($supportedLocales as $locale => $config) {
            if ($config['enabled']) {
                $alternates[] = [
                    'hreflang' => $locale,
                    'href' => $this->getLocalizedUrl($url, $locale),
                ];
            }
        }

        // Add x-default if configured
        $xDefaultUrl = SiteSetting::getValue('hreflang_x_default');
        if ($xDefaultUrl) {
            $alternates[] = [
                'hreflang' => 'x-default',
                'href' => $xDefaultUrl,
            ];
        }

        return $alternates;
    }

    /**
     * Get localized URL for a given locale
     */
    protected function getLocalizedUrl($url, $locale)
    {
        $supportedLocales = $this->seoService->getSupportedLocales();
        
        if (isset($supportedLocales[$locale]['url']) && !empty($supportedLocales[$locale]['url'])) {
            return $supportedLocales[$locale]['url'];
        }

        $urlStructure = SiteSetting::getValue('url_structure_type', 'subdirectory');
        $hideDefault = SiteSetting::getValue('hide_default_locale_in_url', true);
        $defaultLocale = SiteSetting::getValue('default_locale', 'en-US');

        switch ($urlStructure) {
            case 'subdirectory':
                if ($hideDefault && $locale === $defaultLocale) {
                    return $url;
                }
                return preg_replace('/^(https?:\/\/[^\/]+)/', '$1/' . $locale, $url);
                
            case 'subdomain':
                if ($hideDefault && $locale === $defaultLocale) {
                    return $url;
                }
                return preg_replace('/^(https?:\/\/)([^.]+\.)/', '$1' . $locale . '.', $url);
                
            case 'parameter':
                $separator = strpos($url, '?') !== false ? '&' : '?';
                return $url . $separator . 'lang=' . $locale;
                
            default:
                return $url;
        }
    }
}
