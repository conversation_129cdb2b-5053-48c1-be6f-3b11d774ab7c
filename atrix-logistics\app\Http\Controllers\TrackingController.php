<?php

namespace App\Http\Controllers;

use App\Models\Parcel;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;

class TrackingController extends Controller
{
    /**
     * Show the tracking page
     */
    public function index(): View
    {
        return view('frontend.tracking.index', [
            'pageTitle' => 'Track Your Package',
            'siteSettings' => SiteSetting::getPublicSettings(),
        ]);
    }

    /**
     * Track a parcel by tracking number
     */
    public function track(Request $request): View
    {
        $request->validate([
            'tracking_number' => 'required|string|max:100',
        ]);

        $trackingNumber = strtoupper(trim($request->tracking_number));

        $parcel = Parcel::where('tracking_number', $trackingNumber)
                       ->with(['carrier', 'publicTrackingEvents'])
                       ->first();

        if (!$parcel) {
            return view('frontend.tracking.index', [
                'pageTitle' => 'Track Your Package',
                'siteSettings' => SiteSetting::getPublicSettings(),
                'error' => 'Tracking number not found. Please check your tracking number and try again.',
                'trackingNumber' => $trackingNumber,
            ]);
        }

        return view('frontend.tracking.result', [
            'pageTitle' => "Tracking: {$trackingNumber}",
            'siteSettings' => SiteSetting::getPublicSettings(),
            'parcel' => $parcel,
            'trackingNumber' => $trackingNumber,
        ]);
    }

    /**
     * API endpoint for tracking
     */
    public function apiTrack(string $trackingNumber): JsonResponse
    {
        $trackingNumber = strtoupper(trim($trackingNumber));

        $parcel = Parcel::where('tracking_number', $trackingNumber)
                       ->with(['carrier', 'publicTrackingEvents'])
                       ->first();

        if (!$parcel) {
            return response()->json([
                'success' => false,
                'message' => 'Tracking number not found',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'tracking_number' => $parcel->tracking_number,
                'status' => $parcel->status,
                'formatted_status' => $parcel->getFormattedStatus(),
                'current_location' => $parcel->publicTrackingEvents->first()?->location,
                'estimated_delivery' => $parcel->estimated_delivery_date?->format('Y-m-d'),
                'delivered_at' => $parcel->delivered_at?->format('Y-m-d H:i:s'),
                'carrier' => [
                    'id' => $parcel->carrier->id,
                    'name' => $parcel->carrier->name,
                    'code' => $parcel->carrier->code,
                ],
                'events' => $parcel->publicTrackingEvents->map(function ($event) {
                    return [
                        'id' => $event->id,
                        'status' => $event->status,
                        'formatted_status' => $event->getFormattedStatus(),
                        'location' => $event->location,
                        'description' => $event->description,
                        'event_date' => $event->event_date->format('Y-m-d H:i:s'),
                        'is_public' => $event->is_public,
                    ];
                }),
            ],
        ]);
    }
}
