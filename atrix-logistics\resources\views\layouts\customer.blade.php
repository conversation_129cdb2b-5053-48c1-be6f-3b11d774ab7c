<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Customer Portal') - {{ config('app.name', 'Atrix Logistics') }}</title>

    <!-- Favicon -->
    @if(!empty($siteSettings['site_favicon']) && str_starts_with($siteSettings['site_favicon'], 'uploads/'))
        <link rel="icon" href="{{ Storage::url($siteSettings['site_favicon']) }}" type="image/x-icon">
        <link rel="shortcut icon" href="{{ Storage::url($siteSettings['site_favicon']) }}" type="image/x-icon">
    @else
        <link rel="icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
        <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --info-color: #0891b2;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
        }

        body {
            font-family: 'Figtree', sans-serif;
            background-color: #f8fafc;
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--primary-color) !important;
        }

        /* Fixed Sidebar Styles */
        .sidebar-fixed {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            transition: all 0.3s ease;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-fixed.collapsed {
            transform: translateX(-280px);
        }

        .sidebar-header {
            flex-shrink: 0;
            background: rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
        }

        .sidebar-footer {
            flex-shrink: 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
        }

        .avatar-circle {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }

        .sidebar-fixed .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            margin: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .sidebar-fixed .nav-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: white;
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .sidebar-fixed .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            padding-left: 2rem;
        }

        .sidebar-fixed .nav-link:hover::before {
            transform: scaleY(1);
        }

        .sidebar-fixed .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.2);
            font-weight: 600;
            padding-left: 2rem;
        }

        .sidebar-fixed .nav-link.active::before {
            transform: scaleY(1);
        }

        .sidebar-fixed .nav-link i {
            width: 20px;
            text-align: center;
            flex-shrink: 0;
        }

        .sidebar-fixed .nav-link span {
            flex: 1;
        }

        .nav-divider hr {
            border-color: rgba(255, 255, 255, 0.2);
            margin: 0.5rem 1.5rem;
        }

        /* Main content area */
        .main-content-area {
            margin-left: 280px;
            min-height: 100vh;
            background-color: #f8fafc;
            transition: margin-left 0.3s ease;
            width: calc(100% - 280px);
            overflow-x: hidden;
        }

        .main-content-area.sidebar-collapsed {
            margin-left: 0;
            width: 100%;
        }

        .main-content {
            padding: 2rem;
            max-width: 100%;
            overflow-x: hidden;
        }

        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            color: white;
            border-radius: 1rem 1rem 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            border: none;
            border-radius: 0.5rem;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.4);
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .stats-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }

        .stats-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--light-color);
            border: none;
            font-weight: 600;
            color: var(--dark-color);
        }

        .badge {
            padding: 0.5rem 0.75rem;
            border-radius: 0.5rem;
            font-weight: 500;
        }

        .alert {
            border: none;
            border-radius: 0.75rem;
            padding: 1rem 1.5rem;
        }

        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }

        .breadcrumb {
            background: none;
            padding: 0;
            margin-bottom: 1.5rem;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--secondary-color);
        }

        /* Mobile responsiveness */
        @media (max-width: 991.98px) {
            .sidebar-fixed {
                transform: translateX(-280px);
            }

            .sidebar-fixed.show {
                transform: translateX(0);
            }

            .main-content-area {
                margin-left: 0;
                width: 100%;
            }
        }

        @media (max-width: 767.98px) {
            .main-content {
                padding: 1rem;
            }
        }

        /* Toggle button */
        .sidebar-toggle {
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle i {
            transition: transform 0.3s ease;
        }

        .sidebar-toggle.active i {
            transform: rotate(180deg);
        }

        /* Scrollbar styling for sidebar */
        .sidebar-content::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Floating Cart Icon */
        .floating-cart {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(5, 150, 105, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 1000;
            text-decoration: none;
        }

        .floating-cart:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(5, 150, 105, 0.6);
            color: white;
            text-decoration: none;
        }

        .floating-cart .cart-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc2626;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            border: 2px solid white;
        }

        .floating-cart.hidden {
            display: none;
        }
    </style>

    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <div class="d-flex align-items-center">
                <div class="sidebar-toggle me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </div>
                <a class="navbar-brand" href="{{ route('customer.dashboard') }}">
                    <i class="fas fa-shipping-fast me-2"></i>
                    Atrix Logistics
                </a>
            </div>

            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>
                        {{ Auth::user()->name }}
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ route('customer.profile.show') }}">
                            <i class="fas fa-user me-2"></i> Profile
                        </a></li>
                        <li><a class="dropdown-item" href="{{ route('customer.profile.preferences') }}">
                            <i class="fas fa-cog me-2"></i> Preferences
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('customer.logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="fas fa-sign-out-alt me-2"></i> Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebarMenu" class="sidebar-fixed">
                <div class="sidebar-header">
                    <div class="d-flex align-items-center p-3">
                        <div class="avatar-circle me-3">
                            {{ strtoupper(substr(Auth::user()->name, 0, 2)) }}
                        </div>
                        <div>
                            <h6 class="mb-0 text-white">{{ Auth::user()->name }}</h6>
                            <small class="text-light opacity-75">{{ Auth::user()->email }}</small>
                        </div>
                    </div>
                </div>

                <div class="sidebar-content">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.dashboard') ? 'active' : '' }}"
                               href="{{ route('customer.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-3"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.orders*') ? 'active' : '' }}"
                               href="{{ route('customer.orders') }}">
                                <i class="fas fa-box me-3"></i>
                                <span>My Orders</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.quotes*') ? 'active' : '' }}"
                               href="{{ route('customer.quotes.index') }}">
                                <i class="fas fa-quote-left me-3"></i>
                                <span>My Quotes</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.track') ? 'active' : '' }}"
                               href="{{ route('customer.track') }}">
                                <i class="fas fa-search-location me-3"></i>
                                <span>Track Package</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.wishlist*') ? 'active' : '' }}"
                               href="{{ route('customer.wishlist.index') }}">
                                <i class="fas fa-heart me-3"></i>
                                <span>Wishlist</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.support*') ? 'active' : '' }}"
                               href="{{ route('customer.support.index') }}">
                                <i class="fas fa-headset me-3"></i>
                                <span>Support</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('customer.profile.*') ? 'active' : '' }}"
                               href="{{ route('customer.profile.show') }}">
                                <i class="fas fa-user me-3"></i>
                                <span>Profile</span>
                            </a>
                        </li>

                        <!-- Divider -->
                        <li class="nav-divider">
                            <hr class="my-3 opacity-25">
                        </li>

                        <!-- Quick Actions -->
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('customer.quotes.create') }}">
                                <i class="fas fa-plus-circle me-3 text-success"></i>
                                <span>Request Quote</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('customer.support.create') }}">
                                <i class="fas fa-ticket-alt me-3 text-warning"></i>
                                <span>Create Ticket</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="sidebar-footer">
                    <div class="p-3">
                        <form method="POST" action="{{ route('logout') }}" class="d-grid">
                            @csrf
                            <button type="submit" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </nav>

            <!-- Main content -->
            <main class="main-content-area">
                <div class="main-content">
                    <!-- Breadcrumb -->
                    @if(isset($breadcrumbs))
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb">
                                @foreach($breadcrumbs as $breadcrumb)
                                    @if($loop->last)
                                        <li class="breadcrumb-item active">{{ $breadcrumb['title'] }}</li>
                                    @else
                                        <li class="breadcrumb-item">
                                            <a href="{{ $breadcrumb['url'] }}">{{ $breadcrumb['title'] }}</a>
                                        </li>
                                    @endif
                                @endforeach
                            </ol>
                        </nav>
                    @endif

                    <!-- Page Header -->
                    @hasSection('page-title')
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">@yield('page-title')</h1>
                            @hasSection('page-actions')
                                <div class="btn-toolbar mb-2 mb-md-0">
                                    @yield('page-actions')
                                </div>
                            @endif
                        </div>
                    @endif

                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('warning'))
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ session('warning') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    @if(session('info'))
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ session('info') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Main Content -->
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js for dashboard charts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileToggle = document.querySelector('.navbar-toggler');
            const sidebar = document.getElementById('sidebarMenu');
            const mainContent = document.querySelector('.main-content-area');

            // Desktop sidebar toggle
            if (sidebarToggle && sidebar && mainContent) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('sidebar-collapsed');
                    sidebarToggle.classList.toggle('active');
                });
            }

            // Mobile sidebar toggle
            if (mobileToggle && sidebar) {
                mobileToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });

                // Close sidebar when clicking outside on mobile
                document.addEventListener('click', function(e) {
                    if (window.innerWidth <= 991.98) {
                        if (!sidebar.contains(e.target) &&
                            !mobileToggle.contains(e.target) &&
                            !sidebarToggle.contains(e.target)) {
                            sidebar.classList.remove('show');
                        }
                    }
                });

                // Handle window resize
                window.addEventListener('resize', function() {
                    if (window.innerWidth > 991.98) {
                        sidebar.classList.remove('show');
                    }
                });
            }

            // Save sidebar state in localStorage
            const savedState = localStorage.getItem('sidebarCollapsed');
            if (savedState === 'true' && window.innerWidth > 991.98) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('sidebar-collapsed');
                sidebarToggle.classList.add('active');
            }

            // Save state when toggling
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    const isCollapsed = sidebar.classList.contains('collapsed');
                    localStorage.setItem('sidebarCollapsed', isCollapsed);
                });
            }
        });
    </script>

    <!-- Currency Helper Script -->
    <script>
        // Global currency settings
        window.currencySettings = {
            symbol: '@currencySymbol',
            code: '@currencyCode',
            position: '{{ \App\Helpers\CurrencyHelper::getPosition() }}',
            decimalPlaces: {{ \App\Helpers\CurrencyHelper::getDecimalPlaces() }},
            thousandsSeparator: '{{ \App\Helpers\CurrencyHelper::getThousandsSeparator() }}',
            decimalSeparator: '{{ \App\Helpers\CurrencyHelper::getDecimalSeparator() }}'
        };

        // Currency formatting function
        function formatCurrency(amount, showSymbol = true) {
            if (amount === null || amount === undefined || amount === '') {
                amount = 0;
            }

            amount = parseFloat(amount);

            // Format the number
            const formattedAmount = amount.toLocaleString('en-US', {
                minimumFractionDigits: window.currencySettings.decimalPlaces,
                maximumFractionDigits: window.currencySettings.decimalPlaces
            });

            if (!showSymbol) {
                return formattedAmount;
            }

            // Add currency symbol
            if (window.currencySettings.position === 'before') {
                return window.currencySettings.symbol + formattedAmount;
            } else {
                return formattedAmount + ' ' + window.currencySettings.symbol;
            }
        }

        // Parse currency string to float
        function parseCurrency(currencyString) {
            if (!currencyString) return 0;

            // Remove currency symbol and spaces
            let cleaned = currencyString.toString()
                .replace(window.currencySettings.symbol, '')
                .replace(/\s/g, '');

            // Replace thousands separator
            if (window.currencySettings.thousandsSeparator !== ',') {
                cleaned = cleaned.replace(new RegExp('\\' + window.currencySettings.thousandsSeparator, 'g'), '');
            } else {
                cleaned = cleaned.replace(/,/g, '');
            }

            // Replace decimal separator with dot
            if (window.currencySettings.decimalSeparator !== '.') {
                cleaned = cleaned.replace(window.currencySettings.decimalSeparator, '.');
            }

            return parseFloat(cleaned) || 0;
        }
    </script>

    <!-- Floating Cart Icon -->
    <a href="{{ route('cart.index') }}" class="floating-cart" id="floating-cart">
        <i class="fas fa-shopping-cart"></i>
        <span class="cart-badge" id="cart-badge">0</span>
    </a>

    <!-- Cart JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();

            // Update cart count every 30 seconds
            setInterval(updateCartCount, 30000);
        });

        function updateCartCount() {
            fetch('/cart/count')
                .then(response => response.json())
                .then(data => {
                    const badge = document.getElementById('cart-badge');
                    const floatingCart = document.getElementById('floating-cart');

                    if (data.count > 0) {
                        badge.textContent = data.count;
                        badge.style.display = 'flex';
                        floatingCart.classList.remove('hidden');
                    } else {
                        badge.style.display = 'none';
                        floatingCart.classList.add('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error fetching cart count:', error);
                });
        }

        // Update cart count when items are added/removed
        window.updateCartCount = updateCartCount;
    </script>

    @stack('scripts')
</body>
</html>
