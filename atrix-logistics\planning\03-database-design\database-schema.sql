-- =====================================================
-- Atrix Logistics Database Schema
-- Laravel 10.48.29 Compatible
-- =====================================================

-- =====================================================
-- CORE AUTHENTICATION & USER MANAGEMENT
-- =====================================================

-- Users table (Laravel default with modifications)
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'staff', 'customer') DEFAULT 'customer',
    phone VARCHAR(20) NULL,
    address TEXT NULL,
    city VARCHAR(100) NULL,
    state VARCHAR(100) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(100) DEFAULT 'USA',
    is_active BOOLEAN DEFAULT TRUE,
    avatar VARCHAR(255) NULL,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- Password reset tokens (Laravel default)
CREATE TABLE password_reset_tokens (
    email VARCHAR(255) PRIMARY KEY,
    token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL
);

-- Personal access tokens (Laravel Sanctum)
CREATE TABLE personal_access_tokens (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tokenable_type VARCHAR(255) NOT NULL,
    tokenable_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    token VARCHAR(64) UNIQUE NOT NULL,
    abilities TEXT NULL,
    last_used_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_tokenable (tokenable_type, tokenable_id)
);

-- =====================================================
-- LOGISTICS & SHIPPING CORE
-- =====================================================

-- Carriers/Shipping Companies
CREATE TABLE carriers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    logo VARCHAR(255) NULL,
    website VARCHAR(255) NULL,
    api_endpoint VARCHAR(255) NULL,
    api_key VARCHAR(255) NULL,
    api_secret VARCHAR(255) NULL,
    tracking_url_template VARCHAR(500) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    supports_api BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (code),
    INDEX idx_active (is_active)
);

-- Parcel/Package Management
CREATE TABLE parcels (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(100) UNIQUE NOT NULL,
    carrier_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NULL, -- Customer who owns this parcel
    
    -- Sender Information
    sender_name VARCHAR(255) NOT NULL,
    sender_email VARCHAR(255) NULL,
    sender_phone VARCHAR(20) NULL,
    sender_address TEXT NOT NULL,
    sender_city VARCHAR(100) NOT NULL,
    sender_state VARCHAR(100) NOT NULL,
    sender_postal_code VARCHAR(20) NOT NULL,
    sender_country VARCHAR(100) DEFAULT 'USA',
    
    -- Recipient Information
    recipient_name VARCHAR(255) NOT NULL,
    recipient_email VARCHAR(255) NULL,
    recipient_phone VARCHAR(20) NULL,
    recipient_address TEXT NOT NULL,
    recipient_city VARCHAR(100) NOT NULL,
    recipient_state VARCHAR(100) NOT NULL,
    recipient_postal_code VARCHAR(20) NOT NULL,
    recipient_country VARCHAR(100) DEFAULT 'USA',
    
    -- Package Details
    description TEXT NULL,
    weight DECIMAL(8,2) NULL, -- in kg
    length DECIMAL(8,2) NULL, -- in cm
    width DECIMAL(8,2) NULL,  -- in cm
    height DECIMAL(8,2) NULL, -- in cm
    declared_value DECIMAL(10,2) NULL,
    
    -- Shipping Details
    service_type VARCHAR(100) NULL, -- Express, Standard, Economy
    status ENUM('pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'returned') DEFAULT 'pending',
    current_location VARCHAR(255) NULL,
    
    -- Dates
    shipped_at TIMESTAMP NULL,
    estimated_delivery TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    
    -- Additional Info
    special_instructions TEXT NULL,
    insurance_amount DECIMAL(10,2) NULL,
    cod_amount DECIMAL(10,2) NULL, -- Cash on Delivery
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (carrier_id) REFERENCES carriers(id) ON DELETE RESTRICT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_tracking (tracking_number),
    INDEX idx_status (status),
    INDEX idx_carrier (carrier_id),
    INDEX idx_user (user_id),
    INDEX idx_recipient_email (recipient_email)
);

-- Tracking Events/History
CREATE TABLE tracking_events (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    parcel_id BIGINT UNSIGNED NOT NULL,
    status VARCHAR(100) NOT NULL,
    location VARCHAR(255) NULL,
    description TEXT NULL,
    event_date TIMESTAMP NOT NULL,
    created_by BIGINT UNSIGNED NULL, -- Admin who created this event
    is_public BOOLEAN DEFAULT TRUE, -- Whether to show to customers
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parcel_id) REFERENCES parcels(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_parcel (parcel_id),
    INDEX idx_event_date (event_date),
    INDEX idx_status (status)
);

-- =====================================================
-- E-COMMERCE SYSTEM
-- =====================================================

-- Product Categories
CREATE TABLE categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NULL,
    image VARCHAR(255) NULL,
    parent_id BIGINT UNSIGNED NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    
    INDEX idx_slug (slug),
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active)
);

-- Products
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NULL,
    short_description TEXT NULL,
    sku VARCHAR(100) UNIQUE NULL,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2) NULL,
    cost_price DECIMAL(10,2) NULL,
    
    -- Inventory
    stock_quantity INT DEFAULT 0,
    manage_stock BOOLEAN DEFAULT TRUE,
    in_stock BOOLEAN DEFAULT TRUE,
    
    -- Physical Properties
    weight DECIMAL(8,2) NULL,
    length DECIMAL(8,2) NULL,
    width DECIMAL(8,2) NULL,
    height DECIMAL(8,2) NULL,
    
    -- SEO & Meta
    meta_title VARCHAR(255) NULL,
    meta_description TEXT NULL,
    
    -- Status
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_slug (slug),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price)
);

-- Product Categories Relationship
CREATE TABLE product_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_product_category (product_id, category_id)
);

-- Product Images
CREATE TABLE product_images (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    product_id BIGINT UNSIGNED NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    alt_text VARCHAR(255) NULL,
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    INDEX idx_product (product_id),
    INDEX idx_primary (is_primary)
);

-- =====================================================
-- ORDER MANAGEMENT
-- =====================================================

-- Orders
CREATE TABLE orders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(100) UNIQUE NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    
    -- Order Totals
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    shipping_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    
    -- Status
    status ENUM('pending', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
    
    -- Billing Information
    billing_name VARCHAR(255) NOT NULL,
    billing_email VARCHAR(255) NOT NULL,
    billing_phone VARCHAR(20) NULL,
    billing_address TEXT NOT NULL,
    billing_city VARCHAR(100) NOT NULL,
    billing_state VARCHAR(100) NOT NULL,
    billing_postal_code VARCHAR(20) NOT NULL,
    billing_country VARCHAR(100) DEFAULT 'USA',
    
    -- Shipping Information
    shipping_name VARCHAR(255) NOT NULL,
    shipping_email VARCHAR(255) NULL,
    shipping_phone VARCHAR(20) NULL,
    shipping_address TEXT NOT NULL,
    shipping_city VARCHAR(100) NOT NULL,
    shipping_state VARCHAR(100) NOT NULL,
    shipping_postal_code VARCHAR(20) NOT NULL,
    shipping_country VARCHAR(100) DEFAULT 'USA',
    
    -- Additional Info
    notes TEXT NULL,
    shipped_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_order_number (order_number),
    INDEX idx_user (user_id),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Order Items
CREATE TABLE order_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10,2) NOT NULL, -- Price at time of order
    total DECIMAL(10,2) NOT NULL, -- quantity * price
    
    -- Product snapshot (in case product is deleted)
    product_name VARCHAR(255) NOT NULL,
    product_sku VARCHAR(100) NULL,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,
    
    INDEX idx_order (order_id),
    INDEX idx_product (product_id)
);

-- =====================================================
-- CONTENT MANAGEMENT SYSTEM
-- =====================================================

-- CMS Pages/Content
CREATE TABLE cms_content (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    section VARCHAR(100) NOT NULL, -- homepage_hero, about_us, services, etc.
    title VARCHAR(255) NULL,
    subtitle VARCHAR(255) NULL,
    content LONGTEXT NULL,
    image VARCHAR(255) NULL,
    button_text VARCHAR(100) NULL,
    button_url VARCHAR(255) NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_section (section),
    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- Site Settings (Enhanced for comprehensive CMS)
CREATE TABLE site_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(255) UNIQUE NOT NULL,
    value LONGTEXT NULL,
    type ENUM('string', 'text', 'integer', 'boolean', 'json', 'image') DEFAULT 'string',
    group_name VARCHAR(100) DEFAULT 'general',
    label VARCHAR(255) NULL,
    description TEXT NULL,
    is_public BOOLEAN DEFAULT FALSE, -- Whether setting can be accessed publicly
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_key (key_name),
    INDEX idx_group (group_name),
    INDEX idx_public (is_public)
);

-- Team Members
CREATE TABLE team_members (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    position VARCHAR(255) NOT NULL,
    department VARCHAR(255) NULL,
    bio TEXT NULL,
    photo VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(20) NULL,
    social_links JSON NULL, -- {linkedin, twitter, facebook, etc.}
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order),
    INDEX idx_department (department)
);

-- Company Branches/Locations
CREATE TABLE branches (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(100) NOT NULL,
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(100) DEFAULT 'USA',
    phone VARCHAR(20) NULL,
    email VARCHAR(255) NULL,
    fax VARCHAR(20) NULL,
    manager_name VARCHAR(255) NULL,
    operating_hours TEXT NULL, -- JSON or text format
    services_offered TEXT NULL,
    is_headquarters BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    latitude DECIMAL(10, 8) NULL,
    longitude DECIMAL(11, 8) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_active (is_active),
    INDEX idx_headquarters (is_headquarters),
    INDEX idx_location (latitude, longitude)
);

-- Homepage Sliders
CREATE TABLE sliders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    subtitle VARCHAR(255) NULL,
    description TEXT NULL,
    image VARCHAR(255) NOT NULL,
    button_text VARCHAR(100) NULL,
    button_url VARCHAR(255) NULL,
    button_style ENUM('primary', 'secondary', 'outline') DEFAULT 'primary',
    text_position ENUM('left', 'center', 'right') DEFAULT 'left',
    overlay_opacity DECIMAL(3,2) DEFAULT 0.5, -- 0.0 to 1.0
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_active (is_active),
    INDEX idx_sort (sort_order)
);

-- Blog Posts
CREATE TABLE blog_posts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    excerpt TEXT NULL,
    content LONGTEXT NOT NULL,
    featured_image VARCHAR(255) NULL,
    author_id BIGINT UNSIGNED NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at TIMESTAMP NULL,
    meta_title VARCHAR(255) NULL,
    meta_description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_published (published_at),
    INDEX idx_author (author_id)
);

-- Testimonials
CREATE TABLE testimonials (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    company VARCHAR(255) NULL,
    position VARCHAR(255) NULL,
    content TEXT NOT NULL,
    rating INT DEFAULT 5, -- 1-5 stars
    image VARCHAR(255) NULL,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (is_active),
    INDEX idx_featured (is_featured),
    INDEX idx_rating (rating)
);

-- Contact Form Submissions
CREATE TABLE contact_submissions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    subject VARCHAR(255) NULL,
    message TEXT NOT NULL,
    type ENUM('contact', 'quote', 'support', 'product_inquiry') DEFAULT 'contact',
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    replied_at TIMESTAMP NULL,
    replied_by BIGINT UNSIGNED NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_email (email),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Quote Requests (Enhanced for product inquiries)
CREATE TABLE quote_requests (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    quote_number VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NULL,
    company VARCHAR(255) NULL,

    -- Shipping Details
    origin_address TEXT NULL,
    destination_address TEXT NULL,
    service_type VARCHAR(100) NULL,
    estimated_weight DECIMAL(8,2) NULL,
    estimated_dimensions VARCHAR(255) NULL,
    preferred_delivery_date DATE NULL,

    -- Quote Details
    message TEXT NULL,
    special_requirements TEXT NULL,
    total_estimated_value DECIMAL(10,2) NULL,

    -- Status and Processing
    status ENUM('pending', 'processing', 'quoted', 'accepted', 'declined', 'expired') DEFAULT 'pending',
    quoted_amount DECIMAL(10,2) NULL,
    quoted_at TIMESTAMP NULL,
    quoted_by BIGINT UNSIGNED NULL,
    expires_at TIMESTAMP NULL,

    -- Tracking
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    source VARCHAR(100) DEFAULT 'website', -- website, product_page, modal, etc.

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (quoted_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_quote_number (quote_number),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Quote Request Items (for product-specific quotes)
CREATE TABLE quote_request_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    quote_request_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NULL, -- NULL for non-product quotes
    product_name VARCHAR(255) NOT NULL, -- Store name even if product deleted
    product_sku VARCHAR(100) NULL,
    quantity INT NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NULL, -- Current product price at time of quote
    special_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (quote_request_id) REFERENCES quote_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL,

    INDEX idx_quote_request (quote_request_id),
    INDEX idx_product (product_id)
);

-- =====================================================
-- SYSTEM TABLES
-- =====================================================

-- Settings/Configuration
CREATE TABLE settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(255) UNIQUE NOT NULL,
    value LONGTEXT NULL,
    type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT NULL,
    group_name VARCHAR(100) DEFAULT 'general',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_key (key_name),
    INDEX idx_group (group_name)
);

-- Activity Logs
CREATE TABLE activity_logs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NULL,
    action VARCHAR(255) NOT NULL,
    model_type VARCHAR(255) NULL,
    model_id BIGINT UNSIGNED NULL,
    description TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_model (model_type, model_id),
    INDEX idx_created (created_at)
);

-- =====================================================
-- INITIAL DATA INSERTS
-- =====================================================

-- Default Carriers
INSERT INTO carriers (name, code, logo, website, is_active, supports_api) VALUES
('DHL Express', 'DHL', 'carriers/dhl-logo.png', 'https://www.dhl.com', TRUE, FALSE),
('Post Office', 'USPS', 'carriers/usps-logo.png', 'https://www.usps.com', TRUE, FALSE),
('Custom Logistics', 'CUSTOM', 'carriers/custom-logo.png', NULL, TRUE, FALSE);

-- Default Admin User (password: admin123)
INSERT INTO users (name, email, password, role, is_active) VALUES
('Admin User', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', TRUE);

-- Default Site Settings
INSERT INTO site_settings (key_name, value, type, group_name, label, description, is_public) VALUES
-- Branding
('site_name', 'Atrix Logistics', 'string', 'branding', 'Site Name', 'Website name displayed in header and title', TRUE),
('site_title', 'Atrix Logistics - Professional Shipping Solutions', 'string', 'branding', 'Site Title', 'Default page title for SEO', TRUE),
('site_tagline', 'We ship anything, anywhere, anytime', 'string', 'branding', 'Site Tagline', 'Company tagline/slogan', TRUE),
('site_logo', 'assets/images/logo.png', 'image', 'branding', 'Site Logo', 'Main website logo', TRUE),
('site_favicon', 'assets/images/favicon.ico', 'image', 'branding', 'Favicon', 'Website favicon', TRUE),

-- Contact Information
('contact_email', '<EMAIL>', 'string', 'contact', 'Contact Email', 'Main contact email address', TRUE),
('contact_phone', '+****************', 'string', 'contact', 'Contact Phone', 'Main contact phone number', TRUE),
('contact_fax', '+****************', 'string', 'contact', 'Fax Number', 'Company fax number', TRUE),
('contact_address', '123 Logistics Ave, Transport City, TC 12345', 'text', 'contact', 'Company Address', 'Main company address', TRUE),
('business_hours', 'Monday - Friday: 8:00 AM - 6:00 PM\nSaturday: 9:00 AM - 4:00 PM\nSunday: Closed', 'text', 'contact', 'Business Hours', 'Operating hours', TRUE),

-- Social Media
('social_facebook', 'https://facebook.com/atrixlogistics', 'string', 'social', 'Facebook URL', 'Facebook page URL', TRUE),
('social_twitter', 'https://twitter.com/atrixlogistics', 'string', 'social', 'Twitter URL', 'Twitter profile URL', TRUE),
('social_linkedin', 'https://linkedin.com/company/atrixlogistics', 'string', 'social', 'LinkedIn URL', 'LinkedIn company page URL', TRUE),
('social_instagram', 'https://instagram.com/atrixlogistics', 'string', 'social', 'Instagram URL', 'Instagram profile URL', TRUE),

-- SEO Settings
('meta_description', 'Professional logistics and shipping services. We handle truck, air, and ocean freight with reliable tracking and competitive rates.', 'text', 'seo', 'Meta Description', 'Default meta description for SEO', FALSE),
('meta_keywords', 'logistics, shipping, freight, transportation, delivery, tracking', 'text', 'seo', 'Meta Keywords', 'Default meta keywords', FALSE),

-- System Settings
('tracking_prefix', 'ATX', 'string', 'system', 'Tracking Prefix', 'Tracking number prefix', FALSE),
('default_carrier_id', '3', 'integer', 'system', 'Default Carrier', 'Default carrier for new parcels', FALSE),
('quote_validity_days', '30', 'integer', 'system', 'Quote Validity', 'Number of days quotes remain valid', FALSE),
('enable_product_inquiries', '1', 'boolean', 'system', 'Enable Product Inquiries', 'Allow product inquiry modal', FALSE),

-- Homepage Content
('hero_title', 'Professional Logistics Solutions', 'string', 'homepage', 'Hero Title', 'Main hero section title', TRUE),
('hero_subtitle', 'We ship anything, anywhere, anytime with reliable tracking and competitive rates', 'text', 'homepage', 'Hero Subtitle', 'Hero section subtitle', TRUE),
('stats_projects', '90', 'integer', 'homepage', 'Successful Projects', 'Number of successful projects', TRUE),
('stats_clients', '2.6', 'string', 'homepage', 'Satisfied Clients', 'Number of satisfied clients (can include M, K suffixes)', TRUE),
('stats_staff', '350', 'integer', 'homepage', 'Experienced Staff', 'Number of experienced staff', TRUE),
('stats_vehicles', '10', 'integer', 'homepage', 'Owned Vehicles', 'Number of owned vehicles', TRUE);

-- Default Team Members
INSERT INTO team_members (name, position, department, bio, sort_order, is_active) VALUES
('John Smith', 'Chief Executive Officer', 'Management', 'With over 15 years in logistics, John leads our company with vision and expertise.', 1, TRUE),
('Sarah Johnson', 'Operations Manager', 'Operations', 'Sarah ensures smooth daily operations and customer satisfaction across all services.', 2, TRUE),
('Mike Wilson', 'Logistics Coordinator', 'Operations', 'Mike coordinates complex shipping solutions and manages carrier relationships.', 3, TRUE),
('Emily Davis', 'Customer Service Manager', 'Customer Service', 'Emily leads our customer service team to provide exceptional support.', 4, TRUE);

-- Default Company Branch (Headquarters)
INSERT INTO branches (name, address, city, state, postal_code, country, phone, email, manager_name, is_headquarters, is_active) VALUES
('Headquarters', '123 Logistics Ave', 'Transport City', 'TC', '12345', 'USA', '+****************', '<EMAIL>', 'John Smith', TRUE, TRUE);

-- Default Homepage Sliders
INSERT INTO sliders (title, subtitle, description, image, button_text, button_url, sort_order, is_active) VALUES
('Professional Logistics Solutions', 'Reliable Shipping Worldwide', 'We provide comprehensive logistics services including truck, air, and ocean freight with real-time tracking and competitive rates.', 'assets/images/slider/slider-1.jpg', 'Get Free Quote', '#quote-modal', 1, TRUE),
('Track Your Shipments', 'Real-time Package Tracking', 'Stay informed about your shipments with our advanced tracking system. Get updates from pickup to delivery.', 'assets/images/slider/slider-2.jpg', 'Track Now', '/track', 2, TRUE),
('Global Shipping Network', 'Worldwide Delivery Solutions', 'Our partnership with DHL, Post Office, and custom carriers ensures your packages reach any destination safely and on time.', 'assets/images/slider/slider-3.jpg', 'Learn More', '/services', 3, TRUE);
