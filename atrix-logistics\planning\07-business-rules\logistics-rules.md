# 📋 Business Rules - Atrix Logistics

## 🎯 Core Business Principles

### Company Mission
**Atrix Logistics** is a comprehensive trucking and logistics company that ships anything, anywhere, with optional partnerships with industry experts like DHL and Post Office for specialized services.

### Service Philosophy
- **Flexibility First:** We ship any type of goods
- **Customer Transparency:** Real-time tracking for all parcels
- **Multi-carrier Approach:** Leverage best carrier for each shipment
- **Technology Driven:** Modern web platform for all operations

---

## 🔐 User Access & Authentication Rules

### Admin Users
**Role:** `admin` or `staff`
**Access Level:** Full system access

**Permissions:**
- ✅ Create, edit, delete parcels
- ✅ Manage all website content
- ✅ View all customer orders
- ✅ Manage product catalog
- ✅ Access admin dashboard
- ✅ Manage user accounts
- ✅ View system reports

**Authentication Requirements:**
- Must have verified email address
- Strong password requirements (8+ chars, mixed case, numbers)
- Session timeout after 2 hours of inactivity
- Two-factor authentication recommended

### Customer Users
**Role:** `customer`
**Access Level:** Limited to own data

**Permissions:**
- ✅ Place product orders
- ✅ View own order history
- ✅ Track own parcels
- ✅ Update profile information
- ❌ Access admin functions
- ❌ View other customers' data

**Authentication Requirements:**
- Email verification required for registration
- Standard password requirements
- Optional profile completion

### Anonymous Users
**Role:** `guest`
**Access Level:** Public content only

**Permissions:**
- ✅ Browse products without purchasing
- ✅ Track parcels with tracking number
- ✅ Submit contact forms
- ✅ Request quotes
- ✅ View public content (about, services, blog)
- ❌ Place orders
- ❌ Save tracking preferences

---

## 📦 Parcel Management Rules

### Tracking Number Generation
**Format:** `ATX-YYYY-XXXXXXXX`
- **ATX:** Company prefix
- **YYYY:** Current year
- **XXXXXXXX:** 8-digit sequential number

**Rules:**
- Must be unique across all time
- Generated automatically on parcel creation
- Cannot be modified after creation
- Must be provided to customer immediately

### Parcel Status Workflow
**Status Progression:**
1. `pending` → Initial state when parcel is registered
2. `picked_up` → Parcel collected from sender
3. `in_transit` → Parcel in transportation
4. `out_for_delivery` → Parcel out for final delivery
5. `delivered` → Parcel successfully delivered
6. `exception` → Issue occurred (damage, delay, etc.)
7. `returned` → Parcel returned to sender

**Business Rules:**
- Status can only progress forward (except to exception/returned)
- Each status change must create a tracking event
- Delivered status requires delivery confirmation
- Exception status requires detailed explanation
- Returned status requires reason code

### Carrier Assignment Rules
**Default Carrier:** Custom Logistics (internal)
**Alternative Carriers:** DHL, Post Office

**Assignment Logic:**
1. **Domestic Standard:** Custom Logistics
2. **International Express:** DHL
3. **Domestic Economy:** Post Office
4. **Special Handling:** Admin discretion

**Rules:**
- Carrier cannot be changed once parcel is picked up
- Each carrier has different tracking capabilities
- Carrier assignment affects pricing and delivery time
- Admin can override automatic assignment

### Parcel Information Requirements
**Mandatory Fields:**
- Sender complete address and contact
- Recipient complete address and contact
- Package description (for customs/security)
- Declared value (for insurance)
- Service type selection

**Optional Fields:**
- Special handling instructions
- Insurance amount
- Cash on delivery (COD) amount
- Delivery signature requirements

**Validation Rules:**
- All addresses must include postal codes
- Phone numbers must be valid format
- Email addresses must be valid format
- Declared value must be positive number
- Weight and dimensions must be realistic

---

## 🛒 E-commerce Business Rules

### Product Management
**Product Status:**
- `draft` → Not visible to customers
- `published` → Available for purchase
- `archived` → No longer available but order history preserved

**Inventory Rules:**
- Stock quantity tracked automatically
- Out of stock products show "Coming Soon" message
- Low stock alerts for admin (threshold: 5 units)
- Negative inventory not allowed
- Reserved inventory for pending orders

**Pricing Rules:**
- All prices in USD
- Sale prices must be lower than regular price
- Price changes don't affect existing orders
- Bulk pricing discounts available (admin configured)

### Shopping Cart Rules
**Cart Behavior:**
- Anonymous users can add items to cart
- Cart persists for 7 days for anonymous users
- Registered users have permanent cart storage
- Cart automatically updates if product price changes
- Out of stock items automatically removed

**Checkout Requirements:**
- Must be registered user to complete purchase
- Valid billing and shipping addresses required
- Payment method validation (future implementation)
- Order confirmation email sent immediately

### Order Processing Workflow
**Order Status Progression:**
1. `pending` → Order placed, awaiting processing
2. `processing` → Order being prepared for shipment
3. `shipped` → Order shipped, tracking provided
4. `delivered` → Order delivered to customer
5. `cancelled` → Order cancelled (before shipping)
6. `refunded` → Order refunded (after delivery)

**Processing Rules:**
- Orders can only be cancelled before shipping
- Refunds require admin approval
- Shipped orders automatically create parcel records
- Order modifications not allowed after processing starts

---

## 📝 Content Management Rules

### CMS Content Sections
**Homepage Sections:**
- Hero banner with call-to-action
- Services overview
- Company statistics
- Featured testimonials
- Latest news/blog posts

**Content Update Rules:**
- Only admin users can modify content
- All changes logged with timestamp and user
- Content preview available before publishing
- Backup of previous version maintained
- SEO fields required for all public content

### Site Branding Management
**Admin Control Over Branding:**
- Logo upload and management (supports PNG, JPG, SVG)
- Site title and tagline editing
- Favicon management and upload
- Social media links management
- Contact information updates (email, phone, address)
- Business hours management

### Team Member Management
**Team Section Rules:**
- Admin can add/edit/remove team members
- Photo upload required for each member
- Position and department categorization
- Bio and contact information optional
- Sort order control for display
- Active/inactive status management
- Social media links per team member

### Branch/Location Management
**Multi-location Support:**
- Admin can manage multiple company branches
- Each branch has complete contact information
- Operating hours per location
- Services offered per branch
- Headquarters designation
- Manager assignment per branch
- Active/inactive status per location

### Homepage Slider Management
**Dynamic Slider Control:**
- Admin can add/edit/remove slider images
- Custom title, subtitle, and description per slide
- Call-to-action buttons with custom URLs
- Text positioning and overlay opacity control
- Sort order and active/inactive status
- Image optimization requirements (1920x1080 recommended)
- Mobile-responsive image handling

### Blog Management
**Publishing Rules:**
- Only admin/staff can create blog posts
- Draft posts not visible to public
- Published posts require featured image
- SEO meta data required for all posts
- Comments disabled (future feature)

**Content Guidelines:**
- Professional tone and language
- Logistics industry focus
- Regular publishing schedule recommended
- Image optimization required
- Mobile-friendly formatting

---

## 💬 Quote & Inquiry System Rules

### Quote Modal System
**Modal Accessibility:**
- "Get A Quote" button available in header navigation
- Quote modal accessible from any page
- No authentication required for quote requests
- Modal opens as overlay without page refresh
- Mobile-responsive modal design

**Quote Request Processing:**
- All quote requests stored in database
- Unique quote number generated (QTE-YYYY-XXXXXXXX)
- Auto-response email sent to requester
- Admin notification for new quote requests
- Quote validity period: 30 days default (admin configurable)

### Product Inquiry System
**Product Page Options:**
- Two buttons on product pages: "Add to Cart" and "Inquire"
- "Add to Cart" requires user authentication
- "Inquire" opens quote modal with product pre-filled
- Users can add multiple products to single inquiry
- Product details automatically populated in quote form

**Inquiry Processing Rules:**
- Product information captured at time of inquiry
- Quantity and special requirements noted
- Current product price stored for reference
- Admin can convert inquiries to formal quotes
- Follow-up tracking for all product inquiries

### Navigation Enhancement Rules
**Track Now Integration:**
- "Track Now" added to main navigation menu
- "Track Now" added to footer links
- Direct link to tracking page (/track)
- Consistent placement across all pages
- Mobile navigation includes "Track Now"

**Quote Button Integration:**
- "Get A Quote" button in header (desktop)
- Quote access in mobile menu
- Footer includes quote call-to-action
- Consistent styling across all instances

## 📞 Contact & Communication Rules

### Contact Form Processing
**Form Types:**
- General contact inquiries
- Free quote requests (general)
- Product-specific inquiries
- Customer support tickets
- Bulk quote requests (multiple products)

**Processing Rules:**
- All submissions stored in database
- Auto-response email sent to customer
- Admin notification for new submissions
- Response time target: 24 hours
- Follow-up tracking required

### Customer Communication
**Notification Types:**
- Order confirmation emails
- Shipping notifications
- Delivery confirmations
- Status update alerts

**Communication Rules:**
- All emails must include unsubscribe option
- Customer preferences respected
- Professional email templates
- Mobile-friendly email design

---

## 🔒 Security & Privacy Rules

### Data Protection
**Customer Data:**
- Minimal data collection principle
- Secure password storage (hashed)
- PCI compliance for payment data (future)
- Regular data backup procedures
- Data retention policies defined

**Access Control:**
- Role-based permissions strictly enforced
- Admin actions logged and auditable
- Regular security updates required
- Failed login attempt monitoring
- IP-based access restrictions for admin

### Privacy Compliance
**Information Handling:**
- Clear privacy policy displayed
- Cookie consent implementation
- Data sharing policies defined
- Customer data export capability
- Right to deletion honored

---

## 📊 Performance & Quality Rules

### System Performance
**Response Time Targets:**
- Homepage load: <2 seconds
- Tracking lookup: <1 second
- Admin dashboard: <3 seconds
- Product catalog: <2 seconds

**Availability Requirements:**
- 99.9% uptime target
- Scheduled maintenance windows
- Backup and disaster recovery plans
- Performance monitoring alerts

### Quality Assurance
**Testing Requirements:**
- All features tested before deployment
- Mobile responsiveness verified
- Cross-browser compatibility ensured
- SEO optimization validated
- Security vulnerability scanning

**Code Quality:**
- Laravel coding standards followed
- Code documentation required
- Version control best practices
- Regular code reviews conducted
