<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SmsService
{
    protected string $apiKey;
    protected string $apiSecret;
    protected string $sender;
    protected string $provider;

    public function __construct()
    {
        $this->apiKey = config('services.sms.api_key', '');
        $this->apiSecret = config('services.sms.api_secret', '');
        $this->sender = config('services.sms.sender', 'ATRIX');
        $this->provider = config('services.sms.provider', 'twilio'); // twilio, nexmo, textlocal
    }

    /**
     * Send SMS message
     */
    public function sendSms(string $phoneNumber, string $message): bool
    {
        try {
            // Clean phone number
            $phoneNumber = $this->cleanPhoneNumber($phoneNumber);
            
            // Validate phone number
            if (!$this->isValidPhoneNumber($phoneNumber)) {
                Log::error('Invalid phone number format', ['phone' => $phoneNumber]);
                return false;
            }

            // Send based on provider
            switch ($this->provider) {
                case 'twilio':
                    return $this->sendViaTwilio($phoneNumber, $message);
                case 'nexmo':
                    return $this->sendViaNexmo($phoneNumber, $message);
                case 'textlocal':
                    return $this->sendViaTextLocal($phoneNumber, $message);
                default:
                    // Fallback to mock for development
                    return $this->sendViaMock($phoneNumber, $message);
            }
        } catch (\Exception $e) {
            Log::error('SMS sending failed', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Generate and send OTP
     */
    public function sendOtp(string $phoneNumber, int $length = 6): ?string
    {
        $otp = $this->generateOtp($length);
        $message = "Your verification code is: {$otp}. Valid for 5 minutes. Do not share this code.";
        
        if ($this->sendSms($phoneNumber, $message)) {
            // Store OTP in cache for 5 minutes
            $cacheKey = "otp:{$phoneNumber}";
            Cache::put($cacheKey, $otp, now()->addMinutes(5));
            
            // Store attempt count
            $attemptKey = "otp_attempts:{$phoneNumber}";
            $attempts = Cache::get($attemptKey, 0) + 1;
            Cache::put($attemptKey, $attempts, now()->addHour());
            
            return $otp;
        }
        
        return null;
    }

    /**
     * Verify OTP
     */
    public function verifyOtp(string $phoneNumber, string $otp): bool
    {
        $cacheKey = "otp:{$phoneNumber}";
        $storedOtp = Cache::get($cacheKey);
        
        if ($storedOtp && $storedOtp === $otp) {
            // Remove OTP from cache after successful verification
            Cache::forget($cacheKey);
            Cache::forget("otp_attempts:{$phoneNumber}");
            return true;
        }
        
        return false;
    }

    /**
     * Check if phone number can receive OTP (rate limiting)
     */
    public function canSendOtp(string $phoneNumber): bool
    {
        $attemptKey = "otp_attempts:{$phoneNumber}";
        $attempts = Cache::get($attemptKey, 0);
        
        // Allow maximum 5 attempts per hour
        return $attempts < 5;
    }

    /**
     * Get remaining OTP attempts
     */
    public function getRemainingAttempts(string $phoneNumber): int
    {
        $attemptKey = "otp_attempts:{$phoneNumber}";
        $attempts = Cache::get($attemptKey, 0);
        return max(0, 5 - $attempts);
    }

    /**
     * Generate OTP
     */
    protected function generateOtp(int $length = 6): string
    {
        $otp = '';
        for ($i = 0; $i < $length; $i++) {
            $otp .= random_int(0, 9);
        }
        return $otp;
    }

    /**
     * Clean phone number
     */
    protected function cleanPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-numeric characters except +
        $cleaned = preg_replace('/[^\d+]/', '', $phoneNumber);
        
        // Add country code if missing (assuming US +1 for demo)
        if (!str_starts_with($cleaned, '+')) {
            if (strlen($cleaned) === 10) {
                $cleaned = '+1' . $cleaned;
            } elseif (strlen($cleaned) === 11 && str_starts_with($cleaned, '1')) {
                $cleaned = '+' . $cleaned;
            }
        }
        
        return $cleaned;
    }

    /**
     * Validate phone number format
     */
    protected function isValidPhoneNumber(string $phoneNumber): bool
    {
        // Basic validation for international format
        return preg_match('/^\+[1-9]\d{1,14}$/', $phoneNumber);
    }

    /**
     * Send via Twilio
     */
    protected function sendViaTwilio(string $phoneNumber, string $message): bool
    {
        if (empty($this->apiKey) || empty($this->apiSecret)) {
            return $this->sendViaMock($phoneNumber, $message);
        }

        try {
            $response = Http::withBasicAuth($this->apiKey, $this->apiSecret)
                ->asForm()
                ->post("https://api.twilio.com/2010-04-01/Accounts/{$this->apiKey}/Messages.json", [
                    'From' => $this->sender,
                    'To' => $phoneNumber,
                    'Body' => $message,
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Twilio SMS failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Send via Nexmo/Vonage
     */
    protected function sendViaNexmo(string $phoneNumber, string $message): bool
    {
        if (empty($this->apiKey) || empty($this->apiSecret)) {
            return $this->sendViaMock($phoneNumber, $message);
        }

        try {
            $response = Http::post('https://rest.nexmo.com/sms/json', [
                'api_key' => $this->apiKey,
                'api_secret' => $this->apiSecret,
                'from' => $this->sender,
                'to' => $phoneNumber,
                'text' => $message,
            ]);

            $data = $response->json();
            return isset($data['messages'][0]['status']) && $data['messages'][0]['status'] === '0';
        } catch (\Exception $e) {
            Log::error('Nexmo SMS failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Send via TextLocal
     */
    protected function sendViaTextLocal(string $phoneNumber, string $message): bool
    {
        if (empty($this->apiKey)) {
            return $this->sendViaMock($phoneNumber, $message);
        }

        try {
            $response = Http::asForm()->post('https://api.textlocal.in/send/', [
                'apikey' => $this->apiKey,
                'numbers' => $phoneNumber,
                'message' => $message,
                'sender' => $this->sender,
            ]);

            $data = $response->json();
            return isset($data['status']) && $data['status'] === 'success';
        } catch (\Exception $e) {
            Log::error('TextLocal SMS failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Mock SMS sending for development
     */
    protected function sendViaMock(string $phoneNumber, string $message): bool
    {
        Log::info('Mock SMS sent', [
            'phone' => $phoneNumber,
            'message' => $message,
        ]);
        
        // Always return true in development
        return true;
    }

    /**
     * Get SMS provider status
     */
    public function getProviderStatus(): array
    {
        return [
            'provider' => $this->provider,
            'configured' => !empty($this->apiKey),
            'sender' => $this->sender,
        ];
    }
}
