@extends('documents.base')

@section('title', 'Invoice #' . $order->order_number)
@section('document-title', 'INVOICE')

@section('header-right-extra')
    <div style="margin-top: 10px;">
        <span class="badge badge-{{ $order->payment_status === 'paid' ? 'success' : ($order->payment_status === 'pending' ? 'warning' : 'danger') }}">
            {{ ucfirst($order->payment_status) }}
        </span>
    </div>
@endsection

@section('content')
    <!-- Customer Information -->
    <div class="address-section">
        <div class="address-box">
            <div class="address-title">Bill To:</div>
            <div class="address-content">
                <strong>{{ $order->customer->name ?? 'Guest Customer' }}</strong><br>
                @if($order->customer && $order->customer->email)
                    {{ $order->customer->email }}<br>
                @endif
                @if($order->billingAddress)
                    {{ $order->billingAddress->address_line_1 }}<br>
                    @if($order->billingAddress->address_line_2)
                        {{ $order->billingAddress->address_line_2 }}<br>
                    @endif
                    {{ $order->billingAddress->city }}, {{ $order->billingAddress->state }} {{ $order->billingAddress->postal_code }}<br>
                    {{ $order->billingAddress->country }}<br>
                    @if($order->billingAddress->phone)
                        Phone: {{ $order->billingAddress->phone }}
                    @endif
                @endif
            </div>
        </div>
        <div class="address-box">
            <div class="address-title">Ship To:</div>
            <div class="address-content">
                @if($order->shippingAddress)
                    <strong>{{ $order->shippingAddress->first_name }} {{ $order->shippingAddress->last_name }}</strong><br>
                    {{ $order->shippingAddress->address_line_1 }}<br>
                    @if($order->shippingAddress->address_line_2)
                        {{ $order->shippingAddress->address_line_2 }}<br>
                    @endif
                    {{ $order->shippingAddress->city }}, {{ $order->shippingAddress->state }} {{ $order->shippingAddress->postal_code }}<br>
                    {{ $order->shippingAddress->country }}<br>
                    @if($order->shippingAddress->phone)
                        Phone: {{ $order->shippingAddress->phone }}
                    @endif
                @else
                    Same as billing address
                @endif
            </div>
        </div>
    </div>

    <!-- Order Information -->
    <div class="info-box">
        <div class="info-box-title">Order Information</div>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Order Number:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $order->order_number }}</td>
                <td style="border: none; padding: 5px 0; width: 25%;"><strong>Order Date:</strong></td>
                <td style="border: none; padding: 5px 0; width: 25%;">{{ $order->created_at->format('M j, Y') }}</td>
            </tr>
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Payment Method:</strong></td>
                <td style="border: none; padding: 5px 0;">{{ ucfirst($order->payment_method) }}</td>
                <td style="border: none; padding: 5px 0;"><strong>Order Status:</strong></td>
                <td style="border: none; padding: 5px 0;">
                    <span class="badge badge-{{ $order->status === 'completed' ? 'success' : ($order->status === 'processing' ? 'info' : 'warning') }}">
                        {{ ucfirst($order->status) }}
                    </span>
                </td>
            </tr>
            @if($order->notes)
            <tr>
                <td style="border: none; padding: 5px 0;"><strong>Notes:</strong></td>
                <td style="border: none; padding: 5px 0;" colspan="3">{{ $order->notes }}</td>
            </tr>
            @endif
        </table>
    </div>

    <!-- Order Items -->
    <div class="section">
        <div class="section-title">Order Items</div>
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 10%;">#</th>
                    <th style="width: 40%;">Product</th>
                    <th style="width: 15%; text-align: center;">Quantity</th>
                    <th style="width: 15%; text-align: right;">Unit Price</th>
                    <th style="width: 20%; text-align: right;">Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach($order->items as $index => $item)
                <tr>
                    <td>{{ $index + 1 }}</td>
                    <td>
                        <strong>{{ $item->product->name }}</strong><br>
                        @if($item->product->sku)
                            <small class="text-muted">SKU: {{ $item->product->sku }}</small>
                        @endif
                    </td>
                    <td class="text-center">{{ $item->quantity }}</td>
                    <td class="text-right">@currency($item->unit_price)</td>
                    <td class="text-right">@currency($item->total_price)</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <!-- Order Totals -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="total-label">Subtotal:</td>
                <td class="total-value">@currency($order->subtotal)</td>
            </tr>
            @if($order->tax_amount > 0)
            <tr>
                <td class="total-label">Tax:</td>
                <td class="total-value">@currency($order->tax_amount)</td>
            </tr>
            @endif
            @if($order->shipping_amount > 0)
            <tr>
                <td class="total-label">Shipping:</td>
                <td class="total-value">@currency($order->shipping_amount)</td>
            </tr>
            @endif
            @if($order->discount_amount > 0)
            <tr>
                <td class="total-label">Discount:</td>
                <td class="total-value">-@currency($order->discount_amount)</td>
            </tr>
            @endif
            <tr class="grand-total">
                <td class="total-label">Total Amount:</td>
                <td class="total-value">@currency($order->total_amount)</td>
            </tr>
        </table>
    </div>

    <!-- Payment Information -->
    @if($order->payment_status === 'paid')
    <div class="section">
        <div class="section-title">Payment Information</div>
        <div class="info-box">
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="border: none; padding: 5px 0; width: 25%;"><strong>Payment Status:</strong></td>
                    <td style="border: none; padding: 5px 0; width: 25%;">
                        <span class="badge badge-success">Paid</span>
                    </td>
                    <td style="border: none; padding: 5px 0; width: 25%;"><strong>Payment Date:</strong></td>
                    <td style="border: none; padding: 5px 0; width: 25%;">{{ $order->updated_at->format('M j, Y') }}</td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Payment Method:</strong></td>
                    <td style="border: none; padding: 5px 0;" colspan="3">{{ ucfirst($order->payment_method) }}</td>
                </tr>
            </table>
        </div>
    </div>
    @endif

    <!-- Terms and Conditions -->
    <div class="section">
        <div class="section-title">Terms & Conditions</div>
        <div style="font-size: 10px; line-height: 1.4; color: #666;">
            <p><strong>Payment Terms:</strong> Payment is due within 30 days of invoice date unless otherwise specified.</p>
            <p><strong>Returns:</strong> Items may be returned within 30 days of delivery in original condition.</p>
            <p><strong>Shipping:</strong> We are not responsible for delays caused by shipping carriers.</p>
            <p><strong>Warranty:</strong> All products come with manufacturer warranty as specified.</p>
            @if(isset($siteSettings['invoice_terms']))
                <p>{{ $siteSettings['invoice_terms'] }}</p>
            @endif
        </div>
    </div>
@endsection

@section('footer')
    <div style="text-align: center; margin-bottom: 10px;">
        <strong>Thank you for your business!</strong>
    </div>
    <div style="text-align: center; font-size: 10px;">
        For questions about this invoice, please contact us at {{ $siteSettings['company_email'] ?? '<EMAIL>' }}
    </div>
@endsection
