@extends('layouts.customer')

@section('title', 'My Profile')
@section('page-title', 'My Profile')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('customer.dashboard') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
        <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Profile
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        Personal Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Full Name</label>
                            <p class="mb-0">{{ $user->name }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Email Address</label>
                            <p class="mb-0">
                                {{ $user->email }}
                                @if($user->email_verified_at)
                                    <span class="badge bg-success ms-2">Verified</span>
                                @else
                                    <span class="badge bg-warning ms-2">Unverified</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Phone Number</label>
                            <p class="mb-0">{{ $user->phone ?: 'Not provided' }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Company Name</label>
                            <p class="mb-0">{{ $user->company_name ?: 'Not provided' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Address Information
                    </h5>
                </div>
                <div class="card-body">
                    @if($user->address || $user->city || $user->state || $user->postal_code || $user->country)
                        <div class="row">
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold">Street Address</label>
                                <p class="mb-0">{{ $user->address ?: 'Not provided' }}</p>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">City</label>
                                <p class="mb-0">{{ $user->city ?: 'Not provided' }}</p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">State/Province</label>
                                <p class="mb-0">{{ $user->state ?: 'Not provided' }}</p>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label fw-bold">Postal Code</label>
                                <p class="mb-0">{{ $user->postal_code ?: 'Not provided' }}</p>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Country</label>
                                <p class="mb-0">{{ $user->country ?: 'Not provided' }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Full Address</label>
                                <p class="mb-0">{{ $user->full_address ?: 'Not provided' }}</p>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No address information provided</h6>
                            <p class="text-muted">Add your address to make shipping easier.</p>
                            <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                Add Address
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Account Security -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Account Security
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Password</label>
                            <p class="mb-0">••••••••••••</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">Last Updated</label>
                            <p class="mb-0">{{ $user->updated_at->format('M d, Y h:i A') }}</p>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <a href="{{ route('customer.profile.change-password') }}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>
                            Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('customer.profile.edit') }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Profile
                        </a>
                        <a href="{{ route('customer.profile.change-password') }}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>
                            Change Password
                        </a>
                        <a href="{{ route('customer.profile.preferences') }}" class="btn btn-outline-info">
                            <i class="fas fa-cog me-2"></i>
                            Preferences
                        </a>
                        <a href="{{ route('customer.profile.address-book') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-address-book me-2"></i>
                            Address Book
                        </a>
                    </div>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        Account Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <h4 class="text-primary">{{ $user->parcels()->count() }}</h4>
                            <small class="text-muted">Total Parcels</small>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-success">${{ number_format($user->parcels()->where('is_paid', true)->sum('total_cost'), 2) }}</h4>
                            <small class="text-muted">Total Spent</small>
                        </div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-info">{{ $user->parcels()->where('status', 'delivered')->count() }}</h4>
                            <small class="text-muted">Delivered</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-warning">{{ $user->parcels()->whereIn('status', ['picked_up', 'in_transit', 'out_for_delivery'])->count() }}</h4>
                            <small class="text-muted">In Transit</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Account Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Account Type</label>
                        <p class="mb-0">
                            <span class="badge bg-primary">{{ ucfirst($user->role) }}</span>
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">Account Status</label>
                        <p class="mb-0">
                            @if($user->is_active)
                                <span class="badge bg-success">Active</span>
                            @else
                                <span class="badge bg-danger">Inactive</span>
                            @endif
                        </p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">Member Since</label>
                        <p class="mb-0">{{ $user->created_at->format('M d, Y') }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label fw-bold">Customer ID</label>
                        <p class="mb-0 font-monospace">#{{ str_pad($user->id, 6, '0', STR_PAD_LEFT) }}</p>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Danger Zone
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small">
                        Once you delete your account, there is no going back. Please be certain.
                    </p>
                    <button class="btn btn-outline-danger btn-sm" onclick="confirmAccountDeletion()">
                        <i class="fas fa-trash me-2"></i>
                        Delete Account
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Account Modal -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Account
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p><strong>Are you sure you want to delete your account?</strong></p>
                    <p class="text-muted">This action cannot be undone. All your data will be permanently removed.</p>
                    
                    <form method="POST" action="{{ route('customer.profile.delete') }}" id="deleteAccountForm">
                        @csrf
                        @method('DELETE')
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">Enter your password to confirm:</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" form="deleteAccountForm" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>
                        Delete My Account
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function confirmAccountDeletion() {
        new bootstrap.Modal(document.getElementById('deleteAccountModal')).show();
    }
</script>
@endpush
