@extends('layouts.admin')

@section('page-title', 'Live Chat Session')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.communications.live-chat.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Chat List
        </a>
        @if($session->status !== 'closed')
        <button type="button" class="btn btn-outline-danger" onclick="closeSession()">
            <i class="fas fa-times me-1"></i> Close Chat
        </button>
        @else
        <button type="button" class="btn btn-outline-success" onclick="reopenSession()">
            <i class="fas fa-redo me-1"></i> Reopen Chat
        </button>
        @endif
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Chat Messages -->
        <div class="col-lg-8">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-comments me-2"></i>
                            Chat with {{ $session->visitor_name ?? 'Anonymous Visitor' }}
                        </h5>
                        <span class="badge bg-{{ $session->status === 'active' ? 'success' : ($session->status === 'waiting' ? 'warning' : 'secondary') }}">
                            {{ ucfirst($session->status) }}
                        </span>
                    </div>
                </div>
                <div class="card-body d-flex flex-column" style="height: 500px;">
                    <!-- Messages Container -->
                    <div class="flex-grow-1 overflow-auto mb-3" id="messagesContainer" style="max-height: 400px;">
                        @foreach($session->messages as $message)
                        <div class="message-item mb-3 {{ $message->isFromStaff() ? 'text-end' : '' }}">
                            <div class="d-inline-block p-3 rounded {{ $message->isFromStaff() ? 'bg-primary text-white' : 'bg-light' }}" 
                                 style="max-width: 70%;">
                                <div class="message-content">{{ $message->message }}</div>
                                <small class="d-block mt-1 {{ $message->isFromStaff() ? 'text-white-50' : 'text-muted' }}">
                                    {{ $message->sender_name }} • {{ $message->created_at->format('M j, Y g:i A') }}
                                </small>
                            </div>
                        </div>
                        @endforeach
                    </div>

                    <!-- Message Input -->
                    @if($session->status !== 'closed')
                    <div class="border-top pt-3">
                        <form id="messageForm" onsubmit="sendMessage(event)">
                            <div class="input-group">
                                <input type="text" class="form-control" id="messageInput" 
                                       placeholder="Type your message..." required>
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    @else
                    <div class="border-top pt-3 text-center">
                        <div class="alert alert-warning mb-3">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Chat Session Closed</strong>
                            <p class="mb-2 mt-2">This chat session has been closed. The visitor can still send messages, but staff cannot respond.</p>
                            <button type="button" class="btn btn-success btn-sm" onclick="reopenSession()">
                                <i class="fas fa-redo me-1"></i> Reopen Chat Session
                            </button>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Session Info -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Session Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Session ID:</label>
                        <div class="text-muted">{{ $session->session_id }}</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Visitor Name:</label>
                        <div class="text-muted">{{ $session->visitor_name ?? 'Not provided' }}</div>
                    </div>

                    @if($session->visitor_email)
                    <div class="mb-3">
                        <label class="form-label fw-bold">Email:</label>
                        <div class="text-muted">
                            <a href="mailto:{{ $session->visitor_email }}">{{ $session->visitor_email }}</a>
                        </div>
                    </div>
                    @endif

                    <div class="mb-3">
                        <label class="form-label fw-bold">Status:</label>
                        <div>
                            <span class="badge bg-{{ $session->status === 'active' ? 'success' : ($session->status === 'waiting' ? 'warning' : 'secondary') }}">
                                {{ ucfirst($session->status) }}
                            </span>
                        </div>
                    </div>

                    @if($session->assignedStaff)
                    <div class="mb-3">
                        <label class="form-label fw-bold">Assigned to:</label>
                        <div class="text-muted">{{ $session->assignedStaff->name }}</div>
                    </div>
                    @endif

                    <div class="mb-3">
                        <label class="form-label fw-bold">Started:</label>
                        <div class="text-muted">{{ $session->created_at->format('M j, Y g:i A') }}</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">Last Activity:</label>
                        <div class="text-muted">{{ $session->last_activity?->diffForHumans() ?? 'No activity' }}</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">IP Address:</label>
                        <div class="text-muted">{{ $session->visitor_ip ?? 'Not recorded' }}</div>
                    </div>

                    <!-- Assignment Actions -->
                    @if($session->status !== 'closed')
                    <div class="border-top pt-3">
                        @if(!$session->assignedStaff)
                        <button type="button" class="btn btn-success btn-sm w-100" onclick="assignToMe()">
                            <i class="fas fa-user-plus me-1"></i> Assign to Me
                        </button>
                        @elseif($session->assigned_to !== auth()->id())
                        <button type="button" class="btn btn-warning btn-sm w-100" onclick="assignToMe()">
                            <i class="fas fa-user-edit me-1"></i> Take Over Chat
                        </button>
                        @endif
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            @if($session->status !== 'closed')
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Responses
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="insertQuickResponse('Hello! How can I help you today?')">
                            Greeting
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="insertQuickResponse('Thank you for contacting us. Let me check that for you.')">
                            Checking
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="insertQuickResponse('Is there anything else I can help you with?')">
                            Follow-up
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                onclick="insertQuickResponse('Thank you for choosing our services. Have a great day!')">
                            Closing
                        </button>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
@endsection

@push('scripts')
<script>
let lastMessageId = {{ $session->messages->last()?->id ?? 0 }};
let pollingInterval = null;

// Send message
function sendMessage(event) {
    event.preventDefault();
    
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message) return;
    
    fetch('{{ route("admin.communications.live-chat.send-message", $session) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ message: message })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            messageInput.value = '';
            addMessageToChat(data.data, true);
            lastMessageId = data.data.id;
        } else {
            alert('Error sending message');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error sending message');
    });
}

// Add message to chat
function addMessageToChat(message, isFromStaff = false) {
    const messagesContainer = document.getElementById('messagesContainer');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-item mb-3 ${isFromStaff ? 'text-end' : ''}`;
    
    messageDiv.innerHTML = `
        <div class="d-inline-block p-3 rounded ${isFromStaff ? 'bg-primary text-white' : 'bg-light'}" 
             style="max-width: 70%;">
            <div class="message-content">${message.message}</div>
            <small class="d-block mt-1 ${isFromStaff ? 'text-white-50' : 'text-muted'}">
                ${message.sender_name} • ${new Date(message.created_at).toLocaleString()}
            </small>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

// Poll for new messages
function pollForNewMessages() {
    fetch(`{{ route("admin.communications.live-chat.get-new-messages", $session) }}?last_message_id=${lastMessageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.new_messages.length > 0) {
                data.new_messages.forEach(message => {
                    addMessageToChat(message, false);
                    lastMessageId = message.id;
                });
            }
        })
        .catch(error => {
            console.error('Error polling for messages:', error);
        });
}

// Assign session to current user
function assignToMe() {
    fetch('{{ route("admin.communications.live-chat.assign", $session) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error assigning chat session');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error assigning chat session');
    });
}

// Close session
function closeSession() {
    if (confirm('Are you sure you want to close this chat session?')) {
        fetch('{{ route("admin.communications.live-chat.close", $session) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.href = '{{ route("admin.communications.live-chat.index") }}';
            } else {
                alert('Error closing chat session');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error closing chat session');
        });
    }
}

// Reopen session
function reopenSession() {
    if (confirm('Are you sure you want to reopen this chat session? This will make it active again and assign it to you.')) {
        fetch('{{ route("admin.communications.live-chat.reopen", $session) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to reopen chat session'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error reopening chat session');
        });
    }
}

// Insert quick response
function insertQuickResponse(text) {
    document.getElementById('messageInput').value = text;
    document.getElementById('messageInput').focus();
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Scroll to bottom of messages
    const messagesContainer = document.getElementById('messagesContainer');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
    
    // Start polling for new messages every 3 seconds
    @if($session->status !== 'closed')
    pollingInterval = setInterval(pollForNewMessages, 3000);
    @endif
    
    // Focus on message input
    document.getElementById('messageInput')?.focus();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
});
</script>
@endpush
