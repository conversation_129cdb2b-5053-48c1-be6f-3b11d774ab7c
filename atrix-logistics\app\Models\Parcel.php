<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Parcel extends Model
{
    protected $fillable = [
        'tracking_number',
        'carrier_id',
        'user_id',
        'sender_name',
        'sender_email',
        'sender_phone',
        'sender_address',
        'sender_city',
        'sender_state',
        'sender_postal_code',
        'sender_country',
        'recipient_name',
        'recipient_email',
        'recipient_phone',
        'recipient_address',
        'recipient_city',
        'recipient_state',
        'recipient_postal_code',
        'recipient_country',
        'description',
        'weight',
        'dimensions',
        'declared_value',
        'service_type',
        'status',
        'special_instructions',
        'shipped_at',
        'delivered_at',
        'estimated_delivery_date',
        'shipping_cost',
        'insurance_cost',
        'total_cost',
        'is_paid',
    ];

    protected $casts = [
        'weight' => 'decimal:2',
        'declared_value' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'insurance_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'is_paid' => 'boolean',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'estimated_delivery_date' => 'date',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($parcel) {
            if (empty($parcel->tracking_number)) {
                $parcel->tracking_number = static::generateTrackingNumber();
            }
        });
    }

    /**
     * Generate a unique tracking number
     */
    public static function generateTrackingNumber(): string
    {
        $prefix = config('app.tracking_prefix', 'ATX');
        $year = date('Y');

        do {
            $sequence = str_pad(mt_rand(1, 99999999), 8, '0', STR_PAD_LEFT);
            $trackingNumber = "{$prefix}-{$year}-{$sequence}";
        } while (static::where('tracking_number', $trackingNumber)->exists());

        return $trackingNumber;
    }

    /**
     * Get the carrier for this parcel
     */
    public function carrier(): BelongsTo
    {
        return $this->belongsTo(Carrier::class);
    }

    /**
     * Get the user who created this parcel
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all tracking events for this parcel
     */
    public function trackingEvents(): HasMany
    {
        return $this->hasMany(TrackingEvent::class)->orderBy('event_date', 'desc');
    }

    /**
     * Get public tracking events only
     */
    public function publicTrackingEvents(): HasMany
    {
        return $this->hasMany(TrackingEvent::class)
                    ->where('is_public', true)
                    ->orderBy('event_date', 'desc');
    }

    /**
     * Get the payments for this parcel
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scope to filter by status
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get delivered parcels
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    /**
     * Scope to get parcels in transit
     */
    public function scopeInTransit($query)
    {
        return $query->whereIn('status', ['picked_up', 'in_transit', 'out_for_delivery']);
    }

    /**
     * Check if parcel is delivered
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Get status badge color
     */
    public function getStatusBadgeColor(): string
    {
        return match($this->status) {
            'pending' => 'secondary',
            'picked_up' => 'info',
            'in_transit' => 'primary',
            'out_for_delivery' => 'warning',
            'delivered' => 'success',
            'exception' => 'danger',
            'returned' => 'dark',
            default => 'secondary',
        };
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatus(): string
    {
        return Str::title(str_replace('_', ' ', $this->status));
    }
}
