<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->string('icon')->nullable()->after('image');
            $table->boolean('is_featured')->default(false)->after('is_active');

            // SEO fields
            $table->string('meta_title')->nullable()->after('is_featured');
            $table->text('meta_description')->nullable()->after('meta_title');
            $table->text('meta_keywords')->nullable()->after('meta_description');

            $table->softDeletes()->after('updated_at');

            // Add indexes
            $table->index(['parent_id', 'is_active']);
            $table->index(['is_active', 'is_featured']);
            $table->index('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('categories', function (Blueprint $table) {
            $table->dropColumn([
                'icon',
                'is_featured',
                'meta_title',
                'meta_description',
                'meta_keywords'
            ]);
            $table->dropSoftDeletes();
            $table->dropIndex(['parent_id', 'is_active']);
            $table->dropIndex(['is_active', 'is_featured']);
            $table->dropIndex(['sort_order']);
        });
    }
};
