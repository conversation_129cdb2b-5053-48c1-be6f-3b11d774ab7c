<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\NewsletterSubscriber;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class NewsletterController extends Controller
{
    /**
     * Display a listing of newsletter subscribers
     */
    public function index(Request $request): View
    {
        $query = NewsletterSubscriber::latest();

        // Apply filters
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'unsubscribed') {
                $query->unsubscribed();
            } else {
                $query->where('status', $request->status);
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('email', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%");
            });
        }

        $subscribers = $query->paginate(20);

        // Get statistics
        $stats = [
            'total' => NewsletterSubscriber::count(),
            'active' => NewsletterSubscriber::active()->count(),
            'unsubscribed' => NewsletterSubscriber::unsubscribed()->count(),
            'bounced' => NewsletterSubscriber::where('status', 'bounced')->count(),
        ];

        return view('admin.communications.newsletter.index', compact('subscribers', 'stats'));
    }

    /**
     * Show the form for creating a new subscriber
     */
    public function create(): View
    {
        return view('admin.communications.newsletter.create');
    }

    /**
     * Store a newly created subscriber
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'email' => 'required|email|unique:newsletter_subscribers,email',
            'name' => 'nullable|string|max:255',
            'status' => 'required|in:active,unsubscribed,bounced',
        ]);

        $validated['subscription_source'] = 'admin';
        $validated['ip_address'] = $request->ip();
        $validated['user_agent'] = $request->userAgent();

        NewsletterSubscriber::create($validated);

        return redirect()->route('admin.communications.newsletter.index')
                        ->with('success', 'Subscriber added successfully.');
    }

    /**
     * Display the specified subscriber
     */
    public function show(NewsletterSubscriber $newsletter): View
    {
        return view('admin.communications.newsletter.show', compact('newsletter'));
    }

    /**
     * Show the form for editing the specified subscriber
     */
    public function edit(NewsletterSubscriber $newsletter): View
    {
        return view('admin.communications.newsletter.edit', compact('newsletter'));
    }

    /**
     * Update the specified subscriber
     */
    public function update(Request $request, NewsletterSubscriber $newsletter): RedirectResponse
    {
        $validated = $request->validate([
            'email' => 'required|email|unique:newsletter_subscribers,email,' . $newsletter->id,
            'name' => 'nullable|string|max:255',
            'status' => 'required|in:active,unsubscribed,bounced',
        ]);

        $newsletter->update($validated);

        return redirect()->route('admin.communications.newsletter.index')
                        ->with('success', 'Subscriber updated successfully.');
    }

    /**
     * Remove the specified subscriber
     */
    public function destroy(NewsletterSubscriber $newsletter): RedirectResponse
    {
        $newsletter->delete();

        return redirect()->route('admin.communications.newsletter.index')
                        ->with('success', 'Subscriber deleted successfully.');
    }

    /**
     * Bulk import subscribers
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'emails' => 'required|string',
        ]);

        $emails = array_filter(array_map('trim', explode("\n", $request->emails)));
        $imported = 0;
        $errors = [];

        foreach ($emails as $email) {
            $validator = Validator::make(['email' => $email], [
                'email' => 'required|email|unique:newsletter_subscribers,email'
            ]);

            if ($validator->passes()) {
                NewsletterSubscriber::create([
                    'email' => $email,
                    'subscription_source' => 'admin_import',
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                $imported++;
            } else {
                $errors[] = $email;
            }
        }

        $message = "Imported {$imported} subscribers successfully.";
        if (!empty($errors)) {
            $message .= " " . count($errors) . " emails were skipped (invalid or already exist).";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Bulk actions
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,unsubscribe,delete',
            'subscribers' => 'required|array',
            'subscribers.*' => 'exists:newsletter_subscribers,id',
        ]);

        $subscribers = NewsletterSubscriber::whereIn('id', $validated['subscribers']);
        $count = $subscribers->count();

        switch ($validated['action']) {
            case 'activate':
                $subscribers->update(['status' => 'active', 'unsubscribed_at' => null]);
                $message = "{$count} subscribers activated.";
                break;
            case 'unsubscribe':
                $subscribers->update(['status' => 'unsubscribed', 'unsubscribed_at' => now()]);
                $message = "{$count} subscribers unsubscribed.";
                break;
            case 'delete':
                $subscribers->delete();
                $message = "{$count} subscribers deleted.";
                break;
        }

        return redirect()->back()->with('success', $message);
    }
}
