@extends('layouts.customer')

@section('title', 'Payment - Order #' . $order->order_number)
@section('page-title', 'Secure Payment')

@section('content')
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Payment Security Notice -->
        <div class="alert alert-info mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-shield-alt fa-2x me-3"></i>
                <div>
                    <h6 class="mb-1">🔒 Secure Payment Processing</h6>
                    <p class="mb-0">Your payment information is processed securely through industry-standard encryption and PCI-compliant payment gateways. We never store your card details.</p>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    Order Summary
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">Order Details</h6>
                        <p class="mb-1"><strong>Order Number:</strong> {{ $order->order_number }}</p>
                        <p class="mb-1"><strong>Order Date:</strong> {{ $order->created_at->format('M d, Y') }}</p>
                        <p class="mb-1"><strong>Status:</strong> 
                            <span class="badge bg-{{ $order->status_badge_color }}">{{ $order->formatted_status }}</span>
                        </p>
                        <p class="mb-0"><strong>Items:</strong> {{ $order->items->count() }} item(s)</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">Payment Information</h6>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span>@currency($order->subtotal)</span>
                        </div>
                        @if($order->shipping_amount > 0)
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping:</span>
                            <span>@currency($order->shipping_amount)</span>
                        </div>
                        @endif
                        @if($order->tax_amount > 0)
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax:</span>
                            <span>@currency($order->tax_amount)</span>
                        </div>
                        @endif
                        @if($order->discount_amount > 0)
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>Discount:</span>
                            <span>-@currency($order->discount_amount)</span>
                        </div>
                        @endif
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Total Amount:</strong>
                            <strong class="h5 text-primary">@currency($order->total_amount)</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    Payment Method
                </h5>
            </div>
            <div class="card-body">
                <form id="paymentForm" action="{{ route('customer.orders.pay.process', $order) }}" method="POST">
                    @csrf
                    
                    @if(count($paymentMethods) > 0)
                        <div class="row">
                            @foreach($paymentMethods as $method => $config)
                                @if($config['enabled'])
                                <div class="col-md-6 mb-3">
                                    <div class="payment-method-card">
                                        <input type="radio" class="btn-check" name="payment_method" id="method_{{ $method }}" value="{{ $method }}" {{ $loop->first ? 'checked' : '' }}>
                                        <label class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-4" for="method_{{ $method }}">
                                            <i class="{{ $config['icon'] }} fa-2x mb-2"></i>
                                            <h6 class="mb-1">{{ $config['name'] }}</h6>
                                            <small class="text-muted text-center">{{ $config['description'] }}</small>
                                            @if($config['supports_3ds'])
                                                <span class="badge bg-success mt-2">
                                                    <i class="fas fa-shield-alt me-1"></i>3D Secure
                                                </span>
                                            @endif
                                        </label>
                                    </div>
                                </div>
                                @endif
                            @endforeach
                        </div>

                        <!-- Payment Method Specific Information -->
                        <div class="mt-4">
                            <div id="stripe-info" class="payment-info" style="display: none;">
                                <div class="alert alert-light">
                                    <h6><i class="fab fa-cc-stripe me-2"></i>Stripe Payment</h6>
                                    <p class="mb-2">Secure card payment processed by Stripe. Supports all major credit and debit cards with 3D Secure authentication.</p>
                                </div>

                                <!-- Stripe Elements Container -->
                                <div id="stripe-elements-container" style="display: none;">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="mb-3">Enter Card Details</h6>
                                            <div id="card-element" class="form-control" style="height: 40px; padding: 10px;">
                                                <!-- Stripe Elements will create form elements here -->
                                            </div>
                                            <div id="card-errors" role="alert" class="text-danger mt-2"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="paypal-info" class="payment-info" style="display: none;">
                                <div class="alert alert-light">
                                    <h6><i class="fab fa-paypal me-2"></i>PayPal Payment</h6>
                                    <p class="mb-2">Pay securely with your PayPal account or credit card through PayPal's secure checkout.</p>
                                </div>

                                <!-- PayPal Button Container -->
                                <div id="paypal-button-container" style="display: none;">
                                    <!-- PayPal button will be rendered here -->
                                </div>

                                <!-- PayPal Manual Transfer Details -->
                                @if(\App\Models\SiteSetting::getValue('paypal_email'))
                                <div class="card mt-3">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0"><i class="fab fa-paypal me-2"></i>Alternative: PayPal Manual Transfer</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-muted mb-3">If you prefer to send payment manually via PayPal, you can use the details below:</p>
                                        <div class="row">
                                            <div class="col-md-6 mb-2">
                                                <strong>PayPal Email:</strong><br>
                                                <span class="text-primary">{{ \App\Models\SiteSetting::getValue('paypal_email') }}</span>
                                                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ \App\Models\SiteSetting::getValue('paypal_email') }}', 'PayPal email')">
                                                    <i class="fas fa-copy"></i>
                                                </button>
                                            </div>

                                            @if(\App\Models\SiteSetting::getValue('paypal_account_name'))
                                            <div class="col-md-6 mb-2">
                                                <strong>Account Name:</strong><br>
                                                <span class="text-primary">{{ \App\Models\SiteSetting::getValue('paypal_account_name') }}</span>
                                            </div>
                                            @endif
                                        </div>

                                        @if(\App\Models\SiteSetting::getValue('paypal_instructions'))
                                        <div class="alert alert-info mt-3 mb-0">
                                            <small><strong>Instructions:</strong> {{ \App\Models\SiteSetting::getValue('paypal_instructions') }}</small>
                                        </div>
                                        @endif

                                        <div class="alert alert-warning mt-3 mb-0">
                                            <small><strong>Note:</strong> Include order number <strong>{{ $order->order_number }}</strong> in the payment note. Manual transfers require admin verification.</small>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <div id="razorpay-info" class="payment-info" style="display: none;">
                                <div class="alert alert-light">
                                    <h6><i class="fas fa-credit-card me-2"></i>Razorpay Payment</h6>
                                    <p class="mb-0">Supports UPI, Credit/Debit Cards, Net Banking, and Digital Wallets. Secure payment processing for India.</p>
                                </div>
                            </div>

                            <div id="square-info" class="payment-info" style="display: none;">
                                <div class="alert alert-light">
                                    <h6><i class="fas fa-credit-card me-2"></i>Square Payment</h6>
                                    <p class="mb-0">Secure card payment processed by Square with advanced fraud protection and 3D Secure.</p>
                                </div>
                            </div>

                            <div id="admin_approval-info" class="payment-info" style="display: none;">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-university me-2"></i>Bank Transfer / Manual Payment</h6>
                                    <p class="mb-3">Choose this option to pay via bank transfer or other offline methods.</p>

                                    <!-- Bank Account Details -->
                                    @if(\App\Models\SiteSetting::getValue('bank_name') || \App\Models\SiteSetting::getValue('bank_account_number'))
                                    <div class="card mb-3">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0"><i class="fas fa-university me-2"></i>Bank Transfer Details</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                @if(\App\Models\SiteSetting::getValue('bank_name'))
                                                <div class="col-md-6 mb-2">
                                                    <strong>Bank Name:</strong><br>
                                                    <span class="text-primary">{{ \App\Models\SiteSetting::getValue('bank_name') }}</span>
                                                </div>
                                                @endif

                                                @if(\App\Models\SiteSetting::getValue('bank_account_name'))
                                                <div class="col-md-6 mb-2">
                                                    <strong>Account Holder:</strong><br>
                                                    <span class="text-primary">{{ \App\Models\SiteSetting::getValue('bank_account_name') }}</span>
                                                </div>
                                                @endif

                                                @if(\App\Models\SiteSetting::getValue('bank_account_number'))
                                                <div class="col-md-6 mb-2">
                                                    <strong>Account Number:</strong><br>
                                                    <code class="bg-light p-1">{{ \App\Models\SiteSetting::getValue('bank_account_number') }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ \App\Models\SiteSetting::getValue('bank_account_number') }}', 'Account number')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                @endif

                                                @if(\App\Models\SiteSetting::getValue('bank_routing_number'))
                                                <div class="col-md-6 mb-2">
                                                    <strong>Routing Number:</strong><br>
                                                    <code class="bg-light p-1">{{ \App\Models\SiteSetting::getValue('bank_routing_number') }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ \App\Models\SiteSetting::getValue('bank_routing_number') }}', 'Routing number')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                @endif

                                                @if(\App\Models\SiteSetting::getValue('bank_swift_code'))
                                                <div class="col-md-6 mb-2">
                                                    <strong>SWIFT/BIC Code:</strong><br>
                                                    <code class="bg-light p-1">{{ \App\Models\SiteSetting::getValue('bank_swift_code') }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ \App\Models\SiteSetting::getValue('bank_swift_code') }}', 'SWIFT code')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                @endif

                                                @if(\App\Models\SiteSetting::getValue('bank_iban'))
                                                <div class="col-md-6 mb-2">
                                                    <strong>IBAN:</strong><br>
                                                    <code class="bg-light p-1">{{ \App\Models\SiteSetting::getValue('bank_iban') }}</code>
                                                    <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('{{ \App\Models\SiteSetting::getValue('bank_iban') }}', 'IBAN')">
                                                        <i class="fas fa-copy"></i>
                                                    </button>
                                                </div>
                                                @endif

                                                @if(\App\Models\SiteSetting::getValue('bank_branch_address'))
                                                <div class="col-12 mb-2">
                                                    <strong>Branch Address:</strong><br>
                                                    <span class="text-primary">{{ \App\Models\SiteSetting::getValue('bank_branch_address') }}</span>
                                                </div>
                                                @endif
                                            </div>

                                            @if(\App\Models\SiteSetting::getValue('bank_instructions'))
                                            <div class="alert alert-info mt-3 mb-0">
                                                <small><strong>Instructions:</strong> {{ \App\Models\SiteSetting::getValue('bank_instructions') }}</small>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                    @endif

                                    <!-- General Instructions -->
                                    <div class="alert alert-secondary">
                                        <h6><i class="fas fa-info-circle me-2"></i>Payment Instructions</h6>
                                        <ol class="mb-2">
                                            <li>Use the account details above to make your payment</li>
                                            <li>Include your order number <strong>{{ $order->order_number }}</strong> as reference</li>
                                            <li>Send payment confirmation to {{ \App\Models\SiteSetting::getValue('support_email', '<EMAIL>') }}</li>
                                            <li>Your order will be processed once payment is verified</li>
                                        </ol>
                                        <p class="mb-0"><strong>Note:</strong> Your payment will be marked as pending until we receive and verify your payment. You'll receive email confirmation once processed.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Security Features -->
                        <div class="row mt-4">
                            <div class="col-md-4 text-center">
                                <i class="fas fa-lock fa-2x text-success mb-2"></i>
                                <h6>SSL Encrypted</h6>
                                <small class="text-muted">256-bit SSL encryption</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <h6>PCI Compliant</h6>
                                <small class="text-muted">Industry standard security</small>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-user-shield fa-2x text-success mb-2"></i>
                                <h6>Fraud Protection</h6>
                                <small class="text-muted">Advanced fraud detection</small>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary btn-lg" id="submitPayment">
                                <i class="fas fa-credit-card me-2"></i>
                                Proceed to Secure Payment
                                <span class="ms-2">@currency($order->total_amount)</span>
                            </button>
                            <a href="{{ route('customer.orders.show', $order) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                Back to Order Details
                            </a>
                        </div>

                    @else
                        <div class="alert alert-warning text-center">
                            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                            <h5>No Payment Methods Available</h5>
                            <p class="mb-3">Payment processing is currently unavailable. Please contact our support team for assistance.</p>
                            <a href="{{ route('customer.support.create') }}" class="btn btn-warning">
                                <i class="fas fa-headset me-2"></i>
                                Contact Support
                            </a>
                        </div>
                    @endif
                </form>
            </div>
        </div>

        <!-- Trust Indicators -->
        <div class="row mt-4 text-center">
            <div class="col-12">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Your payment information is processed securely. We use industry-standard encryption and never store your card details.
                </small>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.payment-method-card {
    height: 100%;
}

.payment-method-card .btn {
    min-height: 150px;
    transition: all 0.3s ease;
}

.payment-method-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.payment-method-card input[type="radio"]:checked + label {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.payment-info {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
</style>
@endpush

@push('scripts')
<!-- Stripe.js -->
<script src="https://js.stripe.com/v3/"></script>

<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id={{ config('services.paypal.client_id') }}&currency={{ $currency['code'] }}"></script>

<script>
// Initialize Stripe
const stripe = Stripe('{{ config('services.stripe.key') }}');
let elements, cardElement, paymentIntent;

document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const paymentInfos = document.querySelectorAll('.payment-info');
    const paymentForm = document.getElementById('paymentForm');

    // Initialize Stripe Elements
    function initializeStripeElements() {
        elements = stripe.elements();
        cardElement = elements.create('card', {
            style: {
                base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                        color: '#aab7c4',
                    },
                },
            },
        });
        cardElement.mount('#card-element');

        cardElement.on('change', function(event) {
            const displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });
    }

    // Initialize PayPal
    function initializePayPal() {
        if (typeof paypal !== 'undefined') {
            paypal.Buttons({
                createOrder: function(data, actions) {
                    return actions.order.create({
                        purchase_units: [{
                            amount: {
                                value: '{{ $order->total_amount }}',
                                currency_code: '{{ $currency['code'] }}'
                            },
                            description: 'Payment for order {{ $order->order_number }}'
                        }]
                    });
                },
                onApprove: function(data, actions) {
                    // Add PayPal data to form and submit
                    const form = document.getElementById('paymentForm');

                    // Add hidden inputs for PayPal data
                    const paymentIdInput = document.createElement('input');
                    paymentIdInput.type = 'hidden';
                    paymentIdInput.name = 'payment_id';
                    paymentIdInput.value = data.orderID;
                    form.appendChild(paymentIdInput);

                    const payerIdInput = document.createElement('input');
                    payerIdInput.type = 'hidden';
                    payerIdInput.name = 'payer_id';
                    payerIdInput.value = data.payerID;
                    form.appendChild(payerIdInput);

                    // Submit form
                    form.submit();
                },
                onError: function(err) {
                    console.error('PayPal error:', err);
                    alert('PayPal payment failed. Please try again.');
                }
            }).render('#paypal-button-container');
        }
    }

    function showPaymentInfo() {
        // Hide all payment info sections
        paymentInfos.forEach(info => info.style.display = 'none');

        // Hide all payment containers
        document.getElementById('stripe-elements-container').style.display = 'none';
        document.getElementById('paypal-button-container').style.display = 'none';

        // Show the selected payment method info
        const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
        const selectedInfo = document.getElementById(selectedMethod + '-info');
        if (selectedInfo) {
            selectedInfo.style.display = 'block';
        }

        // Show specific payment containers
        if (selectedMethod === 'stripe') {
            document.getElementById('stripe-elements-container').style.display = 'block';
            if (!cardElement) {
                initializeStripeElements();
            }
        } else if (selectedMethod === 'paypal') {
            document.getElementById('paypal-button-container').style.display = 'block';
            // PayPal buttons are initialized once
        }

        // Update submit button text
        const submitButton = document.getElementById('submitPayment');
        const methodName = document.querySelector(`label[for="method_${selectedMethod}"] h6`).textContent;

        if (selectedMethod === 'paypal') {
            submitButton.style.display = 'none'; // Hide for PayPal (uses its own button)
        } else {
            submitButton.style.display = 'block';
            submitButton.innerHTML = `
                <i class="fas fa-credit-card me-2"></i>
                Pay with ${methodName}
                <span class="ms-2">@currency($order->total_amount)</span>
            `;
        }
    }

    // Add event listeners to payment method radio buttons
    paymentMethods.forEach(method => {
        method.addEventListener('change', showPaymentInfo);
    });

    // Show initial payment info
    showPaymentInfo();

    // Initialize PayPal once
    initializePayPal();

    // Form submission handling
    paymentForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
        const submitButton = document.getElementById('submitPayment');

        if (selectedMethod === 'stripe') {
            await handleStripePayment(submitButton);
        } else if (selectedMethod === 'paypal') {
            // PayPal handles its own submission
            return;
        } else {
            // For other methods, submit normally
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
            paymentForm.submit();
        }
    });

    async function handleStripePayment(submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';

        try {
            // Create payment intent
            const response = await fetch('{{ route('customer.orders.pay.process', $order) }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    payment_method: 'stripe'
                })
            });

            const result = await response.json();

            if (result.requires_action) {
                // Confirm payment with Stripe
                const {error, paymentIntent} = await stripe.confirmCardPayment(result.client_secret, {
                    payment_method: {
                        card: cardElement,
                        billing_details: {
                            name: '{{ Auth::user()->name }}',
                            email: '{{ Auth::user()->email }}'
                        }
                    }
                });

                if (error) {
                    throw new Error(error.message);
                } else if (paymentIntent.status === 'succeeded') {
                    // Payment succeeded, submit form with payment intent ID
                    const form = document.getElementById('paymentForm');
                    const paymentIntentInput = document.createElement('input');
                    paymentIntentInput.type = 'hidden';
                    paymentIntentInput.name = 'payment_intent_id';
                    paymentIntentInput.value = paymentIntent.id;
                    form.appendChild(paymentIntentInput);
                    form.submit();
                } else {
                    throw new Error('Payment not completed');
                }
            } else if (result.success) {
                // Payment completed, redirect to success
                window.location.href = result.redirect_url;
            } else {
                throw new Error(result.message || 'Payment failed');
            }

        } catch (error) {
            console.error('Stripe payment error:', error);
            alert('Payment failed: ' + error.message);
            submitButton.disabled = false;
            submitButton.innerHTML = `
                <i class="fas fa-credit-card me-2"></i>
                Pay with Stripe
                <span class="ms-2">{{ $currency['symbol'] }}{{ number_format($order->total_amount, 2) }}</span>
            `;
        }
    }
});

// Copy to clipboard function for account details
function copyToClipboard(text, label = 'Text') {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${label} copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);

        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert(`${label}: ${text}`);
    });
}
</script>
@endpush
