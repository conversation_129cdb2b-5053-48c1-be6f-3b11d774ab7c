<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class FooterCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Shipping Containers',
                'slug' => 'shipping-containers',
                'description' => 'High-quality shipping containers for all your logistics needs',
                'footer_featured' => true,
                'footer_text' => 'Shipping Containers',
                'footer_icon' => 'fas fa-cube text-blue-500',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Cardboard Boxes',
                'slug' => 'cardboard-boxes',
                'description' => 'Durable cardboard boxes for packaging and shipping',
                'footer_featured' => true,
                'footer_text' => 'CardBoxes',
                'footer_icon' => 'fas fa-box text-orange-500',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Steel Products',
                'slug' => 'steel-products',
                'description' => 'Premium steel products for industrial applications',
                'footer_featured' => true,
                'footer_text' => 'Steel Products',
                'footer_icon' => 'fas fa-industry text-gray-500',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Spare Parts',
                'slug' => 'spare-parts',
                'description' => 'Quality spare parts for automotive and industrial equipment',
                'footer_featured' => true,
                'footer_text' => 'Spare Parts',
                'footer_icon' => 'fas fa-cog text-purple-500',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Packaging Materials',
                'slug' => 'packaging-materials',
                'description' => 'Comprehensive packaging materials for secure shipping',
                'footer_featured' => true,
                'footer_text' => 'Packaging Materials',
                'footer_icon' => 'fas fa-tape text-yellow-500',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $categoryData) {
            Category::updateOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }
    }
}
