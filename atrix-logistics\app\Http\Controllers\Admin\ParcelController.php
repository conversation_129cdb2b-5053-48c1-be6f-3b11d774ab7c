<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\Carrier;
use App\Models\User;
use App\Models\TrackingEvent;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class ParcelController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View
    {
        $query = Parcel::with(['carrier', 'user']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('tracking_number', 'like', "%{$search}%")
                  ->orWhere('sender_name', 'like', "%{$search}%")
                  ->orWhere('recipient_name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Carrier filter
        if ($request->filled('carrier_id')) {
            $query->where('carrier_id', $request->carrier_id);
        }

        $parcels = $query->orderBy('created_at', 'desc')->paginate(20);
        $carriers = Carrier::where('is_active', true)->get();
        $statuses = ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered', 'exception', 'returned'];

        return view('admin.parcels.index', compact('parcels', 'carriers', 'statuses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $carriers = Carrier::where('is_active', true)->get();
        $customers = User::where('role', 'customer')->get();

        return view('admin.parcels.create', compact('carriers', 'customers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'carrier_id' => 'required|exists:carriers,id',
            'user_id' => 'nullable|exists:users,id',
            'sender_name' => 'required|string|max:255',
            'sender_email' => 'required|email|max:255',
            'sender_phone' => 'required|string|max:20',
            'sender_address' => 'required|string',
            'sender_city' => 'required|string|max:100',
            'sender_state' => 'required|string|max:100',
            'sender_postal_code' => 'required|string|max:20',
            'sender_country' => 'required|string|max:100',
            'recipient_name' => 'required|string|max:255',
            'recipient_email' => 'required|email|max:255',
            'recipient_phone' => 'required|string|max:20',
            'recipient_address' => 'required|string',
            'recipient_city' => 'required|string|max:100',
            'recipient_state' => 'required|string|max:100',
            'recipient_postal_code' => 'required|string|max:20',
            'recipient_country' => 'required|string|max:100',
            'description' => 'required|string',
            'weight' => 'required|numeric|min:0',
            'dimensions' => 'nullable|string|max:100',
            'declared_value' => 'required|numeric|min:0',
            'service_type' => 'required|string|in:standard,express,overnight,same_day',
            'shipping_cost' => 'required|numeric|min:0',
            'insurance_cost' => 'nullable|numeric|min:0',
            'special_instructions' => 'nullable|string',
            'estimated_delivery_date' => 'nullable|date|after:today',
        ]);

        $validated['total_cost'] = $validated['shipping_cost'] + ($validated['insurance_cost'] ?? 0);
        $validated['status'] = 'pending';
        $validated['is_paid'] = false;

        $parcel = Parcel::create($validated);

        // Create initial tracking event
        TrackingEvent::create([
            'parcel_id' => $parcel->id,
            'status' => 'pending',
            'location' => $validated['sender_city'] . ', ' . $validated['sender_state'],
            'description' => 'Package received and processing',
            'event_date' => now(),
            'is_public' => true,
        ]);

        return redirect()->route('admin.parcels.show', $parcel)
                        ->with('success', 'Parcel created successfully with tracking number: ' . $parcel->tracking_number);
    }

    /**
     * Display the specified resource.
     */
    public function show(Parcel $parcel): View
    {
        $parcel->load(['carrier', 'user', 'trackingEvents']);

        return view('admin.parcels.show', compact('parcel'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Parcel $parcel): View
    {
        $carriers = Carrier::where('is_active', true)->get();
        $customers = User::where('role', 'customer')->get();

        return view('admin.parcels.edit', compact('parcel', 'carriers', 'customers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Parcel $parcel): RedirectResponse
    {
        $validated = $request->validate([
            'carrier_id' => 'required|exists:carriers,id',
            'user_id' => 'nullable|exists:users,id',
            'status' => 'required|string|in:pending,picked_up,in_transit,out_for_delivery,delivered,exception,returned',
            'sender_name' => 'required|string|max:255',
            'sender_email' => 'required|email|max:255',
            'sender_phone' => 'required|string|max:20',
            'sender_address' => 'required|string',
            'sender_city' => 'required|string|max:100',
            'sender_state' => 'required|string|max:100',
            'sender_postal_code' => 'required|string|max:20',
            'sender_country' => 'required|string|max:100',
            'recipient_name' => 'required|string|max:255',
            'recipient_email' => 'required|email|max:255',
            'recipient_phone' => 'required|string|max:20',
            'recipient_address' => 'required|string',
            'recipient_city' => 'required|string|max:100',
            'recipient_state' => 'required|string|max:100',
            'recipient_postal_code' => 'required|string|max:20',
            'recipient_country' => 'required|string|max:100',
            'description' => 'required|string',
            'weight' => 'required|numeric|min:0',
            'dimensions' => 'nullable|string|max:100',
            'declared_value' => 'required|numeric|min:0',
            'service_type' => 'required|string|in:standard,express,overnight,same_day',
            'shipping_cost' => 'required|numeric|min:0',
            'insurance_cost' => 'nullable|numeric|min:0',
            'special_instructions' => 'nullable|string',
            'estimated_delivery_date' => 'nullable|date',
            'is_paid' => 'boolean',
        ]);

        $validated['total_cost'] = $validated['shipping_cost'] + ($validated['insurance_cost'] ?? 0);

        // Check if status changed
        if ($parcel->status !== $validated['status']) {
            // Create tracking event for status change
            TrackingEvent::create([
                'parcel_id' => $parcel->id,
                'status' => $validated['status'],
                'location' => $validated['recipient_city'] . ', ' . $validated['recipient_state'],
                'description' => 'Status updated to ' . ucfirst(str_replace('_', ' ', $validated['status'])),
                'event_date' => now(),
                'is_public' => true,
            ]);

            // Set delivered_at if status is delivered
            if ($validated['status'] === 'delivered') {
                $validated['delivered_at'] = now();
            }
        }

        $parcel->update($validated);

        return redirect()->route('admin.parcels.show', $parcel)
                        ->with('success', 'Parcel updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Parcel $parcel): RedirectResponse
    {
        $trackingNumber = $parcel->tracking_number;

        // Delete related tracking events
        $parcel->trackingEvents()->delete();

        // Delete the parcel
        $parcel->delete();

        return redirect()->route('admin.parcels.index')
                        ->with('success', "Parcel {$trackingNumber} deleted successfully.");
    }

    /**
     * Bulk update parcel statuses
     */
    public function bulkUpdate(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'parcel_ids' => 'required|array',
            'parcel_ids.*' => 'exists:parcels,id',
            'bulk_action' => 'required|string|in:update_status,delete,mark_paid,mark_unpaid',
            'new_status' => 'required_if:bulk_action,update_status|string|in:pending,picked_up,in_transit,out_for_delivery,delivered,exception,returned',
        ]);

        $parcelIds = $validated['parcel_ids'];
        $action = $validated['bulk_action'];
        $updatedCount = 0;

        switch ($action) {
            case 'update_status':
                $newStatus = $validated['new_status'];
                $parcels = Parcel::whereIn('id', $parcelIds)->get();

                foreach ($parcels as $parcel) {
                    $oldStatus = $parcel->status;
                    $parcel->update(['status' => $newStatus]);

                    // Create tracking event for status change
                    TrackingEvent::create([
                        'parcel_id' => $parcel->id,
                        'status' => $newStatus,
                        'location' => $parcel->recipient_city . ', ' . $parcel->recipient_state,
                        'description' => "Status updated from {$oldStatus} to {$newStatus} via bulk operation",
                        'event_date' => now(),
                        'is_public' => true,
                    ]);

                    // Set delivered_at if status is delivered
                    if ($newStatus === 'delivered') {
                        $parcel->update(['delivered_at' => now()]);
                    }

                    $updatedCount++;
                }
                break;

            case 'delete':
                $parcels = Parcel::whereIn('id', $parcelIds)->get();
                foreach ($parcels as $parcel) {
                    $parcel->trackingEvents()->delete();
                    $parcel->delete();
                    $updatedCount++;
                }
                break;

            case 'mark_paid':
                $updatedCount = Parcel::whereIn('id', $parcelIds)->update(['is_paid' => true]);
                break;

            case 'mark_unpaid':
                $updatedCount = Parcel::whereIn('id', $parcelIds)->update(['is_paid' => false]);
                break;
        }

        $actionName = match($action) {
            'update_status' => 'status updated',
            'delete' => 'deleted',
            'mark_paid' => 'marked as paid',
            'mark_unpaid' => 'marked as unpaid',
        };

        return redirect()->route('admin.parcels.index')
                        ->with('success', "{$updatedCount} parcels {$actionName} successfully.");
    }

    /**
     * Send notification email for a parcel
     */
    public function sendNotification(Parcel $parcel): RedirectResponse
    {
        try {
            $latestEvent = $parcel->trackingEvents()->orderBy('event_date', 'desc')->first();

            \Mail::to($parcel->recipient_email)->send(
                new \App\Mail\ParcelStatusUpdated($parcel, $latestEvent)
            );

            return redirect()->back()
                            ->with('success', 'Notification email sent successfully to ' . $parcel->recipient_email);
        } catch (\Exception $e) {
            return redirect()->back()
                            ->with('error', 'Failed to send notification email: ' . $e->getMessage());
        }
    }
}
