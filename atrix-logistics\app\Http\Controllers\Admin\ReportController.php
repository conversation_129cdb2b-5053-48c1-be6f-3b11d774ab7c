<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Parcel;
use App\Models\User;
use App\Models\Carrier;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;
use App\Exports\ParcelsExport;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    /**
     * Display the reports dashboard
     */
    public function index(): View
    {
        return view('admin.reports.index');
    }

    /**
     * Get parcels analytics data
     */
    public function parcelsAnalytics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        // Daily parcels count for the period
        $dailyParcels = Parcel::selectRaw('DATE(created_at) as date, COUNT(*) as count')
                             ->where('created_at', '>=', $startDate)
                             ->groupBy('date')
                             ->orderBy('date')
                             ->get()
                             ->pluck('count', 'date');

        // Status distribution
        $statusDistribution = Parcel::selectRaw('status, COUNT(*) as count')
                                   ->groupBy('status')
                                   ->pluck('count', 'status');

        // Carrier performance
        $carrierStats = Parcel::with('carrier')
                             ->selectRaw('carrier_id, COUNT(*) as total_parcels,
                                         SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered_parcels,
                                         AVG(total_cost) as avg_cost')
                             ->groupBy('carrier_id')
                             ->get()
                             ->map(function ($item) {
                                 return [
                                     'carrier_name' => $item->carrier->name,
                                     'total_parcels' => $item->total_parcels,
                                     'delivered_parcels' => $item->delivered_parcels,
                                     'delivery_rate' => $item->total_parcels > 0 ? round(($item->delivered_parcels / $item->total_parcels) * 100, 2) : 0,
                                     'avg_cost' => round($item->avg_cost, 2),
                                 ];
                             });

        // Revenue analytics
        $dailyRevenue = Parcel::selectRaw('DATE(created_at) as date, SUM(total_cost) as revenue')
                             ->where('created_at', '>=', $startDate)
                             ->where('is_paid', true)
                             ->groupBy('date')
                             ->orderBy('date')
                             ->get()
                             ->pluck('revenue', 'date');

        return response()->json([
            'daily_parcels' => $dailyParcels,
            'status_distribution' => $statusDistribution,
            'carrier_stats' => $carrierStats,
            'daily_revenue' => $dailyRevenue,
            'period' => $period,
        ]);
    }

    /**
     * Get customer analytics
     */
    public function customerAnalytics(): JsonResponse
    {
        // Top customers by parcel count
        $topCustomers = User::where('role', 'customer')
                           ->withCount('parcels')
                           ->orderBy('parcels_count', 'desc')
                           ->limit(10)
                           ->get()
                           ->map(function ($customer) {
                               return [
                                   'name' => $customer->name,
                                   'email' => $customer->email,
                                   'parcels_count' => $customer->parcels_count,
                                   'total_spent' => $customer->parcels()->where('is_paid', true)->sum('total_cost'),
                               ];
                           });

        // Customer registration trend
        $customerTrend = User::where('role', 'customer')
                            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                            ->where('created_at', '>=', Carbon::now()->subDays(30))
                            ->groupBy('date')
                            ->orderBy('date')
                            ->get()
                            ->pluck('count', 'date');

        return response()->json([
            'top_customers' => $topCustomers,
            'customer_trend' => $customerTrend,
        ]);
    }

    /**
     * Export parcels data
     */
    public function exportParcels(Request $request)
    {
        $format = $request->get('format', 'xlsx'); // Default to Excel format
        $filename = 'parcels_export_' . date('Y-m-d_H-i-s');

        // Use the new Excel export class
        return Excel::download(new ParcelsExport($request), $filename . '.' . $format);
    }
}
