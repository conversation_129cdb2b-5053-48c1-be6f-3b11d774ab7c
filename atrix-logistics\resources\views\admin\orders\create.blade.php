@extends('layouts.admin')

@section('title', 'Create Order')
@section('page-title', 'Create New Order')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Orders
        </a>
    </div>
@endsection

@section('content')
    <form method="POST" action="{{ route('admin.orders.store') }}" id="orderForm">
        @csrf
        
        <div class="row">
            <!-- Order Details -->
            <div class="col-lg-8">
                <!-- Customer Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Customer Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="customer_id" class="form-label">Existing Customer</label>
                                <select class="form-select" id="customer_id" name="customer_id" onchange="fillCustomerInfo()">
                                    <option value="">Select existing customer (optional)</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" 
                                                data-name="{{ $customer->name }}" 
                                                data-email="{{ $customer->email }}"
                                                data-phone="{{ $customer->phone ?? '' }}">
                                            {{ $customer->name }} ({{ $customer->email }})
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="customer_name" class="form-label">Customer Name *</label>
                                <input type="text" class="form-control @error('customer_name') is-invalid @enderror" 
                                       id="customer_name" name="customer_name" value="{{ old('customer_name') }}" required>
                                @error('customer_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="customer_email" class="form-label">Email *</label>
                                <input type="email" class="form-control @error('customer_email') is-invalid @enderror" 
                                       id="customer_email" name="customer_email" value="{{ old('customer_email') }}" required>
                                @error('customer_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="customer_phone" class="form-label">Phone</label>
                                <input type="text" class="form-control @error('customer_phone') is-invalid @enderror" 
                                       id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}">
                                @error('customer_phone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Items -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-box me-2"></i>
                            Order Items
                        </h5>
                        <button type="button" class="btn btn-sm btn-primary" onclick="addOrderItem()">
                            <i class="fas fa-plus me-1"></i> Add Item
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="orderItems">
                            <!-- Order items will be added here -->
                        </div>
                        
                        <div class="text-center py-3" id="noItemsMessage">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No items added yet. Click "Add Item" to start.</p>
                        </div>
                    </div>
                </div>

                <!-- Addresses -->
                <div class="row">
                    <!-- Billing Address -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-credit-card me-2"></i>
                                    Billing Address
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="billing_first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control @error('billing_first_name') is-invalid @enderror" 
                                               id="billing_first_name" name="billing_first_name" value="{{ old('billing_first_name') }}" required>
                                        @error('billing_first_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="billing_last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control @error('billing_last_name') is-invalid @enderror" 
                                               id="billing_last_name" name="billing_last_name" value="{{ old('billing_last_name') }}" required>
                                        @error('billing_last_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="billing_company" class="form-label">Company</label>
                                    <input type="text" class="form-control @error('billing_company') is-invalid @enderror" 
                                           id="billing_company" name="billing_company" value="{{ old('billing_company') }}">
                                    @error('billing_company')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="billing_address_1" class="form-label">Address Line 1 *</label>
                                    <input type="text" class="form-control @error('billing_address_1') is-invalid @enderror" 
                                           id="billing_address_1" name="billing_address_1" value="{{ old('billing_address_1') }}" required>
                                    @error('billing_address_1')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="billing_address_2" class="form-label">Address Line 2</label>
                                    <input type="text" class="form-control @error('billing_address_2') is-invalid @enderror" 
                                           id="billing_address_2" name="billing_address_2" value="{{ old('billing_address_2') }}">
                                    @error('billing_address_2')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="billing_city" class="form-label">City *</label>
                                        <input type="text" class="form-control @error('billing_city') is-invalid @enderror" 
                                               id="billing_city" name="billing_city" value="{{ old('billing_city') }}" required>
                                        @error('billing_city')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="billing_state" class="form-label">State *</label>
                                        <input type="text" class="form-control @error('billing_state') is-invalid @enderror" 
                                               id="billing_state" name="billing_state" value="{{ old('billing_state') }}" required>
                                        @error('billing_state')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="billing_postal_code" class="form-label">Postal Code *</label>
                                        <input type="text" class="form-control @error('billing_postal_code') is-invalid @enderror" 
                                               id="billing_postal_code" name="billing_postal_code" value="{{ old('billing_postal_code') }}" required>
                                        @error('billing_postal_code')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="billing_country" class="form-label">Country *</label>
                                        <input type="text" class="form-control @error('billing_country') is-invalid @enderror" 
                                               id="billing_country" name="billing_country" value="{{ old('billing_country', 'USA') }}" required>
                                        @error('billing_country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Shipping Address -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-truck me-2"></i>
                                    Shipping Address
                                </h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="sameAsBilling" onchange="copyBillingToShipping()">
                                    <label class="form-check-label" for="sameAsBilling">
                                        Same as billing
                                    </label>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_first_name" class="form-label">First Name *</label>
                                        <input type="text" class="form-control @error('shipping_first_name') is-invalid @enderror" 
                                               id="shipping_first_name" name="shipping_first_name" value="{{ old('shipping_first_name') }}" required>
                                        @error('shipping_first_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_last_name" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control @error('shipping_last_name') is-invalid @enderror" 
                                               id="shipping_last_name" name="shipping_last_name" value="{{ old('shipping_last_name') }}" required>
                                        @error('shipping_last_name')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="shipping_company" class="form-label">Company</label>
                                    <input type="text" class="form-control @error('shipping_company') is-invalid @enderror" 
                                           id="shipping_company" name="shipping_company" value="{{ old('shipping_company') }}">
                                    @error('shipping_company')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="shipping_address_1" class="form-label">Address Line 1 *</label>
                                    <input type="text" class="form-control @error('shipping_address_1') is-invalid @enderror" 
                                           id="shipping_address_1" name="shipping_address_1" value="{{ old('shipping_address_1') }}" required>
                                    @error('shipping_address_1')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="shipping_address_2" class="form-label">Address Line 2</label>
                                    <input type="text" class="form-control @error('shipping_address_2') is-invalid @enderror" 
                                           id="shipping_address_2" name="shipping_address_2" value="{{ old('shipping_address_2') }}">
                                    @error('shipping_address_2')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_city" class="form-label">City *</label>
                                        <input type="text" class="form-control @error('shipping_city') is-invalid @enderror" 
                                               id="shipping_city" name="shipping_city" value="{{ old('shipping_city') }}" required>
                                        @error('shipping_city')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_state" class="form-label">State *</label>
                                        <input type="text" class="form-control @error('shipping_state') is-invalid @enderror" 
                                               id="shipping_state" name="shipping_state" value="{{ old('shipping_state') }}" required>
                                        @error('shipping_state')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_postal_code" class="form-label">Postal Code *</label>
                                        <input type="text" class="form-control @error('shipping_postal_code') is-invalid @enderror" 
                                               id="shipping_postal_code" name="shipping_postal_code" value="{{ old('shipping_postal_code') }}" required>
                                        @error('shipping_postal_code')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="shipping_country" class="form-label">Country *</label>
                                        <input type="text" class="form-control @error('shipping_country') is-invalid @enderror" 
                                               id="shipping_country" name="shipping_country" value="{{ old('shipping_country', 'USA') }}" required>
                                        @error('shipping_country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-sticky-note me-2"></i>
                            Additional Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="notes" class="form-label">Customer Notes</label>
                                <textarea class="form-control @error('notes') is-invalid @enderror" 
                                          id="notes" name="notes" rows="3" placeholder="Any special instructions or notes...">{{ old('notes') }}</textarea>
                                @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="admin_notes" class="form-label">Admin Notes</label>
                                <textarea class="form-control @error('admin_notes') is-invalid @enderror" 
                                          id="admin_notes" name="admin_notes" rows="3" placeholder="Internal notes (not visible to customer)...">{{ old('admin_notes') }}</textarea>
                                @error('admin_notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            Order Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal:</span>
                            <span id="subtotalDisplay">@currency(0)</span>
                        </div>
                        
                        <div class="mb-3">
                            <label for="shipping_amount" class="form-label">Shipping:</label>
                            <input type="number" class="form-control" id="shipping_amount" name="shipping_amount" 
                                   value="{{ old('shipping_amount', 0) }}" min="0" step="0.01" onchange="calculateTotal()">
                        </div>
                        
                        <div class="mb-3">
                            <label for="tax_amount" class="form-label">Tax:</label>
                            <input type="number" class="form-control" id="tax_amount" name="tax_amount" 
                                   value="{{ old('tax_amount', 0) }}" min="0" step="0.01" onchange="calculateTotal()">
                        </div>
                        
                        <div class="mb-3">
                            <label for="discount_amount" class="form-label">Discount:</label>
                            <input type="number" class="form-control" id="discount_amount" name="discount_amount" 
                                   value="{{ old('discount_amount', 0) }}" min="0" step="0.01" onchange="calculateTotal()">
                        </div>
                        
                        <hr>
                        <div class="d-flex justify-content-between">
                            <strong>Total:</strong>
                            <strong class="h5" id="totalDisplay">@currency(0)</strong>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                Create Order
                            </button>
                            <a href="{{ route('admin.orders.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>
                                Cancel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Order Item Template -->
    <template id="orderItemTemplate">
        <div class="order-item border rounded p-3 mb-3">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h6 class="mb-0">Item #<span class="item-number"></span></h6>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeOrderItem(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Product *</label>
                    <select class="form-select product-select" name="items[INDEX][product_id]" onchange="fillProductInfo(this)" required>
                        <option value="">Select a product</option>
                        @foreach($products as $product)
                            <option value="{{ $product->id }}" 
                                    data-name="{{ $product->name }}" 
                                    data-price="{{ $product->price }}"
                                    data-sku="{{ $product->sku }}">
                                {{ $product->name }} - @currency($product->price)
                            </option>
                        @endforeach
                    </select>
                </div>
                
                <div class="col-md-3 mb-3">
                    <label class="form-label">Quantity *</label>
                    <input type="number" class="form-control quantity-input" name="items[INDEX][quantity]" 
                           value="1" min="1" onchange="calculateItemTotal(this)" required>
                </div>
                
                <div class="col-md-3 mb-3">
                    <label class="form-label">Unit Price *</label>
                    <input type="number" class="form-control price-input" name="items[INDEX][unit_price]" 
                           value="0" min="0" step="0.01" onchange="calculateItemTotal(this)" required>
                </div>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <span>Item Total:</span>
                <strong class="item-total">@currency(0)</strong>
            </div>
        </div>
    </template>
@endsection

@push('scripts')
<script>
    let itemIndex = 0;

    function fillCustomerInfo() {
        const select = document.getElementById('customer_id');
        const option = select.options[select.selectedIndex];
        
        if (option.value) {
            document.getElementById('customer_name').value = option.dataset.name || '';
            document.getElementById('customer_email').value = option.dataset.email || '';
            document.getElementById('customer_phone').value = option.dataset.phone || '';
        }
    }

    function copyBillingToShipping() {
        const checkbox = document.getElementById('sameAsBilling');
        
        if (checkbox.checked) {
            document.getElementById('shipping_first_name').value = document.getElementById('billing_first_name').value;
            document.getElementById('shipping_last_name').value = document.getElementById('billing_last_name').value;
            document.getElementById('shipping_company').value = document.getElementById('billing_company').value;
            document.getElementById('shipping_address_1').value = document.getElementById('billing_address_1').value;
            document.getElementById('shipping_address_2').value = document.getElementById('billing_address_2').value;
            document.getElementById('shipping_city').value = document.getElementById('billing_city').value;
            document.getElementById('shipping_state').value = document.getElementById('billing_state').value;
            document.getElementById('shipping_postal_code').value = document.getElementById('billing_postal_code').value;
            document.getElementById('shipping_country').value = document.getElementById('billing_country').value;
        }
    }

    function addOrderItem() {
        const template = document.getElementById('orderItemTemplate');
        const clone = template.content.cloneNode(true);
        
        // Update item number and indices
        itemIndex++;
        clone.querySelector('.item-number').textContent = itemIndex;
        
        // Update form field names
        clone.querySelectorAll('[name*="INDEX"]').forEach(field => {
            field.name = field.name.replace('INDEX', itemIndex - 1);
        });
        
        document.getElementById('orderItems').appendChild(clone);
        document.getElementById('noItemsMessage').style.display = 'none';
        
        calculateTotal();
    }

    function removeOrderItem(button) {
        button.closest('.order-item').remove();
        
        if (document.querySelectorAll('.order-item').length === 0) {
            document.getElementById('noItemsMessage').style.display = 'block';
        }
        
        calculateTotal();
    }

    function fillProductInfo(select) {
        const option = select.options[select.selectedIndex];
        const item = select.closest('.order-item');
        
        if (option.value) {
            const priceInput = item.querySelector('.price-input');
            priceInput.value = option.dataset.price || 0;
            calculateItemTotal(priceInput);
        }
    }

    function calculateItemTotal(input) {
        const item = input.closest('.order-item');
        const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(item.querySelector('.price-input').value) || 0;
        const total = quantity * price;
        
        item.querySelector('.item-total').textContent = '$' + total.toFixed(2);
        
        calculateTotal();
    }

    function calculateTotal() {
        let subtotal = 0;
        
        document.querySelectorAll('.order-item').forEach(item => {
            const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
            const price = parseFloat(item.querySelector('.price-input').value) || 0;
            subtotal += quantity * price;
        });
        
        const shipping = parseFloat(document.getElementById('shipping_amount').value) || 0;
        const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
        const discount = parseFloat(document.getElementById('discount_amount').value) || 0;
        
        const total = subtotal + shipping + tax - discount;
        
        document.getElementById('subtotalDisplay').textContent = '$' + subtotal.toFixed(2);
        document.getElementById('totalDisplay').textContent = '$' + total.toFixed(2);
    }

    // Add first item by default
    document.addEventListener('DOMContentLoaded', function() {
        addOrderItem();
    });
</script>
@endpush
