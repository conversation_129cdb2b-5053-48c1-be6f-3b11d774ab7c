@extends('layouts.admin')

@section('title', 'Create Product - Step 2')
@section('page-title', 'Create New Product - Content & SEO')

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.products.create') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Step 1
        </a>
    </div>
@endsection

@section('content')
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="progress-steps">
                        <div class="step completed">
                            <div class="step-number"><i class="fas fa-check"></i></div>
                            <div class="step-title">Basic Info</div>
                        </div>
                        <div class="step active">
                            <div class="step-number">2</div>
                            <div class="step-title">Content & SEO</div>
                        </div>
                        <div class="step">
                            <div class="step-number">3</div>
                            <div class="step-title">Pricing & Inventory</div>
                        </div>
                        <div class="step">
                            <div class="step-number">4</div>
                            <div class="step-title">Images & Media</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ route('admin.products.create.step2.store') }}">
                @csrf

                <!-- Product Content -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Product Content
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Description Tab Navigation -->
                        <ul class="nav nav-tabs mb-3" id="contentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="description-tab" data-bs-toggle="tab" 
                                        data-bs-target="#description" type="button" role="tab">
                                    <i class="fas fa-align-left me-2"></i>Description
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="additional-tab" data-bs-toggle="tab" 
                                        data-bs-target="#additional" type="button" role="tab">
                                    <i class="fas fa-info-circle me-2"></i>Additional Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="technical-tab" data-bs-toggle="tab" 
                                        data-bs-target="#technical" type="button" role="tab">
                                    <i class="fas fa-cogs me-2"></i>Technical Specifications
                                </button>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="contentTabsContent">
                            <!-- Description Tab -->
                            <div class="tab-pane fade show active" id="description" role="tabpanel">
                                <div class="mb-3">
                                    <label for="description" class="form-label">Product Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="8"
                                              placeholder="Detailed product description...">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        This will be the main product description shown to customers. 
                                        Use rich formatting to highlight key features.
                                    </small>
                                </div>
                            </div>

                            <!-- Additional Information Tab -->
                            <div class="tab-pane fade" id="additional" role="tabpanel">
                                <div class="mb-3">
                                    <label for="additional_information" class="form-label">Additional Information</label>
                                    <textarea class="form-control @error('additional_information') is-invalid @enderror" 
                                              id="additional_information" name="additional_information" rows="8"
                                              placeholder="Additional product details, usage instructions, care information...">{{ old('additional_information') }}</textarea>
                                    @error('additional_information')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        Include usage instructions, care information, warranty details, 
                                        or any other relevant information.
                                    </small>
                                </div>
                            </div>

                            <!-- Technical Specifications Tab -->
                            <div class="tab-pane fade" id="technical" role="tabpanel">
                                <div class="mb-3">
                                    <label for="technical_specifications" class="form-label">Technical Specifications</label>
                                    <textarea class="form-control @error('technical_specifications') is-invalid @enderror" 
                                              id="technical_specifications" name="technical_specifications" rows="8"
                                              placeholder="Technical details, specifications, compatibility information...">{{ old('technical_specifications') }}</textarea>
                                    @error('technical_specifications')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">
                                        Include technical details, specifications, dimensions, 
                                        compatibility information, system requirements, etc.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SEO Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            SEO Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="meta_title" class="form-label">Meta Title</label>
                            <input type="text" class="form-control @error('meta_title') is-invalid @enderror" 
                                   id="meta_title" name="meta_title" value="{{ old('meta_title') }}"
                                   maxlength="255" placeholder="SEO-optimized title for search engines">
                            @error('meta_title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Recommended: 50-60 characters. Leave empty to use product name.</small>
                        </div>

                        <div class="mb-3">
                            <label for="meta_description" class="form-label">Meta Description</label>
                            <textarea class="form-control @error('meta_description') is-invalid @enderror" 
                                      id="meta_description" name="meta_description" rows="3" 
                                      maxlength="500" placeholder="Brief description for search engine results...">{{ old('meta_description') }}</textarea>
                            @error('meta_description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Recommended: 150-160 characters</small>
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                            <input type="text" class="form-control @error('meta_keywords') is-invalid @enderror" 
                                   id="meta_keywords" name="meta_keywords" value="{{ old('meta_keywords') }}"
                                   placeholder="keyword1, keyword2, keyword3">
                            @error('meta_keywords')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Comma-separated keywords relevant to this product</small>
                        </div>

                        <div class="mb-3">
                            <label for="tags" class="form-label">Product Tags</label>
                            <input type="text" class="form-control @error('tags') is-invalid @enderror" 
                                   id="tags" name="tags" value="{{ old('tags') }}"
                                   placeholder="tag1, tag2, tag3">
                            @error('tags')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">Comma-separated tags for internal organization and filtering</small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-grid gap-2 d-md-flex justify-content-md-between">
                    <a href="{{ route('admin.products.create') }}" class="btn btn-outline-secondary" id="prevBtn">
                        <i class="fas fa-arrow-left me-1"></i> Previous: Basic Info
                    </a>
                    <button type="submit" class="btn btn-primary" id="nextBtn">
                        <i class="fas fa-arrow-right me-1"></i> Next: Pricing & Inventory
                    </button>
                </div>
            </form>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Progress Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Step 2 of 4
                    </h5>
                </div>
                <div class="card-body">
                    <h6>Content & SEO</h6>
                    <p class="text-muted mb-3">
                        Add detailed product content and optimize for search engines.
                    </p>
                    
                    <div class="progress mb-3">
                        <div class="progress-bar" role="progressbar" style="width: 50%">50%</div>
                    </div>
                    
                    <h6>Current Product:</h6>
                    <div class="bg-light p-3 rounded mb-3">
                        <strong>{{ $step1Data['name'] }}</strong><br>
                        <small class="text-muted">{{ $step1Data['short_description'] ?? 'No short description' }}</small>
                    </div>
                    
                    <h6>What's Next?</h6>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-1">
                            <i class="fas fa-arrow-right text-primary me-2"></i>
                            <small>Pricing & inventory settings</small>
                        </li>
                        <li>
                            <i class="fas fa-arrow-right text-muted me-2"></i>
                            <small>Images & media upload</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Content Tips -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Content Tips
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Description:</strong> Focus on benefits and features</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Additional Info:</strong> Include usage and care instructions</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Technical Specs:</strong> List detailed specifications</small>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>SEO:</strong> Use relevant keywords naturally</small>
                        </li>
                        <li>
                            <i class="fas fa-check text-success me-2"></i>
                            <small><strong>Tags:</strong> Help with internal organization</small>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- SEO Preview -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        SEO Preview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="seo-preview">
                        <div class="seo-title text-primary" id="seo-title-preview">
                            {{ $step1Data['name'] }}
                        </div>
                        <div class="seo-url text-success small" id="seo-url-preview">
                            https://yoursite.com/products/{{ $step1Data['slug'] ?? 'product-slug' }}
                        </div>
                        <div class="seo-description text-muted small" id="seo-description-preview">
                            {{ $step1Data['short_description'] ?? 'Product description will appear here...' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        margin: 0 20px;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .step.active .step-number {
        background-color: #0d6efd;
        color: white;
    }

    .step.completed .step-number {
        background-color: #198754;
        color: white;
    }

    .step-title {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6c757d;
        text-align: center;
    }

    .step.active .step-title {
        color: #0d6efd;
    }

    .step.completed .step-title {
        color: #198754;
    }

    .seo-preview {
        border: 1px solid #e9ecef;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }

    .seo-title {
        font-size: 1.125rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
        cursor: pointer;
    }

    .seo-title:hover {
        text-decoration: underline;
    }

    .seo-url {
        margin-bottom: 0.5rem;
    }

    .seo-description {
        line-height: 1.4;
    }

    /* Loading button styles */
    .btn.loading {
        position: relative;
        pointer-events: none;
        opacity: 0.8;
    }

    .btn.loading .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush

@push('scripts')
<script>
    // Update SEO preview in real-time
    document.getElementById('meta_title').addEventListener('input', function() {
        const preview = document.getElementById('seo-title-preview');
        preview.textContent = this.value || '{{ $step1Data["name"] }}';
    });

    document.getElementById('meta_description').addEventListener('input', function() {
        const preview = document.getElementById('seo-description-preview');
        preview.textContent = this.value || '{{ $step1Data["short_description"] ?? "Product description will appear here..." }}';
    });

    // Character counters
    function addCharacterCounter(elementId, maxLength) {
        const element = document.getElementById(elementId);
        const counter = document.createElement('small');
        counter.className = 'text-muted float-end';
        element.parentNode.appendChild(counter);
        
        function updateCounter() {
            const remaining = maxLength - element.value.length;
            counter.textContent = `${element.value.length}/${maxLength}`;
            counter.className = remaining < 20 ? 'text-danger float-end' : (remaining < 50 ? 'text-warning float-end' : 'text-muted float-end');
        }
        
        element.addEventListener('input', updateCounter);
        updateCounter();
    }

    // Add character counters
    addCharacterCounter('meta_title', 255);
    addCharacterCounter('meta_description', 500);

    // Auto-suggest meta title and description
    document.addEventListener('DOMContentLoaded', function() {
        const metaTitle = document.getElementById('meta_title');
        const metaDescription = document.getElementById('meta_description');
        
        if (!metaTitle.value) {
            metaTitle.placeholder = '{{ $step1Data["name"] }} - Professional Quality';
        }
        
        if (!metaDescription.value && '{{ $step1Data["short_description"] }}') {
            metaDescription.placeholder = '{{ $step1Data["short_description"] }}';
        }
    });

    // Rich text editor initialization (you can add TinyMCE or similar here)
    // For now, we'll use simple textareas with enhanced functionality
    
    // Tab persistence
    const tabTriggerList = [].slice.call(document.querySelectorAll('#contentTabs button'));
    tabTriggerList.forEach(function (tabTrigger) {
        tabTrigger.addEventListener('click', function (event) {
            localStorage.setItem('activeContentTab', event.target.getAttribute('data-bs-target'));
        });
    });

    // Restore active tab
    const activeTab = localStorage.getItem('activeContentTab');
    if (activeTab) {
        const tabTrigger = document.querySelector(`#contentTabs button[data-bs-target="${activeTab}"]`);
        if (tabTrigger) {
            const tab = new bootstrap.Tab(tabTrigger);
            tab.show();
        }
    }

    // Add spinner to form submission
    document.querySelector('form').addEventListener('submit', function(e) {
        const nextBtn = document.getElementById('nextBtn');
        if (nextBtn) {
            nextBtn.disabled = true;
            nextBtn.classList.add('loading');
            nextBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';
        }
    });

    // Add spinner to previous button click
    document.getElementById('prevBtn').addEventListener('click', function(e) {
        this.classList.add('loading');
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Loading...';
    });
</script>
@endpush
