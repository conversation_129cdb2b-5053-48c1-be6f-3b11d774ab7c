<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Response - {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 30px 20px;
        }
        .quote-info {
            background: #f8f9fa;
            border-left: 4px solid #2563eb;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 4px 4px 0;
        }
        .quote-info h3 {
            margin: 0 0 15px 0;
            color: #2563eb;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .info-label {
            font-weight: bold;
            color: #666;
        }
        .info-value {
            color: #333;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .priority-high { background: #f8d7da; color: #721c24; }
        .priority-medium { background: #fff3cd; color: #856404; }
        .priority-low { background: #d1ecf1; color: #0c5460; }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 0 10px 10px 0;
        }
        .cta-button:hover {
            background: #1d4ed8;
        }
        .cta-button.secondary {
            background: #6c757d;
        }
        .cta-button.secondary:hover {
            background: #5a6268;
        }
        .service-details {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .service-details h4 {
            margin: 0 0 15px 0;
            color: #2563eb;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
            border-top: 1px solid #e9ecef;
        }
        .footer a {
            color: #2563eb;
            text-decoration: none;
        }
        .attachment-notice {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .attachment-notice .icon {
            color: #2563eb;
            margin-right: 8px;
        }
        @media (max-width: 600px) {
            body { padding: 10px; }
            .content { padding: 20px 15px; }
            .info-row { flex-direction: column; }
            .info-label { margin-bottom: 5px; }
            .cta-button { display: block; margin: 10px 0; }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <h1>Quote Response</h1>
            <p>Your quote request has been processed</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>Dear {{ $quote->customer_name }},</p>
            
            <p>Thank you for your interest in our logistics services. We have prepared a detailed quote for your requirements.</p>

            <!-- Quote Information -->
            <div class="quote-info">
                <h3>Quote Details</h3>
                <div class="info-row">
                    <span class="info-label">Quote Number:</span>
                    <span class="info-value"><strong>{{ $quote->quote_number }}</strong></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Service Type:</span>
                    <span class="info-value">{{ ucfirst($quote->service_type) }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Status:</span>
                    <span class="info-value">
                        <span class="status-badge status-{{ $quote->status }}">{{ ucfirst($quote->status) }}</span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Priority:</span>
                    <span class="info-value">
                        <span class="status-badge priority-{{ $quote->priority }}">{{ ucfirst($quote->priority) }}</span>
                    </span>
                </div>
                @if($quote->quoted_price)
                <div class="info-row">
                    <span class="info-label">Quoted Price:</span>
                    <span class="info-value"><strong>@currency($quote->quoted_price)</strong></span>
                </div>
                @endif
                @if($quote->expires_at)
                <div class="info-row">
                    <span class="info-label">Valid Until:</span>
                    <span class="info-value">{{ $quote->expires_at->format('M j, Y') }}</span>
                </div>
                @endif
            </div>

            <!-- Service Details -->
            @if($quote->origin_address || $quote->destination_address)
            <div class="service-details">
                <h4>Service Requirements</h4>
                @if($quote->origin_address)
                    <p><strong>Origin:</strong> {{ $quote->origin_address }}</p>
                @endif
                @if($quote->destination_address)
                    <p><strong>Destination:</strong> {{ $quote->destination_address }}</p>
                @endif
                @if($quote->package_details)
                    <p><strong>Package Details:</strong> {{ $quote->package_details }}</p>
                @endif
                @if($quote->special_requirements)
                    <p><strong>Special Requirements:</strong> {{ $quote->special_requirements }}</p>
                @endif
            </div>
            @endif

            <!-- Attachment Notice -->
            <div class="attachment-notice">
                <span class="icon">📎</span>
                <strong>Detailed Quote Attached:</strong> Please find the complete quote document attached to this email with full terms and conditions.
            </div>

            <!-- Next Steps -->
            @if($quote->status === 'pending')
            <p>Our team is currently reviewing your requirements. We will provide you with a detailed quote within 24 hours.</p>
            @elseif($quote->status === 'approved')
            <p>Your quote has been approved and is ready for your review. Please find the detailed quote attached to this email.</p>
            
            <div class="cta-section">
                <a href="{{ $quoteUrl }}" class="cta-button">View Quote Online</a>
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}?subject=Quote {{ $quote->quote_number }} - Accept" class="cta-button">Accept Quote</a>
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}?subject=Quote {{ $quote->quote_number }} - Questions" class="cta-button secondary">Ask Questions</a>
            </div>
            @endif

            <!-- Contact Information -->
            <p>If you have any questions about this quote, please don't hesitate to contact us:</p>
            <ul>
                <li><strong>Phone:</strong> {{ $siteSettings['company_phone'] ?? 'Phone Number' }}</li>
                <li><strong>Email:</strong> {{ $siteSettings['company_email'] ?? '<EMAIL>' }}</li>
                @if(isset($siteSettings['company_website']))
                <li><strong>Website:</strong> {{ $siteSettings['company_website'] }}</li>
                @endif
            </ul>

            <p>Thank you for choosing {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }} for your logistics needs.</p>

            <p>Best regards,<br>
            <strong>{{ $quote->assignedTo->name ?? 'Customer Service Team' }}</strong><br>
            {{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p>
                <strong>{{ $siteSettings['site_name'] ?? 'Atrix Logistics' }}</strong><br>
                {{ $siteSettings['company_address'] ?? 'Company Address' }}<br>
                {{ $siteSettings['company_phone'] ?? 'Phone' }} | 
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}">{{ $siteSettings['company_email'] ?? '<EMAIL>' }}</a>
            </p>
            <p style="font-size: 12px; margin-top: 15px;">
                This email was sent regarding quote #{{ $quote->quote_number }}. 
                <a href="{{ $quoteUrl }}">View online</a> | 
                <a href="mailto:{{ $siteSettings['company_email'] ?? '<EMAIL>' }}">Contact Support</a>
            </p>
        </div>
    </div>
</body>
</html>
