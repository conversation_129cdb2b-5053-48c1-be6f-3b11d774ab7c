<?php

namespace App\Exports;

use App\Models\Order;
use App\Helpers\CurrencyHelper;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Illuminate\Http\Request;

class OrdersExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Order::with(['customer', 'items.product']);

        // Apply filters if request is provided
        if ($this->request) {
            if ($this->request->filled('status')) {
                $query->where('status', $this->request->status);
            }

            if ($this->request->filled('payment_status')) {
                $query->where('payment_status', $this->request->payment_status);
            }

            if ($this->request->filled('customer_id')) {
                $query->where('customer_id', $this->request->customer_id);
            }

            if ($this->request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $this->request->date_from);
            }

            if ($this->request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $this->request->date_to);
            }

            if ($this->request->filled('min_amount')) {
                $query->where('total_amount', '>=', $this->request->min_amount);
            }

            if ($this->request->filled('max_amount')) {
                $query->where('total_amount', '<=', $this->request->max_amount);
            }
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Order Number',
            'Customer Name',
            'Customer Email',
            'Status',
            'Payment Status',
            'Subtotal (' . CurrencyHelper::getCode() . ')',
            'Tax Amount (' . CurrencyHelper::getCode() . ')',
            'Shipping Cost (' . CurrencyHelper::getCode() . ')',
            'Discount Amount (' . CurrencyHelper::getCode() . ')',
            'Total Amount (' . CurrencyHelper::getCode() . ')',
            'Items Count',
            'Billing Address',
            'Shipping Address',
            'Notes',
            'Created Date',
            'Updated Date',
        ];
    }

    /**
     * @param mixed $order
     * @return array
     */
    public function map($order): array
    {
        // Format addresses
        $billingAddress = $this->formatAddress([
            'name' => $order->billing_name,
            'address_line_1' => $order->billing_address_line_1,
            'address_line_2' => $order->billing_address_line_2,
            'city' => $order->billing_city,
            'state' => $order->billing_state,
            'postal_code' => $order->billing_postal_code,
            'country' => $order->billing_country,
        ]);

        $shippingAddress = $this->formatAddress([
            'name' => $order->shipping_name,
            'address_line_1' => $order->shipping_address_line_1,
            'address_line_2' => $order->shipping_address_line_2,
            'city' => $order->shipping_city,
            'state' => $order->shipping_state,
            'postal_code' => $order->shipping_postal_code,
            'country' => $order->shipping_country,
        ]);

        return [
            $order->order_number,
            $order->customer->name ?? 'Guest',
            $order->customer->email ?? $order->customer_email,
            ucfirst(str_replace('_', ' ', $order->status)),
            ucfirst(str_replace('_', ' ', $order->payment_status)),
            $order->subtotal,
            $order->tax_amount,
            $order->shipping_cost,
            $order->discount_amount,
            $order->total_amount,
            $order->items->count(),
            $billingAddress,
            $shippingAddress,
            $order->notes,
            $order->created_at->format('Y-m-d H:i:s'),
            $order->updated_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Format address array into a string
     */
    private function formatAddress(array $address): string
    {
        $parts = array_filter([
            $address['name'],
            $address['address_line_1'],
            $address['address_line_2'],
            $address['city'],
            $address['state'],
            $address['postal_code'],
            $address['country'],
        ]);

        return implode(', ', $parts);
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
            // Add borders to all cells
            'A1:P1000' => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    ],
                ],
            ],
        ];
    }
}
