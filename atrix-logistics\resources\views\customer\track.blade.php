@extends('layouts.customer')

@section('title', 'Track Package')
@section('page-title', 'Track Your Package')

@push('styles')
<script src="https://cdn.tailwindcss.com"></script>
<style>
    .tracking-step {
        transition: all 0.3s ease;
    }
    
    .tracking-step.completed {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }
    
    .tracking-step.active {
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        color: white;
        animation: pulse 2s infinite;
    }
    
    .tracking-step.pending {
        background: #f3f4f6;
        color: #6b7280;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }
    
    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .loading-spinner {
        border: 3px solid #f3f3f4;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Track Your Package</h1>
            <p class="text-gray-600">Enter your tracking number to get real-time updates</p>
        </div>

        <!-- Search Form -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form id="trackingForm" class="space-y-4">
                @csrf
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <label for="tracking_number" class="block text-sm font-medium text-gray-700 mb-2">
                            Tracking Number
                        </label>
                        <input 
                            type="text" 
                            id="tracking_number" 
                            name="tracking_number"
                            value="{{ $trackingNumber ?? '' }}"
                            placeholder="Enter your tracking number (e.g., TRK123456789)"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                            required
                        >
                    </div>
                    <div class="sm:self-end">
                        <button 
                            type="submit" 
                            id="trackButton"
                            class="w-full sm:w-auto px-8 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 flex items-center justify-center"
                        >
                            <span id="buttonText">Track Package</span>
                            <div id="loadingSpinner" class="loading-spinner ml-2 hidden"></div>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Loading State -->
        <div id="loadingState" class="hidden">
            <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <p class="text-gray-600">Searching for your package...</p>
            </div>
        </div>

        <!-- Error State -->
        <div id="errorState" class="hidden">
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Package Not Found</h3>
                        <p class="mt-1 text-sm text-red-700" id="errorMessage">
                            We couldn't find a package with that tracking number. Please check the number and try again.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Container -->
        <div id="resultsContainer" class="hidden">
            <!-- Package Information -->
            <div id="packageInfo" class="bg-white rounded-lg shadow-lg p-6 mb-6 fade-in">
                <div class="border-b border-gray-200 pb-4 mb-4">
                    <h2 class="text-xl font-semibold text-gray-900">Package Information</h2>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <p class="text-sm font-medium text-gray-500">Tracking Number</p>
                        <p class="mt-1 text-lg font-semibold text-gray-900" id="displayTrackingNumber"></p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Status</p>
                        <span class="mt-1 inline-flex px-3 py-1 rounded-full text-sm font-medium" id="statusBadge"></span>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Estimated Delivery</p>
                        <p class="mt-1 text-lg text-gray-900" id="estimatedDelivery"></p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Origin</p>
                        <p class="mt-1 text-gray-900" id="originLocation"></p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Destination</p>
                        <p class="mt-1 text-gray-900" id="destinationLocation"></p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500">Carrier</p>
                        <p class="mt-1 text-gray-900" id="carrierName"></p>
                    </div>
                </div>
            </div>

            <!-- Tracking Progress -->
            <div id="trackingProgress" class="bg-white rounded-lg shadow-lg p-6 mb-6 fade-in">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Tracking Progress</h2>
                </div>
                
                <div class="relative">
                    <!-- Progress Line -->
                    <div class="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200" id="progressLine"></div>
                    
                    <!-- Progress Steps -->
                    <div class="space-y-6" id="progressSteps">
                        <!-- Steps will be dynamically added here -->
                    </div>
                </div>
            </div>

            <!-- Tracking Events -->
            <div id="trackingEvents" class="bg-white rounded-lg shadow-lg p-6 fade-in">
                <div class="border-b border-gray-200 pb-4 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Tracking History</h2>
                </div>
                
                <div class="space-y-4" id="eventsList">
                    <!-- Events will be dynamically added here -->
                </div>
            </div>
        </div>

        @if($parcel)
            <!-- Initial Data (if tracking number provided in URL) -->
            <script>
                window.initialParcelData = @json($parcel);
                window.initialTrackingNumber = @json($trackingNumber);
            </script>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('trackingForm');
    const trackButton = document.getElementById('trackButton');
    const buttonText = document.getElementById('buttonText');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadingState = document.getElementById('loadingState');
    const errorState = document.getElementById('errorState');
    const resultsContainer = document.getElementById('resultsContainer');

    // If initial data exists, display it
    if (window.initialParcelData && window.initialTrackingNumber) {
        displayTrackingResults(window.initialParcelData);
    }

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const trackingNumber = document.getElementById('tracking_number').value.trim();
        
        if (!trackingNumber) {
            showError('Please enter a tracking number');
            return;
        }

        trackPackage(trackingNumber);
    });

    function trackPackage(trackingNumber) {
        // Show loading state
        showLoading();
        
        // Make AJAX request
        fetch(`/customer/track?tracking_number=${encodeURIComponent(trackingNumber)}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success && data.parcel) {
                displayTrackingResults(data.parcel);
            } else {
                showError(data.message || 'Package not found');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showError('An error occurred while tracking your package. Please try again.');
        });
    }

    function showLoading() {
        buttonText.textContent = 'Tracking...';
        loadingSpinner.classList.remove('hidden');
        trackButton.disabled = true;
        
        hideStates();
        loadingState.classList.remove('hidden');
    }

    function hideLoading() {
        buttonText.textContent = 'Track Package';
        loadingSpinner.classList.add('hidden');
        trackButton.disabled = false;
        
        loadingState.classList.add('hidden');
    }

    function showError(message) {
        hideStates();
        document.getElementById('errorMessage').textContent = message;
        errorState.classList.remove('hidden');
    }

    function hideStates() {
        loadingState.classList.add('hidden');
        errorState.classList.add('hidden');
        resultsContainer.classList.add('hidden');
    }

    function displayTrackingResults(parcel) {
        hideStates();
        
        // Update package information
        document.getElementById('displayTrackingNumber').textContent = parcel.tracking_number;
        document.getElementById('estimatedDelivery').textContent = parcel.estimated_delivery_date || 'Not available';
        document.getElementById('originLocation').textContent = `${parcel.origin_city}, ${parcel.origin_country}`;
        document.getElementById('destinationLocation').textContent = `${parcel.destination_city}, ${parcel.destination_country}`;
        document.getElementById('carrierName').textContent = parcel.carrier?.name || 'Standard Delivery';
        
        // Update status badge
        const statusBadge = document.getElementById('statusBadge');
        statusBadge.textContent = parcel.status.charAt(0).toUpperCase() + parcel.status.slice(1);
        statusBadge.className = `mt-1 inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(parcel.status)}`;
        
        // Update progress steps
        updateProgressSteps(parcel.status);
        
        // Update tracking events
        updateTrackingEvents(parcel.tracking_events || []);
        
        // Show results
        resultsContainer.classList.remove('hidden');
    }

    function getStatusColor(status) {
        const colors = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'picked_up': 'bg-blue-100 text-blue-800',
            'in_transit': 'bg-purple-100 text-purple-800',
            'out_for_delivery': 'bg-orange-100 text-orange-800',
            'delivered': 'bg-green-100 text-green-800',
            'exception': 'bg-red-100 text-red-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    }

    function updateProgressSteps(currentStatus) {
        const steps = [
            { key: 'pending', label: 'Order Placed', icon: '📦' },
            { key: 'picked_up', label: 'Picked Up', icon: '🚚' },
            { key: 'in_transit', label: 'In Transit', icon: '✈️' },
            { key: 'out_for_delivery', label: 'Out for Delivery', icon: '🚛' },
            { key: 'delivered', label: 'Delivered', icon: '✅' }
        ];

        const statusOrder = ['pending', 'picked_up', 'in_transit', 'out_for_delivery', 'delivered'];
        const currentIndex = statusOrder.indexOf(currentStatus);
        
        const progressSteps = document.getElementById('progressSteps');
        progressSteps.innerHTML = '';

        steps.forEach((step, index) => {
            const isCompleted = index < currentIndex;
            const isActive = index === currentIndex;
            const isPending = index > currentIndex;

            const stepClass = isCompleted ? 'completed' : isActive ? 'active' : 'pending';

            const stepElement = document.createElement('div');
            stepElement.className = 'relative flex items-center';
            stepElement.innerHTML = `
                <div class="tracking-step ${stepClass} flex items-center justify-center w-16 h-16 rounded-full text-2xl font-bold relative z-10">
                    ${step.icon}
                </div>
                <div class="ml-4">
                    <p class="text-lg font-semibold ${isCompleted || isActive ? 'text-gray-900' : 'text-gray-500'}">${step.label}</p>
                    <p class="text-sm text-gray-500">${isCompleted ? 'Completed' : isActive ? 'In Progress' : 'Pending'}</p>
                </div>
            `;
            
            progressSteps.appendChild(stepElement);
        });
    }

    function updateTrackingEvents(events) {
        const eventsList = document.getElementById('eventsList');
        eventsList.innerHTML = '';

        if (events.length === 0) {
            eventsList.innerHTML = '<p class="text-gray-500 text-center py-4">No tracking events available</p>';
            return;
        }

        events.forEach(event => {
            const eventElement = document.createElement('div');
            eventElement.className = 'border-l-4 border-blue-500 pl-4 py-3';
            eventElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <div>
                        <p class="font-semibold text-gray-900">${event.description}</p>
                        <p class="text-sm text-gray-600">${event.location || 'Location not specified'}</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm font-medium text-gray-900">${new Date(event.event_date).toLocaleDateString()}</p>
                        <p class="text-sm text-gray-500">${new Date(event.event_date).toLocaleTimeString()}</p>
                    </div>
                </div>
            `;
            eventsList.appendChild(eventElement);
        });
    }
});
</script>
@endpush
