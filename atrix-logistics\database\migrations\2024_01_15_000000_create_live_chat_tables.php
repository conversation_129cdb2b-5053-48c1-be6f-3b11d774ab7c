<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Live chat sessions table
        Schema::create('live_chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->unique(); // Unique identifier for anonymous users
            $table->string('visitor_name')->nullable();
            $table->string('visitor_email')->nullable();
            $table->string('visitor_ip')->nullable();
            $table->string('user_agent')->nullable();
            $table->enum('status', ['active', 'closed', 'waiting'])->default('waiting');
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null'); // Staff/admin assigned
            $table->timestamp('last_activity')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'assigned_to']);
            $table->index('session_id');
        });

        // Live chat messages table
        Schema::create('live_chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('session_id')->constrained('live_chat_sessions')->onDelete('cascade');
            $table->enum('sender_type', ['visitor', 'staff']); // Who sent the message
            $table->foreignId('staff_id')->nullable()->constrained('users')->onDelete('set null'); // If sent by staff
            $table->text('message');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
            
            $table->index(['session_id', 'created_at']);
            $table->index(['is_read', 'sender_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('live_chat_messages');
        Schema::dropIfExists('live_chat_sessions');
    }
};
