# 🎨 UI/UX Design System - Atrix Logistics

## 🎯 Design Philosophy

### Brand Identity
**Professional Logistics Company**
- Modern, clean, and trustworthy design
- Corporate blue and orange color scheme
- Professional typography
- Consistent visual hierarchy
- Mobile-first responsive approach

### User Experience Principles
1. **Clarity First** - Clear navigation and information hierarchy
2. **Speed & Performance** - Fast loading, optimized interactions
3. **Accessibility** - WCAG 2.1 AA compliance
4. **Consistency** - Uniform design patterns across all pages
5. **Trust & Security** - Professional appearance builds confidence

---

## 🎨 Color Palette

### Primary Colors
```css
/* Primary Blue - Main brand color */
--primary-blue: #1e3a8a;      /* rgb(30, 58, 138) */
--primary-blue-light: #3b82f6; /* rgb(59, 130, 246) */
--primary-blue-dark: #1e40af;  /* rgb(30, 64, 175) */

/* Secondary Orange - Accent color */
--secondary-orange: #f97316;   /* rgb(249, 115, 22) */
--secondary-orange-light: #fb923c; /* rgb(251, 146, 60) */
--secondary-orange-dark: #ea580c;  /* rgb(234, 88, 12) */
```

### Neutral Colors
```css
/* Grays for text and backgrounds */
--gray-50: #f9fafb;   /* Light backgrounds */
--gray-100: #f3f4f6;  /* Card backgrounds */
--gray-200: #e5e7eb;  /* Borders */
--gray-300: #d1d5db;  /* Disabled states */
--gray-400: #9ca3af;  /* Placeholder text */
--gray-500: #6b7280;  /* Secondary text */
--gray-600: #4b5563;  /* Primary text */
--gray-700: #374151;  /* Headings */
--gray-800: #1f2937;  /* Dark headings */
--gray-900: #111827;  /* Darkest text */
```

### Status Colors
```css
/* Success - Delivered, Completed */
--success-green: #10b981;     /* rgb(16, 185, 129) */
--success-green-light: #34d399; /* rgb(52, 211, 153) */

/* Warning - In Transit, Processing */
--warning-yellow: #f59e0b;    /* rgb(245, 158, 11) */
--warning-yellow-light: #fbbf24; /* rgb(251, 191, 36) */

/* Error - Failed, Cancelled */
--error-red: #ef4444;         /* rgb(239, 68, 68) */
--error-red-light: #f87171;   /* rgb(248, 113, 113) */

/* Info - Pending, New */
--info-blue: #3b82f6;         /* rgb(59, 130, 246) */
--info-blue-light: #60a5fa;   /* rgb(96, 165, 250) */
```

---

## 📝 Typography

### Font Stack
```css
/* Primary Font - Modern Sans-serif */
font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;

/* Secondary Font - For headings */
font-family: 'Poppins', 'Inter', 'Segoe UI', sans-serif;

/* Monospace - For tracking numbers, codes */
font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
```

### Font Sizes & Hierarchy
```css
/* Headings */
.text-6xl { font-size: 3.75rem; line-height: 1; }      /* Hero titles */
.text-5xl { font-size: 3rem; line-height: 1; }        /* Page titles */
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; } /* Section titles */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* Card titles */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }    /* Subsection titles */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; } /* Large text */

/* Body Text */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; } /* Large body */
.text-base { font-size: 1rem; line-height: 1.5rem; }    /* Default body */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; } /* Small text */
.text-xs { font-size: 0.75rem; line-height: 1rem; }     /* Captions */
```

### Font Weights
```css
.font-thin { font-weight: 100; }      /* Rarely used */
.font-light { font-weight: 300; }     /* Light text */
.font-normal { font-weight: 400; }    /* Default body */
.font-medium { font-weight: 500; }    /* Emphasized text */
.font-semibold { font-weight: 600; }  /* Subheadings */
.font-bold { font-weight: 700; }      /* Headings */
.font-extrabold { font-weight: 800; } /* Hero text */
```

---

## 🧩 Component Library

### Buttons
```css
/* Primary Button */
.btn-primary {
  background: var(--primary-blue);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-blue-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 58, 138, 0.3);
}

/* Secondary Button */
.btn-secondary {
  background: var(--secondary-orange);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
}

/* Outline Button */
.btn-outline {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
  padding: 10px 22px;
  border-radius: 8px;
}
```

### Cards
```css
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: 16px;
  margin-bottom: 16px;
}
```

### Forms
```css
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--gray-200);
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 8px;
}
```

---

## 📱 Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
/* xs: 0px - 475px (default) */
/* sm: 476px - 639px */
@media (min-width: 476px) { /* Small devices */ }

/* md: 640px - 767px */
@media (min-width: 640px) { /* Medium devices */ }

/* lg: 768px - 1023px */
@media (min-width: 768px) { /* Large devices */ }

/* xl: 1024px - 1279px */
@media (min-width: 1024px) { /* Extra large devices */ }

/* 2xl: 1280px+ */
@media (min-width: 1280px) { /* 2X large devices */ }
```

### Grid System
```css
/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

/* Grid Layout */
.grid {
  display: grid;
  gap: 24px;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

/* Responsive Grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
}
```

---

## 🎭 Page Layout Structure

### Header Layout
```html
<header class="header-main">
  <div class="container">
    <nav class="navbar">
      <div class="navbar-brand">
        <img src="logo.png" alt="Atrix Logistics">
      </div>
      <div class="navbar-menu">
        <!-- Navigation items -->
      </div>
      <div class="navbar-actions">
        <!-- Search, Login, Cart -->
      </div>
    </nav>
  </div>
</header>
```

### Main Content Layout
```html
<main class="main-content">
  <!-- Hero Section (Homepage only) -->
  <section class="hero-section">
    <!-- Hero content -->
  </section>
  
  <!-- Page Content -->
  <section class="content-section">
    <div class="container">
      <!-- Page specific content -->
    </div>
  </section>
</main>
```

### Footer Layout
```html
<footer class="footer-main">
  <div class="container">
    <div class="footer-content">
      <!-- Company info, links, contact -->
    </div>
    <div class="footer-bottom">
      <!-- Copyright, legal links -->
    </div>
  </div>
</footer>
```

---

## 🎨 Animation & Interactions

### Hover Effects
```css
/* Button Hover */
.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Card Hover */
.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Link Hover */
.link:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}
```

### Loading States
```css
/* Spinner */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--gray-200);
  border-top: 4px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Skeleton Loading */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

### Page Transitions
```css
/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Slide In Animation */
.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}
```

---

## 🔍 Accessibility Guidelines

### Color Contrast
- **Normal text:** Minimum 4.5:1 contrast ratio
- **Large text:** Minimum 3:1 contrast ratio
- **Interactive elements:** Clear focus indicators

### Keyboard Navigation
- All interactive elements accessible via keyboard
- Logical tab order throughout pages
- Skip links for main content areas
- Clear focus indicators

### Screen Reader Support
- Semantic HTML structure
- Proper heading hierarchy (h1 → h6)
- Alt text for all images
- ARIA labels for complex interactions

### Form Accessibility
- Labels associated with form controls
- Error messages clearly identified
- Required fields properly marked
- Helpful instructions provided

---

## 📊 Performance Guidelines

### Image Optimization
- WebP format with fallbacks
- Responsive images with srcset
- Lazy loading for below-fold images
- Compressed file sizes

### CSS Optimization
- Critical CSS inlined
- Non-critical CSS loaded asynchronously
- CSS minification and compression
- Unused CSS removal

### JavaScript Performance
- Minimal JavaScript for core functionality
- Async/defer loading for non-critical scripts
- Code splitting for large applications
- Performance monitoring

### Loading Performance
- **First Contentful Paint:** <1.5s
- **Largest Contentful Paint:** <2.5s
- **Cumulative Layout Shift:** <0.1
- **First Input Delay:** <100ms
