<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Make shipping-related fields nullable for product quotes
            $table->string('origin_address')->nullable()->change();
            $table->string('origin_city')->nullable()->change();
            $table->string('origin_country')->nullable()->change();
            $table->string('destination_address')->nullable()->change();
            $table->string('destination_city')->nullable()->change();
            $table->string('destination_country')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Revert the changes - make fields required again
            $table->string('origin_address')->nullable(false)->change();
            $table->string('origin_city')->nullable(false)->change();
            $table->string('origin_country')->nullable(false)->change();
            $table->string('destination_address')->nullable(false)->change();
            $table->string('destination_city')->nullable(false)->change();
            $table->string('destination_country')->nullable(false)->change();
        });
    }
};
