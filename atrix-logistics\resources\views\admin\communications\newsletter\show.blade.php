@extends('layouts.admin')

@section('title', 'Newsletter Subscriber - ' . $newsletter->email)

@section('page-header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="h3 mb-0">Newsletter Subscriber</h1>
            <p class="text-muted">{{ $newsletter->email }} - Subscribed {{ $newsletter->subscribed_at->format('M j, Y') }}</p>
        </div>
        <div>
            <a href="{{ route('admin.communications.newsletter.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Subscribers
            </a>
        </div>
    </div>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Subscriber Details -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-user me-2"></i>
                        Subscriber Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Email:</strong></div>
                        <div class="col-sm-9">
                            <a href="mailto:{{ $newsletter->email }}">{{ $newsletter->email }}</a>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Name:</strong></div>
                        <div class="col-sm-9">{{ $newsletter->name ?: 'Not provided' }}</div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Status:</strong></div>
                        <div class="col-sm-9">
                            <span class="text-dark badge {{ $newsletter->status_badge }}">
                                {{ ucfirst($newsletter->status) }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Subscribed:</strong></div>
                        <div class="col-sm-9">{{ $newsletter->subscribed_at->format('F j, Y \a\t g:i A') }}</div>
                    </div>

                    @if($newsletter->unsubscribed_at)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Unsubscribed:</strong></div>
                        <div class="col-sm-9">{{ $newsletter->unsubscribed_at->format('F j, Y \a\t g:i A') }}</div>
                    </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Source:</strong></div>
                        <div class="col-sm-9">
                            <span class="text-dark badge badge-light">
                                {{ ucfirst(str_replace('_', ' ', $newsletter->subscription_source)) }}
                            </span>
                        </div>
                    </div>

                    @if($newsletter->ip_address)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>IP Address:</strong></div>
                        <div class="col-sm-9">{{ $newsletter->ip_address }}</div>
                    </div>
                    @endif

                    @if($newsletter->user_agent)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Browser:</strong></div>
                        <div class="col-sm-9">
                            <small class="text-muted">{{ $newsletter->user_agent }}</small>
                        </div>
                    </div>
                    @endif

                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Subscriber ID:</strong></div>
                        <div class="col-sm-9"><code>#{{ $newsletter->id }}</code></div>
                    </div>

                    @if($newsletter->unsubscribe_token)
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Unsubscribe Link:</strong></div>
                        <div class="col-sm-9">
                            <div class="input-group">
                                <input type="text" class="form-control" 
                                       value="{{ $newsletter->unsubscribe_url }}" 
                                       readonly id="unsubscribe-link">
                                <button class="btn btn-outline-secondary" type="button" 
                                        onclick="copyToClipboard('unsubscribe-link')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <small class="text-muted">This link allows the subscriber to unsubscribe</small>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-tools me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.communications.newsletter.edit', $newsletter) }}" 
                           class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>
                            Edit Subscriber
                        </a>

                        <a href="mailto:{{ $newsletter->email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>
                            Send Email
                        </a>

                        @if($newsletter->status === 'unsubscribed')
                            <form method="POST" action="{{ route('admin.communications.newsletter.update', $newsletter) }}" style="display: inline;">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="email" value="{{ $newsletter->email }}">
                                <input type="hidden" name="name" value="{{ $newsletter->name }}">
                                <input type="hidden" name="status" value="active">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-undo me-2"></i>
                                    Resubscribe
                                </button>
                            </form>
                        @elseif($newsletter->status === 'active')
                            <form method="POST" action="{{ route('admin.communications.newsletter.update', $newsletter) }}" style="display: inline;">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="email" value="{{ $newsletter->email }}">
                                <input type="hidden" name="name" value="{{ $newsletter->name }}">
                                <input type="hidden" name="status" value="unsubscribed">
                                <button type="submit" class="btn btn-warning w-100">
                                    <i class="fas fa-user-times me-2"></i>
                                    Unsubscribe
                                </button>
                            </form>
                        @endif

                        <form method="POST" action="{{ route('admin.communications.newsletter.destroy', $newsletter) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this subscriber?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>
                                Delete Subscriber
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Subscription History -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>
                        Subscription History
                    </h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Subscribed</h6>
                                <p class="timeline-text">
                                    {{ $newsletter->subscribed_at->format('F j, Y \a\t g:i A') }}
                                    <br><small class="text-muted">via {{ ucfirst(str_replace('_', ' ', $newsletter->subscription_source)) }}</small>
                                </p>
                            </div>
                        </div>

                        @if($newsletter->unsubscribed_at)
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Unsubscribed</h6>
                                <p class="timeline-text">
                                    {{ $newsletter->unsubscribed_at->format('F j, Y \a\t g:i A') }}
                                </p>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    
    // Show feedback
    const button = element.nextElementSibling;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-check"></i>';
    button.classList.remove('btn-outline-secondary');
    button.classList.add('btn-success');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 13px;
    color: #6c757d;
    margin-bottom: 0;
}
</style>
@endpush
@endsection
