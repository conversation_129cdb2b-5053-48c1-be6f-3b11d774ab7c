<?php

namespace App\Helpers;

use App\Services\ImageProcessingService;
use Illuminate\Http\UploadedFile;

class ImageHelper
{
    /**
     * Get the ImageProcessingService instance
     */
    public static function service(): ImageProcessingService
    {
        return app(ImageProcessingService::class);
    }

    /**
     * Quick helper methods for common image processing tasks
     */
    public static function processProduct(UploadedFile $file): string
    {
        return static::service()->processProductImage($file);
    }

    public static function processSlider(UploadedFile $file): string
    {
        return static::service()->processSliderImage($file);
    }

    public static function processCategory(UploadedFile $file): string
    {
        return static::service()->processCategoryImage($file);
    }

    public static function processTeamMember(UploadedFile $file): string
    {
        return static::service()->processTeamMemberPhoto($file);
    }

    public static function processWithOptions(UploadedFile $file, array $options = []): string
    {
        return static::service()->processImageWithOptions($file, $options);
    }

    public static function deleteImage(string $path): bool
    {
        return static::service()->deleteOldImage($path);
    }

    /**
     * Process multiple images at once
     */
    public static function processMultiple(array $files, string $type = 'product'): array
    {
        $processedPaths = [];
        
        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                try {
                    switch ($type) {
                        case 'product':
                            $processedPaths[] = static::processProduct($file);
                            break;
                        case 'slider':
                            $processedPaths[] = static::processSlider($file);
                            break;
                        case 'category':
                            $processedPaths[] = static::processCategory($file);
                            break;
                        case 'team':
                            $processedPaths[] = static::processTeamMember($file);
                            break;
                        default:
                            $processedPaths[] = static::processProduct($file);
                    }
                } catch (\Exception $e) {
                    \Log::error("Failed to process image: " . $e->getMessage());
                    // Skip failed images or handle as needed
                }
            }
        }
        
        return $processedPaths;
    }
}
