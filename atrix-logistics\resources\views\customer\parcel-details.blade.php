@extends('layouts.customer')

@section('title', 'Parcel Details - ' . $parcel->tracking_number)
@section('page-title', 'Parcel Details')

@section('page-actions')
<div class="d-flex gap-2">
    <a href="{{ route('customer.track', ['tracking_number' => $parcel->tracking_number]) }}" class="btn btn-outline-primary">
        <i class="fas fa-search-location me-2"></i>
        Track Package
    </a>
    <a href="{{ route('customer.parcels') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        Back to Parcels
    </a>
</div>
@endsection

@section('content')
<div class="row">
    <!-- Parcel Information -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        Package Information
                    </h5>
                    <span class="badge bg-{{ $parcel->status === 'delivered' ? 'success' : ($parcel->status === 'in_transit' ? 'primary' : ($parcel->status === 'pending' ? 'warning' : 'secondary')) }} fs-6">
                        {{ ucfirst(str_replace('_', ' ', $parcel->status)) }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Tracking Number</label>
                        <p class="mb-0">
                            <code class="fs-5">{{ $parcel->tracking_number }}</code>
                            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyToClipboard('{{ $parcel->tracking_number }}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Service Type</label>
                        <p class="mb-0">{{ ucfirst(str_replace('_', ' ', $parcel->service_type)) }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Description</label>
                        <p class="mb-0">{{ $parcel->description ?: 'No description provided' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Weight</label>
                        <p class="mb-0">{{ $parcel->weight }} kg</p>
                    </div>
                    @if($parcel->dimensions)
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Dimensions</label>
                        <p class="mb-0">{{ $parcel->dimensions }}</p>
                    </div>
                    @endif
                    @if($parcel->declared_value)
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Declared Value</label>
                        <p class="mb-0">@currency($parcel->declared_value)</p>
                    </div>
                    @endif
                </div>

                @if($parcel->special_instructions)
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Special Instructions:</strong> {{ $parcel->special_instructions }}
                </div>
                @endif
            </div>
        </div>

        <!-- Shipping Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shipping-fast me-2"></i>
                    Shipping Information
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">From</h6>
                        <address class="mb-0">
                            <strong>{{ $parcel->sender_name }}</strong><br>
                            {{ $parcel->sender_address }}<br>
                            {{ $parcel->sender_city }}, {{ $parcel->sender_state }} {{ $parcel->sender_postal_code }}<br>
                            {{ $parcel->sender_country }}
                            @if($parcel->sender_phone)
                                <br><i class="fas fa-phone me-1"></i> {{ $parcel->sender_phone }}
                            @endif
                            @if($parcel->sender_email)
                                <br><i class="fas fa-envelope me-1"></i> {{ $parcel->sender_email }}
                            @endif
                        </address>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-3">To</h6>
                        <address class="mb-0">
                            <strong>{{ $parcel->recipient_name }}</strong><br>
                            {{ $parcel->recipient_address }}<br>
                            {{ $parcel->recipient_city }}, {{ $parcel->recipient_state }} {{ $parcel->recipient_postal_code }}<br>
                            {{ $parcel->recipient_country }}
                            @if($parcel->recipient_phone)
                                <br><i class="fas fa-phone me-1"></i> {{ $parcel->recipient_phone }}
                            @endif
                            @if($parcel->recipient_email)
                                <br><i class="fas fa-envelope me-1"></i> {{ $parcel->recipient_email }}
                            @endif
                        </address>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tracking Timeline -->
        @if($parcel->trackingEvents && $parcel->trackingEvents->count() > 0)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-route me-2"></i>
                    Tracking Timeline
                </h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    @foreach($parcel->trackingEvents as $event)
                    <div class="timeline-item {{ $loop->first ? 'active' : '' }}">
                        <div class="timeline-marker">
                            <i class="fas fa-{{ $event->status === 'delivered' ? 'check-circle' : ($event->status === 'in_transit' ? 'truck' : ($event->status === 'picked_up' ? 'box' : 'clock')) }}"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">{{ $event->description }}</h6>
                            <p class="text-muted mb-1">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ $event->location ?: 'Location not specified' }}
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ $event->event_date->format('M d, Y \a\t g:i A') }}
                            </small>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Status Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Status Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="status-icon mb-2">
                        @if($parcel->status === 'delivered')
                            <i class="fas fa-check-circle text-success fa-3x"></i>
                        @elseif($parcel->status === 'in_transit')
                            <i class="fas fa-truck text-primary fa-3x"></i>
                        @elseif($parcel->status === 'out_for_delivery')
                            <i class="fas fa-shipping-fast text-warning fa-3x"></i>
                        @else
                            <i class="fas fa-clock text-secondary fa-3x"></i>
                        @endif
                    </div>
                    <h5 class="mb-1">{{ ucfirst(str_replace('_', ' ', $parcel->status)) }}</h5>
                    @if($parcel->status === 'delivered' && $parcel->delivered_at)
                        <p class="text-muted mb-0">Delivered on {{ $parcel->delivered_at->format('M d, Y') }}</p>
                    @elseif($parcel->estimated_delivery_date)
                        <p class="text-muted mb-0">
                            Expected: {{ $parcel->estimated_delivery_date->format('M d, Y') }}
                        </p>
                    @endif
                </div>

                <div class="row text-center">
                    @if($parcel->shipped_at)
                    <div class="col-6">
                        <small class="text-muted d-block">Shipped</small>
                        <strong>{{ $parcel->shipped_at->format('M d') }}</strong>
                    </div>
                    @endif
                    @if($parcel->estimated_delivery_date)
                    <div class="col-6">
                        <small class="text-muted d-block">{{ $parcel->status === 'delivered' ? 'Delivered' : 'Expected' }}</small>
                        <strong>{{ $parcel->estimated_delivery_date->format('M d') }}</strong>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Cost Breakdown -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    Cost Breakdown
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>Shipping Cost:</span>
                    <strong>@currency($parcel->shipping_cost)</strong>
                </div>
                @if($parcel->insurance_cost)
                <div class="d-flex justify-content-between mb-2">
                    <span>Insurance:</span>
                    <strong>@currency($parcel->insurance_cost)</strong>
                </div>
                @endif
                <hr>
                <div class="d-flex justify-content-between">
                    <strong>Total Cost:</strong>
                    <strong class="text-primary">@currency($parcel->total_cost)</strong>
                </div>
                <div class="mt-2">
                    <span class="badge bg-{{ $parcel->is_paid ? 'success' : 'warning' }}">
                        {{ $parcel->is_paid ? 'Paid' : 'Unpaid' }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Carrier Information -->
        @if($parcel->carrier)
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-truck me-2"></i>
                    Carrier Information
                </h5>
            </div>
            <div class="card-body">
                <h6 class="mb-1">{{ $parcel->carrier->name }}</h6>
                @if($parcel->carrier->website)
                    <p class="mb-2">
                        <a href="{{ $parcel->carrier->website }}" target="_blank" class="text-decoration-none">
                            <i class="fas fa-external-link-alt me-1"></i>
                            Visit Carrier Website
                        </a>
                    </p>
                @endif
                @if($parcel->carrier->phone)
                    <p class="mb-0">
                        <i class="fas fa-phone me-1"></i>
                        {{ $parcel->carrier->phone }}
                    </p>
                @endif
            </div>
        </div>
        @endif

        <!-- Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('customer.track', ['tracking_number' => $parcel->tracking_number]) }}" class="btn btn-primary">
                        <i class="fas fa-search-location me-2"></i>
                        Track Package
                    </a>
                    @if(!$parcel->is_paid)
                    <a href="{{ route('customer.payments.show', $parcel) }}" class="btn btn-success">
                        <i class="fas fa-credit-card me-2"></i>
                        Pay Now
                    </a>
                    @endif
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        Print Details
                    </button>
                    <a href="{{ route('customer.support.create') }}" class="btn btn-outline-warning">
                        <i class="fas fa-headset me-2"></i>
                        Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #6c757d;
}

.timeline-item.active .timeline-marker {
    background: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #dee2e6;
}

.timeline-item.active .timeline-content {
    border-left-color: #0d6efd;
}

@media print {
    .btn, .card-header, .timeline-marker {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    Tracking number copied to clipboard!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Tracking number: ' + text);
    });
}
</script>
@endpush
