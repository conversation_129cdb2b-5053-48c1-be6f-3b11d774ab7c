<?php

namespace App\Services;

use App\Models\Parcel;
use App\Models\Order;
use App\Models\SiteSetting;
use PaypalServerSdkLib\PaypalServerSdkClient;
use PaypalServerSdkLib\PaypalServerSdkClientBuilder;
use PaypalServerSdkLib\Environment;
use PaypalServerSdkLib\Authentication\ClientCredentialsAuthCredentialsBuilder;
use PaypalServerSdkLib\Exceptions\ErrorException;
use Illuminate\Support\Facades\Log;

class PayPalPaymentService
{
    private PaypalServerSdkClient $client;

    public function __construct()
    {
        $clientId = config('services.paypal.client_id');
        $clientSecret = config('services.paypal.client_secret');
        $mode = config('services.paypal.mode', 'sandbox');

        // Create environment
        $environment = $mode === 'live' ? Environment::PRODUCTION : Environment::SANDBOX;

        // Create PayPal Server SDK client
        $this->client = PaypalServerSdkClientBuilder::init()
            ->clientCredentialsAuthCredentials(
                ClientCredentialsAuthCredentialsBuilder::init(
                    $clientId,
                    $clientSecret
                )
            )
            ->environment($environment)
            ->build();
    }

    /**
     * Create PayPal payment
     */
    public function createPayment(Parcel $parcel, string $returnUrl, string $cancelUrl): array
    {
        try {
            // Get currency settings
            $currency = SiteSetting::getValue('base_currency', 'USD');

            // Create order request body
            $body = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $parcel->tracking_number,
                        'description' => "Shipping for parcel {$parcel->tracking_number}",
                        'amount' => [
                            'currency_code' => $currency,
                            'value' => number_format($parcel->total_cost, 2, '.', ''),
                            'breakdown' => [
                                'item_total' => [
                                    'currency_code' => $currency,
                                    'value' => number_format($parcel->total_cost, 2, '.', '')
                                ]
                            ]
                        ],
                        'items' => [
                            [
                                'name' => "Shipping for parcel {$parcel->tracking_number}",
                                'description' => $parcel->description ?? 'Parcel shipping',
                                'quantity' => '1',
                                'unit_amount' => [
                                    'currency_code' => $currency,
                                    'value' => number_format($parcel->total_cost, 2, '.', '')
                                ]
                            ]
                        ]
                    ]
                ],
                'application_context' => [
                    'return_url' => $returnUrl,
                    'cancel_url' => $cancelUrl,
                    'brand_name' => config('app.name', 'Atrix Logistics'),
                    'landing_page' => 'BILLING',
                    'user_action' => 'PAY_NOW'
                ]
            ];

            // Execute request using new SDK
            $response = $this->client->getOrdersController()->ordersCreate([
                'body' => $body,
                'prefer' => 'return=representation'
            ]);

            // Get approval URL
            $approvalUrl = null;
            $result = $response->getResult();
            $links = $result->getLinks();

            if ($links) {
                foreach ($links as $link) {
                    if ($link->getRel() === 'approve') {
                        $approvalUrl = $link->getHref();
                        break;
                    }
                }
            }

            return [
                'success' => true,
                'payment_id' => $result->getId(),
                'approval_url' => $approvalUrl,
                'state' => $result->getStatus(),
            ];

        } catch (ErrorException $e) {
            Log::error('PayPal payment creation failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage(),
                'response' => $e->getCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        } catch (\Exception $e) {
            Log::error('PayPal payment creation failed', [
                'parcel_id' => $parcel->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Execute PayPal payment (Capture Order)
     */
    public function executePayment(string $paymentId, string $payerId): array
    {
        try {
            // Execute capture using new SDK
            $response = $this->client->getOrdersController()->ordersCapture([
                'id' => $paymentId,
                'prefer' => 'return=representation'
            ]);

            $result = $response->getResult();

            if ($result->getStatus() === 'COMPLETED') {
                // Get capture details
                $purchaseUnits = $result->getPurchaseUnits();
                $capture = $purchaseUnits[0]->getPayments()->getCaptures()[0];

                return [
                    'success' => true,
                    'transaction_id' => $capture->getId(),
                    'status' => 'completed',
                    'message' => 'Payment processed successfully via PayPal.',
                    'gateway_response' => [
                        'payment_id' => $result->getId(),
                        'state' => $result->getStatus(),
                        'capture_id' => $capture->getId(),
                        'capture_status' => $capture->getStatus(),
                        'amount' => $capture->getAmount()->getValue(),
                        'currency' => $capture->getAmount()->getCurrencyCode(),
                        'create_time' => $result->getCreateTime(),
                        'update_time' => $result->getUpdateTime(),
                        'payer_info' => $result->getPayer() ?? null,
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Payment not completed',
                    'state' => $result->getStatus(),
                ];
            }

        } catch (ErrorException $e) {
            Log::error('PayPal payment execution failed', [
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
                'error' => $e->getMessage(),
                'response' => $e->getCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        } catch (\Exception $e) {
            Log::error('PayPal payment execution failed', [
                'payment_id' => $paymentId,
                'payer_id' => $payerId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        }
    }

    /**
     * Get payment details
     */
    public function getPaymentDetails(string $paymentId): array
    {
        try {
            $response = $this->client->getOrdersController()->ordersGet([
                'id' => $paymentId
            ]);

            $result = $response->getResult();

            return [
                'success' => true,
                'payment' => [
                    'id' => $result->getId(),
                    'state' => $result->getStatus(),
                    'intent' => $result->getIntent(),
                    'create_time' => $result->getCreateTime(),
                    'update_time' => $result->getUpdateTime(),
                    'purchase_units' => $result->getPurchaseUnits(),
                    'payer' => $result->getPayer(),
                ]
            ];

        } catch (ErrorException $e) {
            Log::error('PayPal payment details retrieval failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::error('PayPal payment details retrieval failed', [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create refund
     */
    public function createRefund(string $captureId, float $amount = null): array
    {
        try {
            $body = [];
            if ($amount !== null) {
                $body['amount'] = [
                    'currency_code' => SiteSetting::getValue('base_currency', 'USD'),
                    'value' => number_format($amount, 2, '.', '')
                ];
            }

            $response = $this->client->getPaymentsController()->capturesRefund([
                'captureId' => $captureId,
                'body' => $body,
                'prefer' => 'return=representation'
            ]);

            $result = $response->getResult();

            return [
                'success' => true,
                'refund_id' => $result->getId(),
                'state' => $result->getStatus(),
                'amount' => $result->getAmount()->getValue(),
                'currency' => $result->getAmount()->getCurrencyCode(),
            ];

        } catch (ErrorException $e) {
            Log::error('PayPal refund failed', [
                'capture_id' => $captureId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        } catch (\Exception $e) {
            Log::error('PayPal refund failed', [
                'capture_id' => $captureId,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature (Note: Webhook verification requires additional setup with the new SDK)
     */
    public function verifyWebhookSignature(array $headers, string $body, string $webhookId): bool
    {
        // Note: The new PayPal SDK handles webhook verification differently
        // This would require implementing webhook verification using the PayPal REST API directly
        // For now, we'll log this and return false to maintain backward compatibility

        Log::warning('PayPal webhook verification not implemented with new SDK', [
            'webhook_id' => $webhookId,
            'headers' => array_keys($headers),
        ]);

        return false;
    }

    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies(): array
    {
        return [
            'AUD', 'BRL', 'CAD', 'CNY', 'CZK', 'DKK', 'EUR', 'HKD', 'HUF', 'ILS',
            'JPY', 'MYR', 'MXN', 'TWD', 'NZD', 'NOK', 'PHP', 'PLN', 'GBP', 'RUB',
            'SGD', 'SEK', 'CHF', 'THB', 'USD'
        ];
    }

    /**
     * Check if currency is supported
     */
    public function isCurrencySupported(string $currency): bool
    {
        return in_array(strtoupper($currency), $this->getSupportedCurrencies());
    }

    /**
     * Create PayPal payment for an order
     */
    public function createOrderPayment(Order $order, string $returnUrl, string $cancelUrl): array
    {
        try {
            // Get currency settings
            $currency = SiteSetting::getValue('base_currency', 'USD');

            // Build items array
            $items = [];
            $itemTotal = 0;

            foreach ($order->items as $orderItem) {
                $itemPrice = number_format($orderItem->unit_price, 2, '.', '');
                $items[] = [
                    'name' => $orderItem->product_name,
                    'description' => $orderItem->product_description ?? 'Product',
                    'quantity' => (string)$orderItem->quantity,
                    'unit_amount' => [
                        'currency_code' => $currency,
                        'value' => $itemPrice
                    ]
                ];
                $itemTotal += $orderItem->unit_price * $orderItem->quantity;
            }

            // Add shipping as an item if applicable
            if ($order->shipping_amount > 0) {
                $shippingPrice = number_format($order->shipping_amount, 2, '.', '');
                $items[] = [
                    'name' => 'Shipping',
                    'description' => 'Shipping charges',
                    'quantity' => '1',
                    'unit_amount' => [
                        'currency_code' => $currency,
                        'value' => $shippingPrice
                    ]
                ];
                $itemTotal += $order->shipping_amount;
            }

            // Add tax as an item if applicable
            if ($order->tax_amount > 0) {
                $taxPrice = number_format($order->tax_amount, 2, '.', '');
                $items[] = [
                    'name' => 'Tax',
                    'description' => 'Tax charges',
                    'quantity' => '1',
                    'unit_amount' => [
                        'currency_code' => $currency,
                        'value' => $taxPrice
                    ]
                ];
                $itemTotal += $order->tax_amount;
            }

            // Create order request body
            $body = [
                'intent' => 'CAPTURE',
                'purchase_units' => [
                    [
                        'reference_id' => $order->order_number,
                        'description' => "Payment for order {$order->order_number}",
                        'amount' => [
                            'currency_code' => $currency,
                            'value' => number_format($order->total_amount, 2, '.', ''),
                            'breakdown' => [
                                'item_total' => [
                                    'currency_code' => $currency,
                                    'value' => number_format($itemTotal, 2, '.', '')
                                ]
                            ]
                        ],
                        'items' => $items
                    ]
                ],
                'application_context' => [
                    'return_url' => $returnUrl,
                    'cancel_url' => $cancelUrl,
                    'brand_name' => config('app.name', 'Atrix Logistics'),
                    'landing_page' => 'BILLING',
                    'user_action' => 'PAY_NOW'
                ]
            ];

            // Execute request using new SDK
            $response = $this->client->getOrdersController()->ordersCreate([
                'body' => $body,
                'prefer' => 'return=representation'
            ]);

            // Get approval URL
            $approvalUrl = null;
            $result = $response->getResult();
            $links = $result->getLinks();

            if ($links) {
                foreach ($links as $link) {
                    if ($link->getRel() === 'approve') {
                        $approvalUrl = $link->getHref();
                        break;
                    }
                }
            }

            if (!$approvalUrl) {
                throw new \Exception('Could not retrieve PayPal approval URL');
            }

            return [
                'success' => true,
                'payment_id' => $result->getId(),
                'approval_url' => $approvalUrl,
                'amount' => $order->total_amount,
                'currency' => $currency,
            ];

        } catch (ErrorException $e) {
            Log::error('PayPal order payment creation failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'response' => $e->getCode(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => null,
            ];
        } catch (\Exception $e) {
            Log::error('PayPal order payment creation failed', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }
}
