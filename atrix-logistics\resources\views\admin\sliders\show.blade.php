@extends('layouts.admin')

@section('title', 'Slider Details')
@section('page-title', 'Slider: ' . $slider->title)

@section('page-actions')
    <div class="btn-group" role="group">
        <a href="{{ route('admin.cms.sliders.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Sliders
        </a>
        <a href="{{ route('admin.cms.sliders.edit', $slider) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i> Edit Slider
        </a>
        <button type="button" class="btn btn-outline-info" onclick="toggleStatus()">
            <i class="fas fa-{{ $slider->is_active ? 'eye-slash' : 'eye' }} me-1"></i> 
            {{ $slider->is_active ? 'Deactivate' : 'Activate' }}
        </button>
        <button type="button" class="btn btn-outline-success" onclick="duplicateSlider()">
            <i class="fas fa-copy me-1"></i> Duplicate
        </button>
        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
            <i class="fas fa-trash me-1"></i> Delete
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <!-- Slider Preview -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Slider Preview</h6>
                </div>
                <div class="card-body p-0">
                    <div class="position-relative">
                        @if($slider->image)
                            <img src="{{ Storage::url($slider->image) }}" 
                                 alt="{{ $slider->title }}" 
                                 class="img-fluid w-100" 
                                 style="max-height: 400px; object-fit: cover;">
                        @else
                            <div class="bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 400px;">
                                <i class="fas fa-image fa-4x text-muted"></i>
                            </div>
                        @endif
                        
                        <!-- Overlay Content -->
                        <div class="position-absolute top-50 start-50 translate-middle text-center text-white">
                            <h2 class="display-4 fw-bold mb-3" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.7);">
                                {{ $slider->title }}
                            </h2>
                            @if($slider->subtitle)
                                <h4 class="mb-3" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">
                                    {{ $slider->subtitle }}
                                </h4>
                            @endif
                            @if($slider->description)
                                <p class="lead mb-4" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.7);">
                                    {{ $slider->description }}
                                </p>
                            @endif
                            @if($slider->hasButton())
                                <a href="{{ $slider->button_url }}" class="btn btn-primary btn-lg">
                                    {{ $slider->button_text }}
                                </a>
                            @endif
                        </div>
                        
                        <!-- Status Badge -->
                        <span class="position-absolute top-0 start-0 m-3 badge bg-{{ $slider->is_active ? 'success' : 'secondary' }} fs-6">
                            {{ $slider->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        
                        <!-- Order Badge -->
                        <span class="position-absolute top-0 end-0 m-3 badge bg-primary fs-6">
                            Order: {{ $slider->display_order }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Slider Details -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Slider Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Title:</strong>
                            <p class="mb-0">{{ $slider->title }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>Subtitle:</strong>
                            <p class="mb-0">{{ $slider->subtitle ?: 'Not set' }}</p>
                        </div>
                    </div>
                    
                    @if($slider->description)
                        <div class="mb-3">
                            <strong>Description:</strong>
                            <p class="mb-0">{{ $slider->description }}</p>
                        </div>
                    @endif
                    
                    @if($slider->hasButton())
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <strong>Button Text:</strong>
                                <p class="mb-0">{{ $slider->button_text }}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <strong>Button URL:</strong>
                                <p class="mb-0">
                                    <a href="{{ $slider->button_url }}" target="_blank" class="text-decoration-none">
                                        {{ $slider->button_url }} <i class="fas fa-external-link-alt ms-1"></i>
                                    </a>
                                </p>
                            </div>
                        </div>
                    @endif
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Display Order:</strong>
                            <p class="mb-0">{{ $slider->display_order }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>Status:</strong>
                            <p class="mb-0">
                                <span class="badge bg-{{ $slider->is_active ? 'success' : 'secondary' }}">
                                    {{ $slider->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <strong>Created:</strong>
                            <p class="mb-0">{{ $slider->created_at->format('M d, Y h:i A') }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <strong>Last Updated:</strong>
                            <p class="mb-0">{{ $slider->updated_at->format('M d, Y h:i A') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Images -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Desktop Image</h6>
                </div>
                <div class="card-body text-center">
                    @if($slider->image)
                        <img src="{{ Storage::url($slider->image) }}" 
                             alt="{{ $slider->title }}" 
                             class="img-fluid rounded mb-2" 
                             style="max-height: 200px;">
                        <div class="text-muted small">{{ basename($slider->image) }}</div>
                    @else
                        <div class="text-muted">No image uploaded</div>
                    @endif
                </div>
            </div>

            @if($slider->mobile_image)
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="m-0 font-weight-bold text-primary">Mobile Image</h6>
                    </div>
                    <div class="card-body text-center">
                        <img src="{{ Storage::url($slider->mobile_image) }}" 
                             alt="{{ $slider->title }} (Mobile)" 
                             class="img-fluid rounded mb-2" 
                             style="max-height: 200px;">
                        <div class="text-muted small">{{ basename($slider->mobile_image) }}</div>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.cms.sliders.edit', $slider) }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i> Edit Slider
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="toggleStatus()">
                            <i class="fas fa-{{ $slider->is_active ? 'eye-slash' : 'eye' }} me-2"></i> 
                            {{ $slider->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="duplicateSlider()">
                            <i class="fas fa-copy me-2"></i> Duplicate Slider
                        </button>
                        <hr>
                        <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash me-2"></i> Delete Slider
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the slider <strong>"{{ $slider->title }}"</strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle"></i> This action cannot be undone and will delete all associated images.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" action="{{ route('admin.cms.sliders.destroy', $slider) }}" style="display: inline;">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">Delete Slider</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function confirmDelete() {
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    function toggleStatus() {
        if (confirm('Are you sure you want to {{ $slider->is_active ? 'deactivate' : 'activate' }} this slider?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/cms/sliders/{{ $slider->id }}/toggle-status';
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);
            
            document.body.appendChild(form);
            form.submit();
        }
    }

    function duplicateSlider() {
        if (confirm('Are you sure you want to duplicate this slider?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/cms/sliders/{{ $slider->id }}/duplicate';
            
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);
            
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
@endpush
