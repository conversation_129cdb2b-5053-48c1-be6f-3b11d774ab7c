<!-- Currency Dropdown (Mockup) -->
<div class="relative group">
    <button class="flex items-center space-x-2 text-gray-700 hover:text-green-600 transition-colors" 
            onclick="toggleCurrencyDropdown()" 
            id="currency-dropdown-btn">
        <i class="fas fa-dollar-sign text-sm"></i>
        <span class="text-sm font-medium">USD</span>
        <i class="fas fa-chevron-down text-xs transition-transform" id="currency-chevron"></i>
    </button>
    
    <div id="currency-dropdown" class="absolute top-full right-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-200 opacity-0 invisible transition-all duration-300 z-50">
        <div class="py-2">
            <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100">
                Select Currency
            </div>
            
            <!-- USD -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option active" data-currency="USD" data-symbol="$">
                <span class="text-lg mr-3">$</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">US Dollar</div>
                    <div class="text-xs text-gray-500">USD</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">1.00</div>
                    <i class="fas fa-check text-green-600 opacity-100"></i>
                </div>
            </a>
            
            <!-- EUR -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option" data-currency="EUR" data-symbol="€">
                <span class="text-lg mr-3">€</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Euro</div>
                    <div class="text-xs text-gray-500">EUR</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">0.85</div>
                    <i class="fas fa-check text-green-600 opacity-0"></i>
                </div>
            </a>
            
            <!-- GBP -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option" data-currency="GBP" data-symbol="£">
                <span class="text-lg mr-3">£</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">British Pound</div>
                    <div class="text-xs text-gray-500">GBP</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">0.73</div>
                    <i class="fas fa-check text-green-600 opacity-0"></i>
                </div>
            </a>
            
            <!-- CAD -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option" data-currency="CAD" data-symbol="C$">
                <span class="text-lg mr-3">C$</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Canadian Dollar</div>
                    <div class="text-xs text-gray-500">CAD</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">1.25</div>
                    <i class="fas fa-check text-green-600 opacity-0"></i>
                </div>
            </a>
            
            <!-- AUD -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option" data-currency="AUD" data-symbol="A$">
                <span class="text-lg mr-3">A$</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Australian Dollar</div>
                    <div class="text-xs text-gray-500">AUD</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">1.35</div>
                    <i class="fas fa-check text-green-600 opacity-0"></i>
                </div>
            </a>
            
            <!-- JPY -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option" data-currency="JPY" data-symbol="¥">
                <span class="text-lg mr-3">¥</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Japanese Yen</div>
                    <div class="text-xs text-gray-500">JPY</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">110.0</div>
                    <i class="fas fa-check text-green-600 opacity-0"></i>
                </div>
            </a>
            
            <!-- CNY -->
            <a href="#" class="flex items-center px-4 py-3 hover:bg-green-50 transition-colors currency-option" data-currency="CNY" data-symbol="¥">
                <span class="text-lg mr-3">¥</span>
                <div>
                    <div class="text-sm font-medium text-gray-900">Chinese Yuan</div>
                    <div class="text-xs text-gray-500">CNY</div>
                </div>
                <div class="ml-auto text-right">
                    <div class="text-xs text-gray-500">6.45</div>
                    <i class="fas fa-check text-green-600 opacity-0"></i>
                </div>
            </a>
        </div>
        
        <div class="border-t border-gray-100 px-4 py-2">
            <div class="text-xs text-gray-500 text-center">
                <i class="fas fa-info-circle mr-1"></i>
                Exchange rates are indicative
            </div>
        </div>
    </div>
</div>

<script>
function toggleCurrencyDropdown() {
    const dropdown = document.getElementById('currency-dropdown');
    const chevron = document.getElementById('currency-chevron');
    
    if (dropdown.classList.contains('opacity-0')) {
        dropdown.classList.remove('opacity-0', 'invisible');
        dropdown.classList.add('opacity-100', 'visible');
        chevron.classList.add('rotate-180');
    } else {
        dropdown.classList.add('opacity-0', 'invisible');
        dropdown.classList.remove('opacity-100', 'visible');
        chevron.classList.remove('rotate-180');
    }
}

// Handle currency selection (mockup functionality)
document.addEventListener('DOMContentLoaded', function() {
    const currencyOptions = document.querySelectorAll('.currency-option');
    const currencyBtn = document.getElementById('currency-dropdown-btn');
    
    currencyOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active state from all options
            currencyOptions.forEach(opt => {
                opt.classList.remove('active');
                opt.querySelector('.fas.fa-check').classList.add('opacity-0');
            });
            
            // Add active state to selected option
            this.classList.add('active');
            this.querySelector('.fas.fa-check').classList.remove('opacity-0');
            
            // Update button text and icon
            const currencyCode = this.dataset.currency;
            const symbol = this.dataset.symbol;
            currencyBtn.querySelector('span').textContent = currencyCode;
            currencyBtn.querySelector('.fas.fa-dollar-sign').className = `fas fa-dollar-sign text-sm`;
            
            // Close dropdown
            toggleCurrencyDropdown();
            
            // Show notification (mockup)
            showNotification(`Currency changed to ${currencyCode}`, 'info');
            
            // Simulate price updates (mockup)
            updatePricesForCurrency(currencyCode, symbol);
        });
    });
});

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    const dropdown = document.getElementById('currency-dropdown');
    const button = document.getElementById('currency-dropdown-btn');
    
    if (!dropdown.contains(e.target) && !button.contains(e.target)) {
        dropdown.classList.add('opacity-0', 'invisible');
        dropdown.classList.remove('opacity-100', 'visible');
        document.getElementById('currency-chevron').classList.remove('rotate-180');
    }
});

// Mockup function to simulate price updates
function updatePricesForCurrency(currency, symbol) {
    // This is a mockup - in real implementation, this would make API calls
    const priceElements = document.querySelectorAll('[data-price]');
    priceElements.forEach(element => {
        const basePrice = parseFloat(element.dataset.price);
        let convertedPrice = basePrice;
        
        // Mockup conversion rates
        const rates = {
            'USD': 1.00,
            'EUR': 0.85,
            'GBP': 0.73,
            'CAD': 1.25,
            'AUD': 1.35,
            'JPY': 110.0,
            'CNY': 6.45
        };
        
        if (rates[currency]) {
            convertedPrice = basePrice * rates[currency];
        }
        
        // Format based on currency
        let formattedPrice;
        if (currency === 'JPY' || currency === 'CNY') {
            formattedPrice = symbol + Math.round(convertedPrice).toLocaleString();
        } else {
            formattedPrice = symbol + convertedPrice.toFixed(2);
        }
        
        element.textContent = formattedPrice;
    });
}
</script>

<style>
.currency-option.active {
    background-color: #f0fdf4;
}

.currency-option:hover .fas.fa-check {
    opacity: 0.3 !important;
}

.currency-option.active:hover .fas.fa-check {
    opacity: 1 !important;
}
</style>
