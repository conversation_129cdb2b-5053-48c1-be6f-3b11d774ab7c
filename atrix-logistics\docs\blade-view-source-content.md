
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40">

    <title>Products - Atrix Logistics</title>

    <!-- Favicon -->
            <link rel="icon" href="http://localhost:8000/assets/images/favicon.ico" type="image/x-icon">
        <link rel="shortcut icon" href="http://localhost:8000/assets/images/favicon.ico" type="image/x-icon">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <style>
        body {
            overflow-x: hidden;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar-content {
            height: calc(100vh - 160px);
            overflow-y: auto;
            padding-bottom: 20px;
        }

        .sidebar-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 280px;
            background: rgba(0, 0, 0, 0.2);
            padding: 20px 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1001;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .sidebar.show .sidebar-footer {
            transform: translateX(0);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            margin: 2px 0;
            transition: all 0.3s ease;
            padding: 12px 15px;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
            margin-left: 0;
            transition: all 0.3s ease;
            width: 100vw;
            max-width: 100%;
            overflow-x: hidden;
        }

        .main-content.sidebar-open {
            margin-left: 280px;
            width: calc(100vw - 280px);
        }

        .main-content .container-fluid {
            padding-left: 15px;
            padding-right: 15px;
            max-width: 100%;
            overflow-x: hidden;
        }

        .sidebar-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #667eea;
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .sidebar-toggle:hover {
            background: #5a67d8;
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .sidebar-toggle.sidebar-open {
            left: 300px;
            background: #5a67d8;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .sidebar-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive adjustments */
        @media (max-width: 991.98px) {
            .main-content.sidebar-open {
                margin-left: 0;
                width: 100vw;
            }

            .sidebar-toggle.sidebar-open {
                left: 20px;
            }
        }

        @media (max-width: 768px) {
            .sidebar-toggle {
                top: 15px;
                left: 15px;
                width: 45px;
                height: 45px;
                font-size: 16px;
            }

            .sidebar-toggle.sidebar-open {
                left: 15px;
            }

            .main-content .container-fluid {
                padding-left: 10px;
                padding-right: 10px;
            }
        }

        /* Prevent horizontal scroll on all elements */
        * {
            box-sizing: border-box;
        }

        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .stat-card-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        .stat-card-danger {
            background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
        }
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        .table th {
            border-top: none;
            font-weight: 600;
            color: #495057;
        }
        .badge {
            font-size: 0.75rem;
            padding: 0.5em 0.75em;
        }
        .btn {
            border-radius: 8px;
            font-weight: 500;
        }
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        /* Admin Pagination Fixes - Override any conflicting styles with high specificity */
        body .main-content .pagination,
        .main-content .pagination,
        .card .pagination,
        nav .pagination {
            margin-bottom: 0 !important;
        }

        /* High specificity selectors to override global.css */
        body .main-content .pagination .page-item,
        .main-content .pagination .page-item,
        .card .pagination .page-item,
        nav .pagination .page-item {
            margin: 0 2px !important;
            height: auto !important;
            width: auto !important;
            line-height: normal !important;
            font-size: 14px !important;
            position: relative !important;
            display: inline-block !important;
        }

        body .main-content .pagination .page-link,
        .main-content .pagination .page-link,
        .card .pagination .page-link,
        nav .pagination .page-link {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0.5rem 0.75rem !important;
            min-width: 40px !important;
            height: 40px !important;
            width: auto !important;
            font-size: 14px !important;
            line-height: 1.25 !important;
            color: #6c757d !important;
            text-decoration: none !important;
            background-color: #fff !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 0.375rem !important;
            transition: all 0.15s ease-in-out !important;
            position: relative !important;
            z-index: 1 !important;
        }

        body .main-content .pagination .page-link:hover,
        .main-content .pagination .page-link:hover,
        .card .pagination .page-link:hover,
        nav .pagination .page-link:hover {
            color: #0056b3 !important;
            background-color: #e9ecef !important;
            border-color: #dee2e6 !important;
        }

        body .main-content .pagination .page-item.active .page-link,
        .main-content .pagination .page-item.active .page-link,
        .card .pagination .page-item.active .page-link,
        nav .pagination .page-item.active .page-link {
            color: #fff !important;
            background-color: #667eea !important;
            border-color: #667eea !important;
        }

        body .main-content .pagination .page-item.disabled .page-link,
        .main-content .pagination .page-item.disabled .page-link,
        .card .pagination .page-item.disabled .page-link,
        nav .pagination .page-item.disabled .page-link {
            color: #6c757d !important;
            background-color: #fff !important;
            border-color: #dee2e6 !important;
            opacity: 0.5 !important;
        }

        /* Fix pagination icon sizes specifically with high specificity */
        body .main-content .pagination .page-link i.fas,
        .main-content .pagination .page-link i.fas,
        .card .pagination .page-link i.fas,
        nav .pagination .page-link i.fas,
        body .main-content .pagination .page-link i,
        .main-content .pagination .page-link i,
        .card .pagination .page-link i,
        nav .pagination .page-link i {
            font-size: 12px !important;
            line-height: 1 !important;
            width: auto !important;
            height: auto !important;
        }

        /* Override any global pagination li styles that might conflict */
        body .main-content .pagination li,
        .main-content .pagination li,
        .card .pagination li,
        nav .pagination li {
            height: auto !important;
            width: auto !important;
            line-height: normal !important;
            font-size: 14px !important;
            margin: 0 2px !important;
            position: relative !important;
            display: inline-block !important;
        }

        body .main-content .pagination li a,
        body .main-content .pagination li span,
        .main-content .pagination li a,
        .main-content .pagination li span,
        .card .pagination li a,
        .card .pagination li span,
        nav .pagination li a,
        nav .pagination li span {
            height: 40px !important;
            width: auto !important;
            min-width: 40px !important;
            line-height: normal !important;
            font-size: 14px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            position: relative !important;
            z-index: 1 !important;
            padding: 0.5rem 0.75rem !important;
            color: #6c757d !important;
            text-decoration: none !important;
            background-color: #fff !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 0.375rem !important;
            transition: all 0.15s ease-in-out !important;
        }

        /* Responsive pagination */
        @media (max-width: 576px) {
            body .main-content .pagination .page-link,
            .main-content .pagination .page-link,
            .card .pagination .page-link,
            nav .pagination .page-link {
                padding: 0.375rem 0.5rem !important;
                font-size: 12px !important;
                min-width: 35px !important;
                height: 35px !important;
            }

            body .main-content .pagination .page-link i.fas,
            .main-content .pagination .page-link i.fas,
            .card .pagination .page-link i.fas,
            nav .pagination .page-link i.fas,
            body .main-content .pagination .page-link i,
            .main-content .pagination .page-link i,
            .card .pagination .page-link i,
            nav .pagination .page-link i {
                font-size: 10px !important;
            }
        }

        /* Final override for any remaining global.css conflicts - highest specificity */
        body.admin-layout .main-content .pagination li,
        body .main-content .card .pagination li,
        .admin-layout .pagination li {
            height: auto !important;
            width: auto !important;
            line-height: normal !important;
            font-size: 14px !important;
            margin: 0 2px !important;
        }

        body.admin-layout .main-content .pagination li a,
        body.admin-layout .main-content .pagination li span,
        body .main-content .card .pagination li a,
        body .main-content .card .pagination li span,
        .admin-layout .pagination li a,
        .admin-layout .pagination li span {
            height: 40px !important;
            width: auto !important;
            min-width: 40px !important;
            line-height: normal !important;
            font-size: 14px !important;
            padding: 0.5rem 0.75rem !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        /* Ensure icons in pagination are properly sized */
        body.admin-layout .main-content .pagination i,
        body .main-content .card .pagination i,
        .admin-layout .pagination i,
        body.admin-layout .pagination .page-link i.fas,
        body.admin-layout .pagination .page-link i.fa,
        body.admin-layout .pagination .page-link i,
        .admin-layout .pagination .page-link i.fas,
        .admin-layout .pagination .page-link i.fa,
        .admin-layout .pagination .page-link i {
            font-size: 12px !important;
            line-height: 1 !important;
            width: auto !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Additional override for any remaining icon conflicts */
        body.admin-layout nav[aria-label="Page navigation"] .pagination .page-link i,
        body.admin-layout nav .pagination .page-link i.fas,
        body.admin-layout nav .pagination .page-link i.fa {
            font-size: 12px !important;
            line-height: 1 !important;
            width: auto !important;
            height: auto !important;
        }

        /* Nuclear option - highest specificity override for global.css conflicts */
        html body.admin-layout .main-content .card .pagination li,
        html body.admin-layout .main-content .pagination li {
            height: auto !important;
            width: auto !important;
            line-height: normal !important;
            font-size: 14px !important;
            margin: 0 2px !important;
        }

        html body.admin-layout .main-content .card .pagination li a,
        html body.admin-layout .main-content .card .pagination li span,
        html body.admin-layout .main-content .pagination li a,
        html body.admin-layout .main-content .pagination li span {
            height: 40px !important;
            width: auto !important;
            min-width: 40px !important;
            line-height: normal !important;
            font-size: 14px !important;
            padding: 0.5rem 0.75rem !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        html body.admin-layout .main-content .pagination i,
        html body.admin-layout .main-content .card .pagination i {
            font-size: 12px !important;
            line-height: 1 !important;
            width: auto !important;
            height: auto !important;
        }
    </style>

    </head>
<body class="admin-layout">
    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle" id="sidebarToggle">
        <i class="fas fa-chevron-right" id="toggleIcon"></i>
    </button>

    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="p-3">
            <div class="text-center mb-4">
                <h4 class="navbar-brand mb-0">
                    <i class="fas fa-shipping-fast me-2"></i>
                    Atrix Admin
                </h4>
                <small class="text-white-50">Welcome, Admin User</small>
            </div>

            <div class="sidebar-content">
                <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link " href="http://localhost:8000/admin">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle "
                               href="#" id="parcelsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-box me-2"></i>
                                Parcels
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="http://localhost:8000/admin/parcels">
                                    <i class="fas fa-list me-2"></i> Manage Parcels
                                </a></li>
                                <li><a class="dropdown-item" href="http://localhost:8000/admin/analytics/parcels">
                                    <i class="fas fa-chart-line me-2"></i> Parcel Analytics
                                </a></li>
                                <li><a class="dropdown-item" href="http://localhost:8000/admin/parcels/create">
                                    <i class="fas fa-plus me-2"></i> Create Parcel
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link " href="http://localhost:8000/admin/reports">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="http://localhost:8000/admin/quotes">
                                <i class="fas fa-quote-left me-2"></i>
                                Quotes
                            </a>
                        </li>

                        <!-- CMS Management -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle "
                               href="#" id="cmsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-edit me-2"></i>
                                CMS Management
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/cms/settings">
                                    <i class="fas fa-cog me-2"></i> Site Settings
                                </a></li>
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/cms/team">
                                    <i class="fas fa-users me-2"></i> Team Members
                                </a></li>
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/cms/sliders">
                                    <i class="fas fa-images me-2"></i> Sliders & Banners
                                </a></li>
                            </ul>
                        </li>

                        <!-- Communications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle "
                               href="#" id="communicationsDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-envelope me-2"></i>
                                Communications
                                                                                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/communications/live-chat">
                                    <i class="fas fa-comments me-2"></i> Live Chat
                                                                                                        </a></li>
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/communications/live-chat/history">
                                    <i class="fas fa-history me-2"></i> Chat History
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/communications/contacts">
                                    <i class="fas fa-inbox me-2"></i> Contact Messages
                                                                    </a></li>
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/communications/newsletter">
                                    <i class="fas fa-newspaper me-2"></i> Newsletter Subscribers
                                </a></li>
                            </ul>
                        </li>

                        <!-- E-commerce Management -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle active"
                               href="#" id="ecommerceDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-shopping-cart me-2"></i>
                                E-commerce
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/ecommerce/categories">
                                    <i class="fas fa-folder me-2"></i> Categories
                                </a></li>
                                <li><a class="dropdown-item active"
                                       href="http://localhost:8000/admin/ecommerce/products">
                                    <i class="fas fa-box me-2"></i> Products
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item "
                                       href="http://localhost:8000/admin/orders">
                                    <i class="fas fa-receipt me-2"></i> Orders
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="alert('Coming soon!')">
                                    <i class="fas fa-users me-2"></i> Customers
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="alert('Coming soon!')">
                                    <i class="fas fa-chart-line me-2"></i> Reports
                                </a></li>
                            </ul>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle "
                               href="#" id="customersDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-users me-2"></i>
                                Customers
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="http://localhost:8000/admin/customers">
                                    <i class="fas fa-list me-2"></i> Manage Customers
                                </a></li>
                                <li><a class="dropdown-item" href="http://localhost:8000/admin/analytics/customers">
                                    <i class="fas fa-chart-line me-2"></i> Customer Analytics
                                </a></li>
                                <li><a class="dropdown-item" href="http://localhost:8000/admin/customers/create">
                                    <i class="fas fa-plus me-2"></i> Add Customer
                                </a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="alert('Coming soon!')">
                                <i class="fas fa-truck me-2"></i>
                                Carriers
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link " href="http://localhost:8000/admin/cms/settings">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <hr class="my-3">
                        <li class="nav-item">
                            <a class="nav-link" href="http://localhost:8000/track" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>
                                View Website
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Sidebar Footer -->
                <div class="sidebar-footer">
                    <form method="POST" action="http://localhost:8000/logout">
                        <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                        <button type="submit" class="nav-link btn btn-link text-start w-100 border-0 p-0" style="color: rgba(255, 255, 255, 0.8);">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>
        </nav>

    <!-- Main content -->
    <main class="main-content" id="mainContent">
        <div class="container-fluid">
            <!-- Top Navigation -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="margin-top: 80px;">
                <h1 class="h2">Product Management</h1>
                <div class="d-flex align-items-center">
                    <!-- Live Chat Notification Bell -->
                    <div class="me-3">
                        <div class="dropdown">
                            <button class="btn btn-outline-primary position-relative" type="button" id="liveChatDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="liveChatBadge" style="display: none;">
                                    0
                                </span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" style="min-width: 350px; max-height: 400px; overflow-y: auto;">
                                <li><h6 class="dropdown-header">Live Chat Notifications</h6></li>
                                <li><hr class="dropdown-divider"></li>
                                <div id="liveChatNotifications">
                                    <li class="dropdown-item text-center text-muted py-3">
                                        <i class="fas fa-comments fa-2x mb-2"></i><br>
                                        No new chat messages
                                    </li>
                                </div>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-center" href="http://localhost:8000/admin/communications/live-chat">
                                        <i class="fas fa-eye me-1"></i> View All Chats
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group" role="group">
        <a href="http://localhost:8000/admin/ecommerce/products/create" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> Add Product
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
            <i class="fas fa-cog me-1"></i> Actions
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="toggleBulkMode()">
                <i class="fas fa-check-square me-2"></i> Bulk Actions
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="alert('Coming soon!')">
                <i class="fas fa-download me-2"></i> Export Products
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="alert('Coming soon!')">
                <i class="fas fa-upload me-2"></i> Import Products
            </a></li>
        </ul>
    </div>
                    </div>
                </div>
            </div>

                <!-- Alerts -->
                
                
                
            <!-- Page Content -->
                <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">30</h4>
                            <p class="mb-0">Total Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">20</h4>
                            <p class="mb-0">Active Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">11</h4>
                            <p class="mb-0">Featured Products</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-star fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">19</h4>
                            <p class="mb-0">Categories</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-folder fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="http://localhost:8000/admin/ecommerce/products" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="" placeholder="Product name, SKU...">
                </div>
                <div class="col-md-2">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                                                    <option value="10" >
                                Boxes &amp; Packaging
                            </option>
                                                            <option value="11" >
                                    └─ Cardboard Boxes
                                </option>
                                                            <option value="12" >
                                    └─ Wooden Crates &amp; Pallets
                                </option>
                                                            <option value="13" >
                                    └─ Plastic Containers &amp; Tubs
                                </option>
                                                            <option value="14" >
                                    └─ Packaging Materials &amp; Supplies
                                </option>
                                                                                <option value="11" >
                                Cardboard Boxes
                            </option>
                                                                                <option value="7" >
                                Container Parts &amp; Hardware
                            </option>
                                                                                <option value="9" >
                                Logistics Equipment Parts
                            </option>
                                                                                <option value="14" >
                                Packaging Materials &amp; Supplies
                            </option>
                                                                                <option value="13" >
                                Plastic Containers &amp; Tubs
                            </option>
                                                                                <option value="3" >
                                Refrigerated Containers (Reefers)
                            </option>
                                                                                <option value="1" >
                                Shipping Containers
                            </option>
                                                            <option value="2" >
                                    └─ Standard Dry Containers
                                </option>
                                                            <option value="3" >
                                    └─ Refrigerated Containers (Reefers)
                                </option>
                                                            <option value="4" >
                                    └─ Specialized Containers
                                </option>
                                                            <option value="5" >
                                    └─ Used Containers
                                </option>
                                                                                <option value="6" >
                                Spare Parts &amp; Components
                            </option>
                                                            <option value="7" >
                                    └─ Container Parts &amp; Hardware
                                </option>
                                                            <option value="8" >
                                    └─ Truck &amp; Trailer Parts
                                </option>
                                                            <option value="9" >
                                    └─ Logistics Equipment Parts
                                </option>
                                                                                <option value="4" >
                                Specialized Containers
                            </option>
                                                                                <option value="2" >
                                Standard Dry Containers
                            </option>
                                                                                <option value="16" >
                                Steel Beams &amp; Sections
                            </option>
                                                                                <option value="18" >
                                Steel Pipes &amp; Tubes
                            </option>
                                                                                <option value="17" >
                                Steel Plates &amp; Sheets
                            </option>
                                                                                <option value="15" >
                                Steel Products &amp; Materials
                            </option>
                                                            <option value="16" >
                                    └─ Steel Beams &amp; Sections
                                </option>
                                                            <option value="17" >
                                    └─ Steel Plates &amp; Sheets
                                </option>
                                                            <option value="18" >
                                    └─ Steel Pipes &amp; Tubes
                                </option>
                                                            <option value="19" >
                                    └─ Steel Reinforcement (Rebar)
                                </option>
                                                                                <option value="19" >
                                Steel Reinforcement (Rebar)
                            </option>
                                                                                <option value="8" >
                                Truck &amp; Trailer Parts
                            </option>
                                                                                <option value="5" >
                                Used Containers
                            </option>
                                                                                <option value="12" >
                                Wooden Crates &amp; Pallets
                            </option>
                                                                        </select>
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Status</option>
                        <option value="active" >Active</option>
                        <option value="inactive" >Inactive</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="stock_status" class="form-label">Stock Status</label>
                    <select class="form-select" id="stock_status" name="stock_status">
                        <option value="">All Stock</option>
                                                    <option value="in_stock" >
                                In Stock
                            </option>
                                                    <option value="out_of_stock" >
                                Out of Stock
                            </option>
                                                    <option value="on_backorder" >
                                On Backorder
                            </option>
                                            </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i> Filter
                        </button>
                        <a href="http://localhost:8000/admin/ecommerce/products" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i> Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-box me-2"></i>
                    Products
                </h5>
                <div id="bulk-actions" class="d-none">
                    <form method="POST" action="http://localhost:8000/admin/ecommerce/products/bulk-action" class="d-inline">
                        <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                        <div class="input-group">
                            <select name="action" class="form-select" required>
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate</option>
                                <option value="deactivate">Deactivate</option>
                                <option value="delete">Delete</option>
                            </select>
                            <button type="submit" class="btn btn-primary">Apply</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="toggleBulkMode()">Cancel</button>
                        </div>
                        <input type="hidden" name="products" id="bulk-products">
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
                            <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="5%" class="bulk-checkbox d-none">
                                    <input type="checkbox" id="select-all" class="form-check-input">
                                </th>
                                <th width="40%">Product</th>
                                <th width="10%">Category</th>
                                <th width="10%">Price</th>
                                <th width="10%">Stock</th>
                                <th width="10%">Status</th>
                                <th width="15%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="24">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel I-Beam 203x133x25kg/m (6m length) - Grade 300W</strong>
                                                <br><small class="text-muted">SKU: STEEL-IBEAM-203-133-25-ZA</small>
                                                                                                    <br><small class="text-muted">Structural steel I-beam 203x133x25kg/m, Grade 300W. SANS 143...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/16" 
                                               class="text-decoration-none">
                                                Steel Beams &amp; Sections
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R6,850.00</span>
                                            <br><strong class="text-success">R6,350.00</strong>
                                            <br><small class="badge bg-danger">7% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">200</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/24/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/24" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/24/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(24, 'Steel I-Beam 203x133x25kg/m (6m length) - Grade 300W')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="25">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel Channel 150x75x14kg/m (6m length) - Grade 300W</strong>
                                                <br><small class="text-muted">SKU: STEEL-CHANNEL-150-75-14-ZA</small>
                                                                                                    <br><small class="text-muted">Steel channel 150x75x14kg/m, Grade 300W. Perfect for framewo...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/16" 
                                               class="text-decoration-none">
                                                Steel Beams &amp; Sections
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R3,950.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">150</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/25/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/25" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/25/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(25, 'Steel Channel 150x75x14kg/m (6m length) - Grade 300W')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="26">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel Plate 12mm x 1500mm x 3000mm - Grade 250 Mild Steel</strong>
                                                <br><small class="text-muted">SKU: STEEL-PLATE-12-1500-3000-ZA</small>
                                                                                                    <br><small class="text-muted">Mild steel plate 12mm thick, Grade 250. Perfect for fabricat...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/17" 
                                               class="text-decoration-none">
                                                Steel Plates &amp; Sheets
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R8,950.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">80</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/26/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/26" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/26/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(26, 'Steel Plate 12mm x 1500mm x 3000mm - Grade 250 Mild Steel')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="27">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel Round Pipe 48.3mm OD x 3.2mm Wall (6m length)</strong>
                                                <br><small class="text-muted">SKU: STEEL-PIPE-48-3.2-6M-ZA</small>
                                                                                                    <br><small class="text-muted">Seamless steel round pipe 48.3mm OD x 3.2mm wall. SANS 719 c...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/18" 
                                               class="text-decoration-none">
                                                Steel Pipes &amp; Tubes
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R1,150.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">300</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/27/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/27" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/27/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(27, 'Steel Round Pipe 48.3mm OD x 3.2mm Wall (6m length)')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="28">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel Square Tube 50x50x3mm Wall (6m length)</strong>
                                                <br><small class="text-muted">SKU: STEEL-SQUARE-50-3-6M-ZA</small>
                                                                                                    <br><small class="text-muted">Steel square tube 50x50x3mm wall. Perfect for framework and...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/18" 
                                               class="text-decoration-none">
                                                Steel Pipes &amp; Tubes
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R950.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">250</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/28/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/28" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/28/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(28, 'Steel Square Tube 50x50x3mm Wall (6m length)')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="29">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel Rebar 12mm Diameter (12m length) - High Tensile Y12</strong>
                                                <br><small class="text-muted">SKU: STEEL-REBAR-12-12M-Y12-ZA</small>
                                                                                                    <br><small class="text-muted">High-tensile steel rebar 12mm diameter, Grade Y12. SANS 920...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/19" 
                                               class="text-decoration-none">
                                                Steel Reinforcement (Rebar)
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R485.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">1000</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/29/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/29" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/29/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(29, 'Steel Rebar 12mm Diameter (12m length) - High Tensile Y12')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="30">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Steel Rebar 16mm Diameter (12m length) - High Tensile Y16</strong>
                                                <br><small class="text-muted">SKU: STEEL-REBAR-16-12M-Y16-ZA</small>
                                                                                                    <br><small class="text-muted">Heavy-duty steel rebar 16mm diameter, Grade Y16. For structu...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/19" 
                                               class="text-decoration-none">
                                                Steel Reinforcement (Rebar)
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R850.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">800</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/30/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/30" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/30/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(30, 'Steel Rebar 16mm Diameter (12m length) - High Tensile Y16')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="16">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Small Cardboard Box 300x200x150mm - Single Wall</strong>
                                                <br><small class="text-muted">SKU: BOX-CARD-SMALL-SW-ZA</small>
                                                                                                    <br><small class="text-muted">Small single wall cardboard box for e-commerce and small ite...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/11" 
                                               class="text-decoration-none">
                                                Cardboard Boxes
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R45.00</span>
                                            <br><strong class="text-success">R38.00</strong>
                                            <br><small class="badge bg-danger">16% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">10000</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/16/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/16" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/16/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(16, 'Small Cardboard Box 300x200x150mm - Single Wall')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="17">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Medium Cardboard Box 400x300x250mm - Double Wall</strong>
                                                <br><small class="text-muted">SKU: BOX-CARD-MEDIUM-DW-ZA</small>
                                                                                                    <br><small class="text-muted">Medium double wall cardboard box for general shipping and st...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/11" 
                                               class="text-decoration-none">
                                                Cardboard Boxes
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R85.00</span>
                                            <br><strong class="text-success">R75.00</strong>
                                            <br><small class="badge bg-danger">12% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">5000</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/17/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/17" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/17/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(17, 'Medium Cardboard Box 400x300x250mm - Double Wall')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="18">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Large Cardboard Box 600x400x400mm - Triple Wall</strong>
                                                <br><small class="text-muted">SKU: BOX-CARD-LARGE-TW-ZA</small>
                                                                                                    <br><small class="text-muted">Large triple wall cardboard box for heavy items. 30kg capaci...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/11" 
                                               class="text-decoration-none">
                                                Cardboard Boxes
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R165.00</span>
                                            <br><strong class="text-success">R148.00</strong>
                                            <br><small class="badge bg-danger">10% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">2000</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/18/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/18" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/18/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(18, 'Large Cardboard Box 600x400x400mm - Triple Wall')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="19">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Export Wooden Crate 1200x800x600mm - ISPM-15 Certified</strong>
                                                <br><small class="text-muted">SKU: BOX-WOOD-EXPORT-1200-ZA</small>
                                                                                                    <br><small class="text-muted">ISPM-15 certified wooden export crate for international ship...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/12" 
                                               class="text-decoration-none">
                                                Wooden Crates &amp; Pallets
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R1,850.00</span>
                                            <br><strong class="text-success">R1,650.00</strong>
                                            <br><small class="badge bg-danger">11% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">150</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/19/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/19" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/19/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(19, 'Export Wooden Crate 1200x800x600mm - ISPM-15 Certified')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="20">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Heavy Duty Wooden Pallet 1200x1000mm - Euro Standard</strong>
                                                <br><small class="text-muted">SKU: BOX-PALLET-EURO-1200-ZA</small>
                                                                                                    <br><small class="text-muted">Euro standard wooden pallet 1200x1000mm. Four-way entry, hea...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/12" 
                                               class="text-decoration-none">
                                                Wooden Crates &amp; Pallets
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R285.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">500</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/20/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/20" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/20/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(20, 'Heavy Duty Wooden Pallet 1200x1000mm - Euro Standard')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="21">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Stackable Plastic Storage Container 60L with Lid</strong>
                                                <br><small class="text-muted">SKU: BOX-PLASTIC-60L-STACK-ZA</small>
                                                                                                    <br><small class="text-muted">Stackable 60L plastic container with lid. Food-grade HDPE, w...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/13" 
                                               class="text-decoration-none">
                                                Plastic Containers &amp; Tubs
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R485.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">800</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/21/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/21" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/21/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(21, 'Stackable Plastic Storage Container 60L with Lid')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="22">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Bubble Wrap Roll 1500mm x 100m - Large Bubbles</strong>
                                                <br><small class="text-muted">SKU: MAT-BUBBLE-1500-100-ZA</small>
                                                                                                    <br><small class="text-muted">Bubble wrap roll 1500mm x 100m with large bubbles. Maximum p...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/14" 
                                               class="text-decoration-none">
                                                Packaging Materials &amp; Supplies
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R650.00</span>
                                            <br><strong class="text-success">R585.00</strong>
                                            <br><small class="badge bg-danger">10% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">200</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/22/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/22" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/22/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(22, 'Bubble Wrap Roll 1500mm x 100m - Large Bubbles')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="23">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Heavy Duty Packing Tape 48mm x 100m - Clear</strong>
                                                <br><small class="text-muted">SKU: MAT-TAPE-48-100-CLEAR-ZA</small>
                                                                                                    <br><small class="text-muted">Heavy-duty clear packing tape 48mm x 100m. Strong adhesive f...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/14" 
                                               class="text-decoration-none">
                                                Packaging Materials &amp; Supplies
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R85.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">1000</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/23/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/23" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/23/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(23, 'Heavy Duty Packing Tape 48mm x 100m - Clear')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="8">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Container Door Seal Kit - Complete EPDM Rubber</strong>
                                                <br><small class="text-muted">SKU: CPART-SEAL-KIT-EPDM-ZA</small>
                                                                                                    <br><small class="text-muted">Complete EPDM rubber door seal kit for shipping containers....</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/7" 
                                               class="text-decoration-none">
                                                Container Parts &amp; Hardware
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R1,850.00</span>
                                            <br><strong class="text-success">R1,650.00</strong>
                                            <br><small class="badge bg-danger">11% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">250</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/8/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/8" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/8/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(8, 'Container Door Seal Kit - Complete EPDM Rubber')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="9">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Heavy Duty Container Door Lock Set - Marine Grade</strong>
                                                <br><small class="text-muted">SKU: CPART-LOCK-MARINE-ZA</small>
                                                                                                    <br><small class="text-muted">Marine-grade container door lock set with cam lock operation...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/7" 
                                               class="text-decoration-none">
                                                Container Parts &amp; Hardware
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R2,850.00</span>
                                            <br><strong class="text-success">R2,650.00</strong>
                                            <br><small class="badge bg-danger">7% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">120</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/9/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/9" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/9/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(9, 'Heavy Duty Container Door Lock Set - Marine Grade')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="10">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>ISO Corner Casting - Lloyd&#039;s Certified 45 Ton</strong>
                                                <br><small class="text-muted">SKU: CPART-CORNER-ISO-45T-ZA</small>
                                                                                                    <br><small class="text-muted">ISO 1161 corner casting, Lloyd&#039;s certified, 45-ton capacity....</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/7" 
                                               class="text-decoration-none">
                                                Container Parts &amp; Hardware
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R950.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">400</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/10/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/10" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/10/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(10, 'ISO Corner Casting - Lloyd&#039;s Certified 45 Ton')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="11">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Container Hinge Pin Set - Stainless Steel 316</strong>
                                                <br><small class="text-muted">SKU: CPART-HINGE-SS316-ZA</small>
                                                                                                    <br><small class="text-muted">Stainless steel 316 hinge pin set. Marine-grade corrosion re...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/7" 
                                               class="text-decoration-none">
                                                Container Parts &amp; Hardware
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <strong>R750.00</strong>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">500</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/11/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/11" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/11/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(11, 'Container Hinge Pin Set - Stainless Steel 316')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                            <tr>
                                    <td class="bulk-checkbox d-none">
                                        <input type="checkbox" class="form-check-input product-checkbox" 
                                               value="12">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                                                                            <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-box text-muted"></i>
                                                </div>
                                                                                        
                                            <div>
                                                <strong>Heavy Duty Truck Brake Pad Set - Ceramic Composite</strong>
                                                <br><small class="text-muted">SKU: TPART-BRAKE-CERAMIC-ZA</small>
                                                                                                    <br><small class="text-muted">Ceramic composite brake pad set for heavy-duty trucks. Excel...</small>
                                                                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                                                                    <a href="http://localhost:8000/admin/ecommerce/categories/8" 
                                               class="text-decoration-none">
                                                Truck &amp; Trailer Parts
                                            </a>
                                                                            </td>
                                    <td>
                                                                                    <span class="text-decoration-line-through text-muted small">R3,850.00</span>
                                            <br><strong class="text-success">R3,550.00</strong>
                                            <br><small class="badge bg-danger">8% OFF</small>
                                                                            </td>
                                    <td>
                                                                                                                                    <span class="badge bg-success">80</span>
                                                                                                                        </td>
                                    <td>
                                        <form method="POST" action="http://localhost:8000/admin/ecommerce/products/12/toggle-status" class="d-inline">
                                            <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                                            <button type="submit" class="btn btn-sm btn-link p-0">
                                                                                                    <span class="badge bg-success">Active</span>
                                                                                            </button>
                                        </form>
                                                                                    <span class="badge bg-warning ms-1">Featured</span>
                                                                            </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="http://localhost:8000/admin/ecommerce/products/12" 
                                               class="btn btn-sm btn-outline-info" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="http://localhost:8000/admin/ecommerce/products/12/edit" 
                                               class="btn btn-sm btn-outline-primary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteProduct(12, 'Heavy Duty Truck Brake Pad Set - Ceramic Composite')" 
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                                    </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    <nav role="navigation" aria-label="Pagination Navigation" class="flex items-center justify-between">
        <div class="flex justify-between flex-1 sm:hidden">
                            <span class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md dark:text-gray-600 dark:bg-gray-800 dark:border-gray-600">
                    &laquo; Previous
                </span>
            
                            <a href="http://localhost:8000/admin/ecommerce/products?page=2" class="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:focus:border-blue-700 dark:active:bg-gray-700 dark:active:text-gray-300">
                    Next &raquo;
                </a>
                    </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700 leading-5 dark:text-gray-400">
                    Showing
                                            <span class="font-medium">1</span>
                        to
                        <span class="font-medium">20</span>
                                        of
                    <span class="font-medium">30</span>
                    results
                </p>
            </div>

            <div>
                <span class="relative z-0 inline-flex rtl:flex-row-reverse shadow-sm rounded-md">
                    
                                            <span aria-disabled="true" aria-label="&amp;laquo; Previous">
                            <span class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5 dark:bg-gray-800 dark:border-gray-600" aria-hidden="true">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        </span>
                    
                    
                                            
                        
                        
                                                                                                                        <span aria-current="page">
                                        <span class="relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 dark:bg-gray-800 dark:border-gray-600">1</span>
                                    </span>
                                                                                                                                <a href="http://localhost:8000/admin/ecommerce/products?page=2" class="relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-400 dark:hover:text-gray-300 dark:active:bg-gray-700 dark:focus:border-blue-800" aria-label="Go to page 2">
                                        2
                                    </a>
                                                                                                        
                    
                                            <a href="http://localhost:8000/admin/ecommerce/products?page=2" rel="next" class="relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:ring ring-gray-300 focus:border-blue-300 active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150 dark:bg-gray-800 dark:border-gray-600 dark:active:bg-gray-700 dark:focus:border-blue-800" aria-label="Next &amp;raquo;">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                                    </span>
            </div>
        </div>
    </nav>

                </div>
                    </div>
    </div>

    <!-- Delete Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Delete Product
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the product <strong id="deleteProductName"></strong>?</p>
                    <p class="text-muted">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <form method="POST" id="deleteForm" class="d-inline">
                        <input type="hidden" name="_token" value="XRExNBNAvwjINxZ09WPPOlJAjJXaDA9dhFSkve40" autocomplete="off">                        <input type="hidden" name="_method" value="DELETE">                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>
                            Delete Product
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Currency Helper Script -->
    <script>
        // Global currency settings
        window.currencySettings = {
            symbol: 'R',
            code: 'Rands',
            position: 'before',
            decimalPlaces: 2,
            thousandsSeparator: ',',
            decimalSeparator: '.'
        };

        // Currency formatting function
        function formatCurrency(amount, showSymbol = true) {
            if (amount === null || amount === undefined || amount === '') {
                amount = 0;
            }

            amount = parseFloat(amount);

            // Format the number
            const formattedAmount = amount.toLocaleString('en-US', {
                minimumFractionDigits: window.currencySettings.decimalPlaces,
                maximumFractionDigits: window.currencySettings.decimalPlaces
            });

            if (!showSymbol) {
                return formattedAmount;
            }

            // Add currency symbol
            if (window.currencySettings.position === 'before') {
                return window.currencySettings.symbol + formattedAmount;
            } else {
                return formattedAmount + ' ' + window.currencySettings.symbol;
            }
        }

        // Parse currency string to float
        function parseCurrency(currencyString) {
            if (!currencyString) return 0;

            // Remove currency symbol and spaces
            let cleaned = currencyString.toString()
                .replace(window.currencySettings.symbol, '')
                .replace(/\s/g, '');

            // Replace thousands separator
            if (window.currencySettings.thousandsSeparator !== ',') {
                cleaned = cleaned.replace(new RegExp('\\' + window.currencySettings.thousandsSeparator, 'g'), '');
            } else {
                cleaned = cleaned.replace(/,/g, '');
            }

            // Replace decimal separator with dot
            if (window.currencySettings.decimalSeparator !== '.') {
                cleaned = cleaned.replace(window.currencySettings.decimalSeparator, '.');
            }

            return parseFloat(cleaned) || 0;
        }

        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const toggleIcon = document.getElementById('toggleIcon');

            // Prevent horizontal scroll
            function preventHorizontalScroll() {
                document.body.style.overflowX = 'hidden';
                document.documentElement.style.overflowX = 'hidden';
            }

            // Check if sidebar should be open by default on larger screens
            function checkScreenSize() {
                preventHorizontalScroll();

                if (window.innerWidth >= 992) {
                    // Desktop: auto-open sidebar
                    sidebar.classList.add('show');
                    mainContent.classList.add('sidebar-open');
                    sidebarToggle.classList.add('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-left';
                } else {
                    // Mobile/Tablet: auto-close sidebar
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-open');
                    sidebarToggle.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-right';
                }
            }

            // Initial setup
            preventHorizontalScroll();
            checkScreenSize();

            // Check on window resize with debounce
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(checkScreenSize, 100);
            });

            // Toggle sidebar
            sidebarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const isOpen = sidebar.classList.contains('show');

                if (isOpen) {
                    // Close sidebar
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-open');
                    sidebarToggle.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-right';
                } else {
                    // Open sidebar
                    sidebar.classList.add('show');
                    sidebarToggle.classList.add('sidebar-open');
                    toggleIcon.className = 'fas fa-chevron-left';

                    // Only adjust main content and show overlay based on screen size
                    if (window.innerWidth >= 992) {
                        mainContent.classList.add('sidebar-open');
                        sidebarOverlay.classList.remove('show');
                    } else {
                        mainContent.classList.remove('sidebar-open');
                        sidebarOverlay.classList.add('show');
                    }
                }
            });

            // Close sidebar when clicking overlay (mobile)
            sidebarOverlay.addEventListener('click', function() {
                sidebar.classList.remove('show');
                mainContent.classList.remove('sidebar-open');
                sidebarToggle.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('show');
                toggleIcon.className = 'fas fa-chevron-right';
            });

            // Close sidebar on escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                    mainContent.classList.remove('sidebar-open');
                    sidebarToggle.classList.remove('sidebar-open');
                    sidebarOverlay.classList.remove('show');
                    toggleIcon.className = 'fas fa-chevron-right';
                }
            });

            // Prevent body scroll when sidebar is open on mobile
            function updateBodyScroll() {
                if (window.innerWidth < 992 && sidebar.classList.contains('show')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            }

            // Update body scroll on sidebar toggle
            const observer = new MutationObserver(updateBodyScroll);
            observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });

            // Live Chat Notification System
            function updateLiveChatNotifications() {
                fetch('http://localhost:8000/admin/communications/live-chat/stats')
                    .then(response => response.json())
                    .then(data => {
                        const badge = document.getElementById('liveChatBadge');
                        const notificationsContainer = document.getElementById('liveChatNotifications');

                        if (data.total_unread > 0) {
                            badge.textContent = data.total_unread;
                            badge.style.display = 'block';

                            // Update notifications dropdown
                            let notificationsHtml = '';
                            data.recent_sessions.forEach(session => {
                                if (session.unread_count > 0) {
                                    notificationsHtml += `
                                        <li>
                                            <a class="dropdown-item" href="/admin/communications/live-chat/sessions/${session.id}">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <strong>${session.visitor_name}</strong>
                                                        <br>
                                                        <small class="text-muted">${session.latest_message ? session.latest_message.substring(0, 50) + '...' : 'New chat'}</small>
                                                        <br>
                                                        <small class="text-muted">${session.last_activity}</small>
                                                    </div>
                                                    <span class="badge bg-danger">${session.unread_count}</span>
                                                </div>
                                            </a>
                                        </li>
                                    `;
                                }
                            });

                            if (notificationsHtml) {
                                notificationsContainer.innerHTML = notificationsHtml;
                            }
                        } else {
                            badge.style.display = 'none';
                            notificationsContainer.innerHTML = `
                                <li class="dropdown-item text-center text-muted py-3">
                                    <i class="fas fa-comments fa-2x mb-2"></i><br>
                                    No new chat messages
                                </li>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error updating live chat notifications:', error);
                    });
            }

            // Update live chat notifications every 10 seconds
            updateLiveChatNotifications();
            setInterval(updateLiveChatNotifications, 10000);
        });
    </script>

    <script>
    // Delete product
    function deleteProduct(productId, productName) {
        document.getElementById('deleteProductName').textContent = productName;
        document.getElementById('deleteForm').action = `/admin/ecommerce/products/${productId}`;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // Bulk actions
    function toggleBulkMode() {
        const bulkCheckboxes = document.querySelectorAll('.bulk-checkbox');
        const bulkActions = document.getElementById('bulk-actions');
        
        bulkCheckboxes.forEach(checkbox => {
            checkbox.classList.toggle('d-none');
        });
        
        bulkActions.classList.toggle('d-none');
        
        // Clear all checkboxes
        document.querySelectorAll('.product-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('select-all').checked = false;
    }

    // Select all functionality
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkProducts();
    });

    // Update bulk products input
    function updateBulkProducts() {
        const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
        const productIds = Array.from(checkedBoxes).map(cb => cb.value);
        document.getElementById('bulk-products').value = JSON.stringify(productIds);
    }

    // Add event listeners to product checkboxes
    document.querySelectorAll('.product-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkProducts);
    });
</script>
</body>
</html>
