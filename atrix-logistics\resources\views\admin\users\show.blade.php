@extends('layouts.admin')

@section('title', 'User Details - ' . $user->name)

@section('content')
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0 text-gray-800">User Details</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">Users</a></li>
                        <li class="breadcrumb-item active">{{ $user->name }}</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i> Edit User
                </a>
                <a href="{{ route('admin.users.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Users
                </a>
            </div>
        </div>

        <div class="row">
            <!-- User Information -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">User Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td>{{ $user->name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>{{ $user->email }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>{{ $user->phone ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Role:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : ($user->role === 'staff' ? 'warning' : 'primary') }}">
                                                {{ ucfirst($user->role) }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                                {{ $user->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Company:</strong></td>
                                        <td>{{ $user->company_name ?? 'Not provided' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Joined:</strong></td>
                                        <td>{{ $user->created_at->format('M d, Y') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Updated:</strong></td>
                                        <td>{{ $user->updated_at->format('M d, Y H:i') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Last Login:</strong></td>
                                        <td>{{ $user->last_login_at ? $user->last_login_at->format('M d, Y H:i') : 'Never' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        @if($user->address || $user->city || $user->state || $user->country)
                            <hr>
                            <h6 class="text-primary mb-3">Address Information</h6>
                            <div class="row">
                                <div class="col-12">
                                    <p class="mb-1">{{ $user->address }}</p>
                                    <p class="mb-0">
                                        {{ $user->city }}{{ $user->city && $user->state ? ', ' : '' }}{{ $user->state }} {{ $user->postal_code }}
                                        @if($user->country)
                                            <br>{{ $user->country }}
                                        @endif
                                    </p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Activity Statistics -->
                @if($user->role === 'customer')
                    <div class="card shadow">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Activity Statistics</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-1">{{ number_format($userStats['total_orders']) }}</h4>
                                        <p class="text-muted mb-0">Total Orders</p>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="border-end">
                                        <h4 class="text-success mb-1">@currency($userStats['total_spent'])</h4>
                                        <p class="text-muted mb-0">Total Spent</p>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <div class="border-end">
                                        <h4 class="text-info mb-1">{{ number_format($userStats['total_parcels']) }}</h4>
                                        <p class="text-muted mb-0">Total Parcels</p>
                                    </div>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h4 class="text-warning mb-1">
                                        {{ $userStats['last_login'] ? $userStats['last_login']->diffForHumans() : 'Never' }}
                                    </h4>
                                    <p class="text-muted mb-0">Last Login</p>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                                <i class="fas fa-edit me-2"></i> Edit User
                            </a>
                            
                            @if($user->id !== auth()->id())
                                <button class="btn btn-warning" onclick="resetPassword()">
                                    <i class="fas fa-key me-2"></i> Reset Password
                                </button>
                                
                                <button class="btn btn-{{ $user->is_active ? 'secondary' : 'success' }}" onclick="toggleStatus()">
                                    <i class="fas fa-{{ $user->is_active ? 'ban' : 'check' }} me-2"></i>
                                    {{ $user->is_active ? 'Deactivate' : 'Activate' }} User
                                </button>
                                
                                @if($user->orders()->count() === 0 && $user->parcels()->count() === 0)
                                    <button class="btn btn-danger" onclick="deleteUser()">
                                        <i class="fas fa-trash me-2"></i> Delete User
                                    </button>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>

                <!-- User Statistics -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Account Summary</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Account Age:</span>
                                <strong>{{ $user->created_at->diffForHumans() }}</strong>
                            </div>
                        </div>
                        
                        @if($user->role === 'customer')
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Orders:</span>
                                    <strong>{{ number_format($userStats['total_orders']) }}</strong>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Parcels:</span>
                                    <strong>{{ number_format($userStats['total_parcels']) }}</strong>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Total Spent:</span>
                                    <strong>@currency($userStats['total_spent'])</strong>
                                </div>
                            </div>
                        @endif
                        
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Status:</span>
                                <span class="badge bg-{{ $user->is_active ? 'success' : 'secondary' }}">
                                    {{ $user->is_active ? 'Active' : 'Inactive' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reset Password for {{ $user->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="resetPasswordForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" name="password_confirmation" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    function resetPassword() {
        const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
        modal.show();
    }

    function toggleStatus() {
        if (!confirm('Are you sure you want to {{ $user->is_active ? "deactivate" : "activate" }} this user?')) {
            return;
        }
        
        fetch('{{ route("admin.users.toggle-status", $user) }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        });
    }

    function deleteUser() {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }
        
        fetch('{{ route("admin.users.destroy", $user) }}', {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            }
        })
        .then(response => {
            if (response.ok) {
                window.location.href = '{{ route("admin.users.index") }}';
            } else {
                alert('Error deleting user');
            }
        });
    }

    // Reset password form submission
    document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const password = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        if (password !== confirmPassword) {
            alert('Passwords do not match');
            return;
        }
        
        fetch('{{ route("admin.users.reset-password", $user) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                password: password,
                password_confirmation: confirmPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                const modal = bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal'));
                modal.hide();
                this.reset();
            } else {
                alert('Error: ' + data.message);
            }
        });
    });
</script>
@endpush
