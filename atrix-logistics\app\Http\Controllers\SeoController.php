<?php

namespace App\Http\Controllers;

use App\Models\SiteSetting;
use App\Services\SeoLocalizationService;
use Illuminate\Http\Response;

class SeoController extends Controller
{
    protected $seoService;

    public function __construct(SeoLocalizationService $seoService)
    {
        $this->seoService = $seoService;
    }

    /**
     * Generate robots.txt file
     */
    public function robots()
    {
        $content = $this->generateRobotsTxt();

        return response($content, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400', // 24 hours
        ]);
    }

    /**
     * Generate robots.txt content
     */
    protected function generateRobotsTxt()
    {
        $lines = [];
        
        // User-agent directive
        $lines[] = 'User-agent: *';
        
        // Allow/Disallow directives
        $lines[] = 'Allow: /';
        $lines[] = 'Disallow: /admin/';
        $lines[] = 'Disallow: /customer/login';
        $lines[] = 'Disallow: /customer/register';
        $lines[] = 'Disallow: /api/';
        $lines[] = 'Disallow: /storage/';
        $lines[] = 'Disallow: /vendor/';
        $lines[] = 'Disallow: /*.json$';
        $lines[] = 'Disallow: /*?*';
        $lines[] = '';
        
        // Sitemap directive
        $lines[] = 'Sitemap: ' . route('sitemap.index');
        
        // Add localized sitemaps if enabled
        if (SiteSetting::getValue('generate_localized_sitemaps', true)) {
            $supportedLocales = $this->seoService->getSupportedLocales();
            
            foreach ($supportedLocales as $locale => $config) {
                if ($config['enabled']) {
                    $lines[] = 'Sitemap: ' . route('sitemap.locale', ['locale' => $locale]);
                }
            }
        }
        
        $lines[] = '';
        
        // Crawl-delay (optional)
        $crawlDelay = SiteSetting::getValue('robots_crawl_delay', null);
        if ($crawlDelay) {
            $lines[] = 'Crawl-delay: ' . $crawlDelay;
            $lines[] = '';
        }
        
        // Custom robots.txt content
        $customContent = SiteSetting::getValue('robots_custom_content', '');
        if ($customContent) {
            $lines[] = $customContent;
            $lines[] = '';
        }
        
        return implode("\n", $lines);
    }
}
